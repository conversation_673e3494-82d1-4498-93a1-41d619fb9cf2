/* Modern Component Styles for Zaiqa Restaurant System */

/* Dashboard Specific Styles */
.dashboard-container {
    animation: fadeInUp 0.5s ease-out;
}

/* Metric Cards */
.metric-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 1) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Top Items List */
.top-items-list {
    max-height: 400px;
    overflow-y: auto;
}

.top-item {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 12px 16px;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.top-item:hover {
    background: #e2e8f0;
    transform: translateX(4px);
}

.top-item-rank {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #f97316 0%, #dc2626 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    margin-right: 12px;
}

/* Recent Orders */
.recent-orders-list {
    max-height: 400px;
    overflow-y: auto;
}

.recent-order-item {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    background: white;
}

.recent-order-item:hover {
    border-color: #f97316;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.1);
}

.order-icon {
    width: 40px;
    height: 40px;
    background: #dbeafe;
    color: #3b82f6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    gap: 12px;
}

.quick-action-btn {
    width: 100%;
    padding: 12px 16px;
    text-align: left;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    background: white;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.quick-action-btn:hover {
    border-color: #f97316;
    background: #fff7ed;
    transform: translateY(-1px);
}

.quick-action-btn i {
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

/* Alert Styles */
.alert {
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid;
    margin-bottom: 16px;
}

.alert-warning {
    background: #fef3c7;
    border-left-color: #f59e0b;
    color: #92400e;
}

.alert-error {
    background: #fee2e2;
    border-left-color: #ef4444;
    color: #991b1b;
}

.alert-success {
    background: #d1fae5;
    border-left-color: #10b981;
    color: #065f46;
}

.alert-info {
    background: #dbeafe;
    border-left-color: #3b82f6;
    color: #1e40af;
}

/* Low Stock Items */
.low-stock-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 12px;
}

.low-stock-item {
    background: #fef3c7;
    border: 1px solid #fbbf24;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
}

.low-stock-item h5 {
    font-weight: 600;
    color: #92400e;
    margin-bottom: 4px;
}

.low-stock-item p {
    color: #b45309;
    font-size: 14px;
}

/* Form Enhancements */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #f97316;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.form-control:invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control:disabled {
    background: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

/* Input Groups */
.input-group {
    display: flex;
    align-items: center;
}

.input-group .form-control {
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.input-group .form-control:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

.input-group-text {
    padding: 12px 16px;
    background: #f9fafb;
    border: 1px solid #d1d5db;
    color: #6b7280;
    font-size: 14px;
    white-space: nowrap;
}

/* Table Enhancements */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: #f8fafc;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e5e7eb;
}

.table td {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    color: #1f2937;
}

.table tbody tr:hover {
    background: #f8fafc;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-cancelled {
    background: #fee2e2;
    color: #991b1b;
}

.status-active {
    background: #dbeafe;
    color: #1e40af;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-action {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.btn-action:hover {
    transform: translateY(-1px);
}

.btn-edit {
    background: #dbeafe;
    color: #1e40af;
}

.btn-edit:hover {
    background: #bfdbfe;
}

.btn-delete {
    background: #fee2e2;
    color: #991b1b;
}

.btn-delete:hover {
    background: #fecaca;
}

.btn-view {
    background: #f3f4f6;
    color: #374151;
}

.btn-view:hover {
    background: #e5e7eb;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #f97316;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #6b7280;
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    background: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #9ca3af;
}

.empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.empty-state p {
    font-size: 14px;
    margin-bottom: 24px;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 16px;
    }
    
    .metric-card {
        margin-bottom: 16px;
    }
    
    .chart-container {
        height: 250px;
        padding: 16px;
    }
    
    .table th,
    .table td {
        padding: 12px 8px;
        font-size: 14px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .btn-action {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .low-stock-grid {
        grid-template-columns: 1fr;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .input-group .form-control {
        border-radius: 8px;
        margin-bottom: 8px;
    }
    
    .input-group .form-control:last-child {
        margin-bottom: 0;
    }
}
