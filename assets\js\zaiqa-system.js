/**
 * Zaiqa Restaurant Management System - Main Integration
 * Clean Architecture with Accurate Financial Systems
 * Integrates all modules with the original frontend
 */

class ZaiqaRestaurantSystem {
    constructor() {
        this.version = '2.0.0';
        this.initialized = false;
        
        // Core system components
        this.financialEngine = null;
        this.databaseManager = null;
        this.financialCalculations = null;
        
        // Initialize the system
        this.init();
    }

    /**
     * Initialize the complete restaurant system
     */
    async init() {
        try {
            console.log('🏪 Initializing Zaiqa Restaurant System v' + this.version);
            
            // Step 1: Initialize Financial Engine
            console.log('💰 Initializing Financial Engine...');
            this.financialEngine = new ZaiqaFinancialEngine();
            
            // Step 2: Initialize Database Manager
            console.log('🗄️ Initializing Database Manager...');
            this.databaseManager = new ZaiqaDatabaseManager();
            
            // Step 3: Initialize Financial Calculations
            console.log('📊 Initializing Financial Calculations...');
            if (typeof ZaiqaFinancialCalculations !== 'undefined') {
                this.financialCalculations = new ZaiqaFinancialCalculations(
                    this.financialEngine,
                    this.databaseManager
                );
            } else {
                console.warn('⚠️ ZaiqaFinancialCalculations not available, using fallback');
                this.financialCalculations = this.createFallbackCalculations();
            }
            
            // Step 4: Initialize sample data if needed
            await this.initializeSampleData();
            
            // Step 5: Set up frontend integration
            this.setupFrontendIntegration();
            
            this.initialized = true;
            console.log('✅ Zaiqa Restaurant System initialized successfully');
            
            // Notify frontend that system is ready
            this.notifySystemReady();
            
        } catch (error) {
            console.error('❌ System initialization failed:', error);
            throw new Error('Failed to initialize Zaiqa Restaurant System: ' + error.message);
        }
    }

    /**
     * Initialize sample data for testing and demonstration - DISABLED
     */
    async initializeSampleData() {
        // COMPLETELY DISABLED - No sample data will be created to prevent phantom orders
        console.log('📊 Sample data initialization disabled - no test data will be created');
        return;
    }

    /**
     * Create fallback calculations when ZaiqaFinancialCalculations is not available
     */
    createFallbackCalculations() {
        return {
            calculateTodayMetrics: () => {
                try {
                    const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
                    const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                    const today = new Date().toISOString().split('T')[0];

                    const todayOrders = orders.filter(order => order.created_at?.startsWith(today));
                    const todayExpenses = expenses.filter(expense => expense.date === today);

                    const revenue = todayOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
                    const totalExpenses = todayExpenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);

                    return {
                        revenue: { total: revenue },
                        expenses: { total: totalExpenses },
                        profit: revenue - totalExpenses,
                        orderCount: todayOrders.length
                    };
                } catch (error) {
                    console.error('❌ Fallback calculations error:', error);
                    return { revenue: { total: 0 }, expenses: { total: 0 }, profit: 0, orderCount: 0 };
                }
            },
            calculateUdharSummary: () => {
                try {
                    const udhars = JSON.parse(localStorage.getItem('udhars') || '[]');
                    const totalAmount = udhars.reduce((sum, udhar) => sum + (udhar.totalAmount || 0), 0);
                    const remainingAmount = udhars.reduce((sum, udhar) => sum + (udhar.remainingAmount || 0), 0);

                    return {
                        totalCustomers: udhars.length,
                        totalAmount: totalAmount,
                        remainingAmount: remainingAmount,
                        paidAmount: totalAmount - remainingAmount
                    };
                } catch (error) {
                    console.error('❌ Fallback Udhar calculations error:', error);
                    return { totalCustomers: 0, totalAmount: 0, remainingAmount: 0, paidAmount: 0 };
                }
            },
            getStatus: () => ({ status: 'fallback', available: true })
        };
    }

    /**
     * Set up integration with the original frontend
     */
    setupFrontendIntegration() {
        try {
            console.log('🔗 Setting up frontend integration...');
            
            // Make system available globally for frontend access
            window.zaiqaSystem = this;
            
            // Create convenient access methods for the frontend
            window.getFinancialData = (type, ...args) => {
                return this.getFinancialData(type, ...args);
            };
            
            window.saveData = (table, data) => {
                return this.saveData(table, data);
            };
            
            window.getData = (table, filters) => {
                return this.getData(table, filters);
            };
            
            window.updateData = (table, id, updates) => {
                return this.updateData(table, id, updates);
            };
            
            window.deleteData = (table, id) => {
                return this.deleteData(table, id);
            };
            
            console.log('✅ Frontend integration setup complete');
            
        } catch (error) {
            console.error('❌ Frontend integration setup failed:', error);
        }
    }

    /**
     * Get financial data for frontend consumption
     */
    getFinancialData(type, ...args) {
        try {
            if (!this.financialCalculations) {
                console.warn(`⚠️ Financial calculations not available for type: ${type}`);
                return this.getFallbackFinancialData(type);
            }

            switch (type) {
                case 'todayMetrics':
                    return this.financialCalculations.calculateTodayMetrics();

                case 'periodMetrics':
                    return this.financialCalculations.calculatePeriodMetrics(args[0], args[1]);

                case 'revenue':
                    return this.financialCalculations.calculateRevenue(args[0], args[1]);

                case 'expenses':
                    return this.financialCalculations.calculateExpenses(args[0], args[1]);

                case 'profitLoss':
                    return this.financialCalculations.calculateProfitLoss(args[0], args[1]);

                case 'cashFlow':
                    return this.financialCalculations.calculateCashFlow(args[0]);

                case 'topItems':
                    return this.financialCalculations.calculateTopItems(args[0], args[1], args[2]);

                case 'udharSummary':
                    return this.financialCalculations.calculateUdharSummary();

                case 'inventoryValue':
                    return this.financialCalculations.calculateInventoryValue();

                default:
                    throw new Error(`Unknown financial data type: ${type}`);
            }

        } catch (error) {
            console.error(`❌ Failed to get financial data (${type}):`, error);
            return this.getFallbackFinancialData(type);
        }
    }

    /**
     * Get fallback financial data when calculations fail
     */
    getFallbackFinancialData(type) {
        const fallbackData = {
            todayMetrics: { revenue: { total: 0 }, expenses: { total: 0 }, profit: 0, orderCount: 0 },
            udharSummary: { totalCustomers: 0, totalAmount: 0, remainingAmount: 0, paidAmount: 0 },
            revenue: { total: 0 },
            expenses: { total: 0 },
            profitLoss: { profit: 0, loss: 0 },
            cashFlow: { inflow: 0, outflow: 0 },
            topItems: [],
            inventoryValue: { total: 0 }
        };

        return fallbackData[type] || null;
    }

    /**
     * Save data to database
     */
    saveData(table, data) {
        try {
            return this.databaseManager.create(table, data);
        } catch (error) {
            console.error(`❌ Failed to save data to ${table}:`, error);
            throw error;
        }
    }

    /**
     * Get data from database
     */
    getData(table, filters = {}) {
        try {
            return this.databaseManager.read(table, filters);
        } catch (error) {
            console.error(`❌ Failed to get data from ${table}:`, error);
            return [];
        }
    }

    /**
     * Update data in database
     */
    updateData(table, id, updates) {
        try {
            return this.databaseManager.update(table, id, updates);
        } catch (error) {
            console.error(`❌ Failed to update data in ${table}:`, error);
            throw error;
        }
    }

    /**
     * Delete data from database
     */
    deleteData(table, id) {
        try {
            return this.databaseManager.delete(table, id);
        } catch (error) {
            console.error(`❌ Failed to delete data from ${table}:`, error);
            throw error;
        }
    }

    /**
     * Notify frontend that system is ready
     */
    notifySystemReady() {
        try {
            // Dispatch custom event
            const event = new CustomEvent('zaiqaSystemReady', {
                detail: {
                    version: this.version,
                    timestamp: new Date().toISOString()
                }
            });
            
            document.dispatchEvent(event);
            
            // Also call global callback if it exists
            if (typeof window.onZaiqaSystemReady === 'function') {
                window.onZaiqaSystemReady(this);
            }
            
        } catch (error) {
            console.error('❌ Failed to notify system ready:', error);
        }
    }

    /**
     * Get system status and health check
     */
    getSystemStatus() {
        return {
            version: this.version,
            initialized: this.initialized,
            components: {
                financialEngine: this.financialEngine ? this.financialEngine.getStatus() : null,
                databaseManager: this.databaseManager ? this.databaseManager.getStatus() : null,
                financialCalculations: this.financialCalculations ? this.financialCalculations.getStatus() : null
            },
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Check if system is properly initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize the system when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('🚀 Starting Zaiqa Restaurant System...');
        window.zaiqaRestaurantSystem = new ZaiqaRestaurantSystem();
    } catch (error) {
        console.error('❌ Failed to start Zaiqa Restaurant System:', error);
    }
});

// Export for global use
window.ZaiqaRestaurantSystem = ZaiqaRestaurantSystem;
