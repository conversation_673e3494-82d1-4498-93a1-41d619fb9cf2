/**
 * Unified Cash System - Single Cash Modal with Complete Integration
 * Replaces all duplicate cash management systems with one comprehensive solution
 */

class ZaiqaUnifiedCashSystem {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize unified cash system
     */
    init() {
        try {
            console.log('💰 Initializing Unified Cash System v' + this.version);
            
            // Wait for app to be ready
            this.waitForApp().then(() => {
                this.setupCashSystem();
                this.initialized = true;
                console.log('✅ Unified Cash System initialized successfully');
            });
            
        } catch (error) {
            console.error('❌ Unified Cash System initialization failed:', error);
        }
    }

    /**
     * Wait for app to be ready
     */
    waitForApp() {
        return new Promise((resolve) => {
            const checkApp = () => {
                if (window.app && typeof window.app.showCashInHandModal === 'function') {
                    resolve();
                } else {
                    setTimeout(checkApp, 100);
                }
            };
            checkApp();
        });
    }

    /**
     * Setup unified cash system
     */
    setupCashSystem() {
        try {
            // Override the original cash modal function
            if (window.app && typeof window.app.showCashInHandModal === 'function') {
                window.app.originalShowCashInHandModal = window.app.showCashInHandModal;
                
                window.app.showCashInHandModal = () => {
                    // Remove any existing modals
                    const existingModals = document.querySelectorAll('.modal-overlay');
                    existingModals.forEach(modal => modal.remove());
                    
                    // Show unified cash modal
                    this.showUnifiedCashModal();
                };
            }

            // Disable all other cash modal functions
            this.disableOtherCashSystems();

            console.log('✅ Unified cash system setup completed');

        } catch (error) {
            console.error('❌ Failed to setup unified cash system:', error);
        }
    }

    /**
     * Disable other cash systems to prevent duplicates
     */
    disableOtherCashSystems() {
        try {
            // Disable cash manager
            if (window.cashManager) {
                window.cashManager.showEnhancedCashModal = () => {
                    window.app.showCashInHandModal();
                };
            }

            // Disable cash integration
            if (window.cashIntegration) {
                window.cashIntegration.showOriginalEnhancedCashModal = () => {
                    window.app.showCashInHandModal();
                };
            }

        } catch (error) {
            console.error('❌ Failed to disable other cash systems:', error);
        }
    }

    /**
     * Show unified cash modal with original design and enhanced functionality
     */
    showUnifiedCashModal() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            
            const modal = document.createElement('div');
            modal.className = 'modal-overlay unified-cash-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Cash in Hand</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="cash-display">
                            <h3>Current Cash: PKR ${currentCash.toLocaleString()}</h3>
                        </div>
                        
                        <div class="cash-sections">
                            <div class="cash-section">
                                <h4>💰 Inflow</h4>
                                <div class="cash-form">
                                    <input type="number" id="inflowAmount" placeholder="Amount" step="0.01" min="0">
                                    <input type="text" id="inflowDescription" placeholder="Description (e.g., Cash Receipt, Sales)">
                                    <button onclick="window.unifiedCashSystem.addCashReceipt()" class="btn btn-success">
                                        Add Inflow
                                    </button>
                                </div>
                                <small class="form-help">Cash receipts will be added to today's revenue</small>
                            </div>
                            
                            <div class="cash-section">
                                <h4>💸 Outflow</h4>
                                <div class="cash-form">
                                    <input type="number" id="outflowAmount" placeholder="Amount" step="0.01" min="0">
                                    <input type="text" id="outflowDescription" placeholder="Description (e.g., Expense, Payment)">
                                    <button onclick="window.unifiedCashSystem.addCashExpense()" class="btn btn-danger">
                                        Add Outflow
                                    </button>
                                </div>
                                <small class="form-help">Outflows will be added to expenses</small>
                            </div>
                        </div>
                        
                        <div class="auto-expense-section">
                            <h4>⚙️ Auto Expenses</h4>
                            <button onclick="window.unifiedCashSystem.showAutoExpenseModal()" class="btn btn-warning">
                                <i class="fas fa-cog"></i> Deduct Auto Expenses
                            </button>
                            <small class="form-help">Deduct configured auto expenses from current cash</small>
                        </div>
                        
                        <div class="recent-transactions">
                            <h4>Recent Transactions</h4>
                            <div id="cashTransactionsList" class="transactions-list">
                                <div class="loading">Loading...</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Close
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Load transactions after modal is displayed
            setTimeout(() => {
                this.loadCashTransactions();
            }, 100);

        } catch (error) {
            console.error('❌ Failed to show unified cash modal:', error);
        }
    }

    /**
     * Add cash receipt (revenue)
     */
    addCashReceipt() {
        try {
            const amount = parseFloat(document.getElementById('inflowAmount')?.value || 0);
            const description = document.getElementById('inflowDescription')?.value?.trim() || 'Cash Receipt';

            if (amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            // Process as revenue
            this.processCashInflow(amount, description);

            // Clear form
            document.getElementById('inflowAmount').value = '';
            document.getElementById('inflowDescription').value = '';

            // Show success message
            this.showNotification(`Cash receipt of PKR ${amount.toLocaleString()} added to revenue`, 'success');

            // Refresh modal
            this.refreshCashModal();

        } catch (error) {
            console.error('❌ Failed to add cash receipt:', error);
            alert('Failed to add cash receipt');
        }
    }

    /**
     * Add cash expense
     */
    addCashExpense() {
        try {
            const amount = parseFloat(document.getElementById('outflowAmount')?.value || 0);
            const description = document.getElementById('outflowDescription')?.value?.trim() || 'Cash Expense';

            if (amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            // Process as expense
            this.processCashOutflow(amount, description);

            // Clear form
            document.getElementById('outflowAmount').value = '';
            document.getElementById('outflowDescription').value = '';

            // Show success message
            this.showNotification(`Expense of PKR ${amount.toLocaleString()} added and deducted from cash`, 'success');

            // Refresh modal
            this.refreshCashModal();

        } catch (error) {
            console.error('❌ Failed to add cash expense:', error);
            alert('Failed to add cash expense');
        }
    }

    /**
     * Process cash inflow (revenue)
     */
    processCashInflow(amount, description) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            // 1. Update cash in hand
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const newCash = currentCash + amount;
            localStorage.setItem('currentCashInHand', newCash.toString());
            localStorage.setItem('lastCashUpdate', timestamp);

            // 2. Create revenue order entry
            const orderEntry = {
                id: 'cash_revenue_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                order_number: 'CASH-' + Date.now(),
                customer_name: 'Cash Customer',
                customer_count: 1,
                service_type: 'cash_receipt',
                items: [{ name: description, price: amount, quantity: 1 }],
                total_amount: amount,
                payment_method: 'cash',
                status: 'completed',
                created_at: timestamp,
                createdAt: timestamp,
                type: 'cash_receipt'
            };

            // Add to orders
            const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
            orders.push(orderEntry);
            localStorage.setItem('restaurantOrders', JSON.stringify(orders));

            // 3. Add cash transaction
            this.addCashTransaction('inflow', amount, description);

            // 4. Refresh all displays
            this.refreshAllDisplays();

            console.log(`✅ Cash inflow processed: PKR ${amount} - ${description}`);
            return true;

        } catch (error) {
            console.error('❌ Failed to process cash inflow:', error);
            return false;
        }
    }

    /**
     * Process cash outflow (expense)
     */
    processCashOutflow(amount, description) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            // 1. Update cash in hand
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const newCash = currentCash - amount;
            localStorage.setItem('currentCashInHand', newCash.toString());
            localStorage.setItem('lastCashUpdate', timestamp);

            // 2. Create expense entry
            const expenseEntry = {
                id: 'cash_expense_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                description: description,
                amount: amount,
                category: 'Manual Expenses',
                date: today,
                created_at: timestamp,
                type: 'cash_expense',
                source: 'cash_in_hand'
            };

            // Add to expenses
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            expenses.push(expenseEntry);
            localStorage.setItem('expenses', JSON.stringify(expenses));

            // 3. Add cash transaction
            this.addCashTransaction('outflow', amount, description);

            // 4. Refresh all displays
            this.refreshAllDisplays();

            console.log(`✅ Cash outflow processed: PKR ${amount} - ${description} (added to expenses)`);
            return true;

        } catch (error) {
            console.error('❌ Failed to process cash outflow:', error);
            return false;
        }
    }

    /**
     * Add cash transaction record
     */
    addCashTransaction(type, amount, description) {
        try {
            const transaction = {
                id: 'trans_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                type: type,
                amount: type === 'outflow' ? -amount : amount,
                description: description,
                timestamp: new Date().toISOString(),
                date: new Date().toISOString().split('T')[0]
            };

            const transactions = JSON.parse(localStorage.getItem('cashTransactions') || '[]');
            transactions.unshift(transaction);

            // Keep only last 100 transactions
            if (transactions.length > 100) {
                transactions.splice(100);
            }

            localStorage.setItem('cashTransactions', JSON.stringify(transactions));

        } catch (error) {
            console.error('❌ Failed to add cash transaction:', error);
        }
    }

    /**
     * Load and display cash transactions
     */
    loadCashTransactions() {
        try {
            const transactionsList = document.getElementById('cashTransactionsList');
            if (!transactionsList) return;

            const transactions = JSON.parse(localStorage.getItem('cashTransactions') || '[]');
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');

            // Combine cash transactions and expenses for complete history
            const allTransactions = [];

            // Add cash transactions
            transactions.forEach(trans => {
                allTransactions.push({
                    ...trans,
                    source: 'cash_modal'
                });
            });

            // Add expenses from expense page
            expenses.forEach(expense => {
                if (expense.source !== 'cash_in_hand') { // Avoid duplicates
                    allTransactions.push({
                        id: expense.id,
                        type: 'outflow',
                        amount: -expense.amount,
                        description: expense.description,
                        timestamp: expense.created_at,
                        date: expense.date,
                        source: 'expense_page',
                        category: expense.category
                    });
                }
            });

            // Sort by timestamp (newest first)
            allTransactions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // Display transactions
            if (allTransactions.length === 0) {
                transactionsList.innerHTML = '<p class="no-transactions">No transactions found</p>';
                return;
            }

            const transactionsHTML = allTransactions.slice(0, 20).map(transaction => {
                const date = new Date(transaction.timestamp);
                const timeStr = date.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });
                const dateStr = date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric'
                });

                return `
                    <div class="transaction-item ${transaction.type}">
                        <div class="transaction-info">
                            <div class="transaction-description">${transaction.description}</div>
                            <div class="transaction-meta">
                                <span class="transaction-time">${timeStr}, ${dateStr}</span>
                                ${transaction.category ? `<span class="transaction-category">${transaction.category}</span>` : ''}
                                <span class="transaction-source">${transaction.source === 'expense_page' ? 'Expense Page' : 'Cash Modal'}</span>
                            </div>
                        </div>
                        <div class="transaction-amount ${transaction.amount >= 0 ? 'positive' : 'negative'}">
                            ${transaction.amount >= 0 ? '+' : ''}PKR ${Math.abs(transaction.amount).toLocaleString()}
                        </div>
                    </div>
                `;
            }).join('');

            transactionsList.innerHTML = transactionsHTML;

        } catch (error) {
            console.error('❌ Failed to load cash transactions:', error);
            const transactionsList = document.getElementById('cashTransactionsList');
            if (transactionsList) {
                transactionsList.innerHTML = '<p class="error">Error loading transactions</p>';
            }
        }
    }

    /**
     * Show auto expense deduction modal
     */
    showAutoExpenseModal() {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const enabledExpenses = config.autoExpenses ?
                config.autoExpenses.filter(exp => exp.enabled && exp.amount > 0) : [];

            if (enabledExpenses.length === 0) {
                alert('No auto expenses configured. Please configure auto expenses in the Expense page first.');
                return;
            }

            const totalAmount = enabledExpenses.reduce((sum, exp) => sum + exp.amount, 0);
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');

            const modal = document.createElement('div');
            modal.className = 'modal-overlay auto-expense-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-cog"></i> Deduct Auto Expenses</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>

                    <div class="modal-body">
                        <div class="cash-status">
                            <h3>Current Cash: PKR ${currentCash.toLocaleString()}</h3>
                            <h3>Total Auto Expenses: PKR ${totalAmount.toLocaleString()}</h3>
                            <h3 class="${currentCash >= totalAmount ? 'sufficient' : 'insufficient'}">
                                ${currentCash >= totalAmount ? 'Sufficient funds available' : 'Insufficient funds!'}
                            </h3>
                        </div>

                        <div class="auto-expenses-list">
                            <h4>Auto Expenses to be deducted:</h4>
                            ${enabledExpenses.map(exp => `
                                <div class="expense-item">
                                    <span class="expense-name">${exp.name} (${exp.nameUrdu})</span>
                                    <span class="expense-amount">PKR ${exp.amount.toLocaleString()}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button onclick="window.unifiedCashSystem.processAutoExpenseDeduction()"
                                class="btn btn-warning"
                                ${currentCash < totalAmount ? 'disabled' : ''}>
                            <i class="fas fa-cog"></i>
                            Deduct PKR ${totalAmount.toLocaleString()}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

        } catch (error) {
            console.error('❌ Failed to show auto expense modal:', error);
        }
    }

    /**
     * Process auto expense deduction
     */
    processAutoExpenseDeduction() {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const enabledExpenses = config.autoExpenses ?
                config.autoExpenses.filter(exp => exp.enabled && exp.amount > 0) : [];

            if (enabledExpenses.length === 0) return;

            const totalAmount = enabledExpenses.reduce((sum, exp) => sum + exp.amount, 0);
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');

            if (currentCash < totalAmount) {
                alert('Insufficient cash for auto expense deduction');
                return;
            }

            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            // Process each auto expense
            enabledExpenses.forEach(expense => {
                const expenseEntry = {
                    id: 'auto_exp_' + expense.id + '_' + Date.now(),
                    description: `${expense.name} (Auto Deduction from Cash)`,
                    amount: expense.amount,
                    category: 'Auto Expenses',
                    date: today,
                    created_at: timestamp,
                    type: 'auto_expense_deduction',
                    source: 'cash_in_hand'
                };

                // Add to expenses
                const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                expenses.push(expenseEntry);
                localStorage.setItem('expenses', JSON.stringify(expenses));
            });

            // Deduct from cash
            const newCash = currentCash - totalAmount;
            localStorage.setItem('currentCashInHand', newCash.toString());

            // Add cash transaction
            this.addCashTransaction('outflow', totalAmount, 'Auto Expenses Deduction');

            // Close modal
            document.querySelector('.auto-expense-modal')?.remove();

            // Show success message
            this.showNotification(`Auto expenses of PKR ${totalAmount.toLocaleString()} deducted successfully`, 'success');

            // Refresh displays
            this.refreshCashModal();
            this.refreshAllDisplays();

        } catch (error) {
            console.error('❌ Failed to process auto expense deduction:', error);
            alert('Failed to process auto expense deduction');
        }
    }

    /**
     * Refresh cash modal display
     */
    refreshCashModal() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const cashDisplayElement = document.querySelector('.unified-cash-modal .cash-display h3');

            if (cashDisplayElement) {
                cashDisplayElement.textContent = `Current Cash: PKR ${currentCash.toLocaleString()}`;
            }

            // Reload transactions
            this.loadCashTransactions();

        } catch (error) {
            console.error('❌ Failed to refresh cash modal:', error);
        }
    }

    /**
     * Refresh all financial displays
     */
    refreshAllDisplays() {
        try {
            // Refresh dashboard
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                setTimeout(() => {
                    window.app.updateDashboardStats();
                }, 100);
            }

            // Refresh reports page if visible
            const reportsPage = document.getElementById('reportsPage');
            if (reportsPage && reportsPage.style.display !== 'none') {
                if (window.zaiqaReports && typeof window.zaiqaReports.render === 'function') {
                    setTimeout(() => {
                        window.zaiqaReports.render(reportsPage);
                    }, 200);
                }
            }

            // Refresh expense page if visible
            const expensePage = document.getElementById('expensePage');
            if (expensePage && expensePage.style.display !== 'none') {
                if (window.app && typeof window.app.loadExpensesPage === 'function') {
                    setTimeout(() => {
                        window.app.loadExpensesPage();
                    }, 300);
                }
            }

        } catch (error) {
            console.error('❌ Failed to refresh all displays:', error);
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        try {
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(message, type);
            } else {
                // Fallback notification
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.textContent = message;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    z-index: 10000;
                    font-weight: 500;
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

        } catch (error) {
            console.error('❌ Failed to show notification:', error);
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize unified cash system
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.unifiedCashSystem = new ZaiqaUnifiedCashSystem();
    }, 1000);
});

// Export for global use
window.ZaiqaUnifiedCashSystem = ZaiqaUnifiedCashSystem;
