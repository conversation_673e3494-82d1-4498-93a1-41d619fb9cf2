// Test Script for Redesigned Zaiqa POS System
// Run this in browser console to test all new features

console.log('🧪 Testing Redesigned Zaiqa POS System...');

// Test 1: POS System Launch
function testPOSLaunch() {
    console.log('📱 Test 1: POS System Launch');
    try {
        if (window.app && typeof window.app.showPOSSystem === 'function') {
            window.app.showPOSSystem();
            console.log('✅ POS system launched successfully');
            
            // Check if redesigned elements exist
            setTimeout(() => {
                const redesignedPOS = document.querySelector('.redesigned-pos');
                const touchOptimized = document.querySelector('.touch-optimized');
                const compactCategories = document.querySelector('.compact-categories');
                
                if (redesignedPOS && touchOptimized && compactCategories) {
                    console.log('✅ Redesigned POS elements found');
                } else {
                    console.log('❌ Some redesigned elements missing');
                }
            }, 1000);
        } else {
            console.log('❌ POS system not available');
        }
    } catch (error) {
        console.log('❌ POS launch failed:', error.message);
    }
}

// Test 2: Menu Category Filtering
function testMenuFiltering() {
    console.log('🔍 Test 2: Menu Category Filtering');
    
    const categories = ['all', 'appetizers', 'main', 'beverages', 'takeaway', 'desserts'];
    
    categories.forEach(category => {
        try {
            if (window.app && typeof window.app.filterPOSItems === 'function') {
                window.app.filterPOSItems(category);
                console.log(`✅ Filter ${category} works`);
            }
        } catch (error) {
            console.log(`❌ Filter ${category} failed:`, error.message);
        }
    });
}

// Test 3: Custom Per Head Pricing
function testCustomPerHeadPricing() {
    console.log('💰 Test 3: Custom Per Head Pricing');
    
    try {
        // Set dine-in mode
        const serviceBtn = document.querySelector('[data-service="dine_in"]');
        if (serviceBtn) {
            serviceBtn.click();
            console.log('✅ Switched to dine-in mode');
        }
        
        // Test custom per head price input
        const perHeadInput = document.getElementById('customPerHeadPrice');
        if (perHeadInput) {
            perHeadInput.value = 150;
            perHeadInput.dispatchEvent(new Event('change'));
            console.log('✅ Custom per head price set to 150');
            
            // Test reset function
            if (window.app && typeof window.app.resetPerHeadPrice === 'function') {
                window.app.resetPerHeadPrice();
                console.log('✅ Per head price reset function works');
            }
        } else {
            console.log('❌ Custom per head price input not found');
        }
        
        // Test customer count adjustment
        if (window.app && typeof window.app.adjustCustomerCount === 'function') {
            window.app.adjustCustomerCount(1);
            window.app.adjustCustomerCount(-1);
            console.log('✅ Customer count adjustment works');
        }
        
    } catch (error) {
        console.log('❌ Custom per head pricing test failed:', error.message);
    }
}

// Test 4: Take Away Items Category
function testTakeAwayItems() {
    console.log('🛍️ Test 4: Take Away Items Category');
    
    try {
        // Filter to takeaway items
        if (window.app && typeof window.app.filterPOSItems === 'function') {
            window.app.filterPOSItems('takeaway');
            
            // Check if takeaway items are displayed
            setTimeout(() => {
                const takeawayItems = document.querySelectorAll('[data-category="takeaway"]');
                const visibleTakeawayItems = Array.from(takeawayItems).filter(item => 
                    item.style.display !== 'none'
                );
                
                if (visibleTakeawayItems.length > 0) {
                    console.log(`✅ Found ${visibleTakeawayItems.length} takeaway items`);
                    
                    // Check for takeaway-only badges
                    const takeawayBadges = document.querySelectorAll('.takeaway-badge');
                    if (takeawayBadges.length > 0) {
                        console.log('✅ Takeaway badges displayed');
                    }
                } else {
                    console.log('❌ No takeaway items found or not visible');
                }
            }, 500);
        }
    } catch (error) {
        console.log('❌ Take away items test failed:', error.message);
    }
}

// Test 5: Touch Optimization
function testTouchOptimization() {
    console.log('👆 Test 5: Touch Optimization');
    
    try {
        const touchElements = {
            'Touch Menu': '.touch-menu',
            'Touch Cart': '.touch-cart',
            'Compact Buttons': '.compact-btn',
            'Touch Controls': '.touch-controls',
            'Touch Buttons': '.touch-btn'
        };
        
        Object.entries(touchElements).forEach(([name, selector]) => {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                console.log(`✅ ${name}: ${elements.length} elements found`);
            } else {
                console.log(`❌ ${name}: No elements found`);
            }
        });
        
        // Test scrollable content
        const scrollableMenu = document.querySelector('.scrollable-menu');
        if (scrollableMenu) {
            console.log('✅ Scrollable menu container found');
        }
        
    } catch (error) {
        console.log('❌ Touch optimization test failed:', error.message);
    }
}

// Test 6: Removed Cold Drinks Extra Charges
function testRemovedColdDrinksCharges() {
    console.log('🥤 Test 6: Removed Cold Drinks Extra Charges');
    
    try {
        // Check if beverages have same price for dine-in and takeaway
        const menuItems = window.app ? window.app.getMenuItems() : [];
        const beverageItems = menuItems.filter(item => item.category === 'beverages');
        
        let allSamePrice = true;
        beverageItems.forEach(item => {
            if (item.dineInPrice !== item.takeawayPrice) {
                allSamePrice = false;
                console.log(`❌ ${item.name} has different prices: Dine-in ${item.dineInPrice}, Takeaway ${item.takeawayPrice}`);
            }
        });
        
        if (allSamePrice && beverageItems.length > 0) {
            console.log('✅ All beverages have same price for dine-in and takeaway');
        } else if (beverageItems.length === 0) {
            console.log('⚠️ No beverage items found to test');
        }
        
    } catch (error) {
        console.log('❌ Cold drinks charges test failed:', error.message);
    }
}

// Test 7: Responsive Design
function testResponsiveDesign() {
    console.log('📱 Test 7: Responsive Design');
    
    try {
        const responsiveElements = [
            '.redesigned-pos .pos-modal-content',
            '.touch-optimized',
            '.compact-grid',
            '.compact-categories'
        ];
        
        responsiveElements.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                const styles = window.getComputedStyle(element);
                console.log(`✅ ${selector} has responsive styles applied`);
            } else {
                console.log(`❌ ${selector} not found`);
            }
        });
        
    } catch (error) {
        console.log('❌ Responsive design test failed:', error.message);
    }
}

// Test 8: Fullscreen Toggle
function testFullscreenToggle() {
    console.log('🖥️ Test 8: Fullscreen Toggle');
    
    try {
        if (window.app && typeof window.app.togglePOSFullscreen === 'function') {
            window.app.togglePOSFullscreen();
            console.log('✅ Fullscreen toggle function works');
            
            // Toggle back
            setTimeout(() => {
                window.app.togglePOSFullscreen();
                console.log('✅ Fullscreen toggle back works');
            }, 1000);
        } else {
            console.log('❌ Fullscreen toggle function not found');
        }
    } catch (error) {
        console.log('❌ Fullscreen toggle test failed:', error.message);
    }
}

// Run All Tests
function runAllTests() {
    console.log('🚀 Starting Complete POS Redesign Tests...\n');
    
    testPOSLaunch();
    
    setTimeout(() => {
        testMenuFiltering();
        testCustomPerHeadPricing();
        testTakeAwayItems();
        testTouchOptimization();
        testRemovedColdDrinksCharges();
        testResponsiveDesign();
        testFullscreenToggle();
        
        console.log('\n🎉 All tests completed! Check results above.');
    }, 2000);
}

// Export test functions
window.testPOS = {
    runAll: runAllTests,
    launch: testPOSLaunch,
    filtering: testMenuFiltering,
    perHead: testCustomPerHeadPricing,
    takeaway: testTakeAwayItems,
    touch: testTouchOptimization,
    coldDrinks: testRemovedColdDrinksCharges,
    responsive: testResponsiveDesign,
    fullscreen: testFullscreenToggle
};

console.log('✅ POS Test Suite Loaded!');
console.log('💡 Usage:');
console.log('   testPOS.runAll() - Run all tests');
console.log('   testPOS.launch() - Test POS launch');
console.log('   testPOS.filtering() - Test menu filtering');
console.log('   testPOS.perHead() - Test custom per head pricing');
console.log('   testPOS.takeaway() - Test takeaway items');
console.log('   testPOS.touch() - Test touch optimization');
console.log('   testPOS.coldDrinks() - Test removed cold drinks charges');
console.log('   testPOS.responsive() - Test responsive design');
console.log('   testPOS.fullscreen() - Test fullscreen toggle');

// Auto-run tests if requested
if (window.location.search.includes('autotest=true')) {
    setTimeout(runAllTests, 1000);
}
