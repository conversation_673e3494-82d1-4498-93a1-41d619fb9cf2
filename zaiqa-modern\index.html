<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zaiqa Restaurant Management System - Modern Edition</title>
    
    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/modern-style.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Modern Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js">
</head>
<body class="bg-gray-50 font-inter">
    <!-- Loading Screen -->
    <div id="loadingScreen" class="fixed inset-0 bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent mx-auto mb-4"></div>
            <h2 class="text-2xl font-bold mb-2">Zaiqa Restaurant</h2>
            <p class="text-orange-100">Loading Modern Management System...</p>
        </div>
    </div>

    <!-- Main Application Container -->
    <div id="app" class="hidden min-h-screen bg-gray-50">
        <!-- Top Navigation Bar -->
        <nav class="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-40">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo and Brand -->
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-utensils text-white text-lg"></i>
                            </div>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">Zaiqa Restaurant</h1>
                            <p class="text-sm text-gray-500">Modern Management System</p>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="hidden md:flex items-center space-x-6">
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900" id="todayRevenue">PKR 0</div>
                            <div class="text-xs text-gray-500">Today's Revenue</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900" id="todayOrders">0</div>
                            <div class="text-xs text-gray-500">Orders</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900" id="cashInHand">PKR 0</div>
                            <div class="text-xs text-gray-500">Cash in Hand</div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-bell text-lg"></i>
                        </button>
                        <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Layout -->
        <div class="flex h-screen pt-16">
            <!-- Sidebar Navigation -->
            <aside class="w-64 bg-white shadow-lg border-r border-gray-200 overflow-y-auto">
                <div class="p-4">
                    <nav class="space-y-2">
                        <!-- Dashboard -->
                        <a href="#" onclick="modernApp.navigateTo('dashboard')" 
                           class="nav-item active flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-chart-line w-5 h-5 mr-3"></i>
                            Dashboard
                        </a>

                        <!-- POS System -->
                        <a href="#" onclick="modernApp.navigateTo('pos')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-cash-register w-5 h-5 mr-3"></i>
                            Point of Sale
                        </a>

                        <!-- Orders -->
                        <a href="#" onclick="modernApp.navigateTo('orders')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-receipt w-5 h-5 mr-3"></i>
                            Orders
                        </a>

                        <!-- Menu Management -->
                        <a href="#" onclick="modernApp.navigateTo('menu')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-utensils w-5 h-5 mr-3"></i>
                            Menu Management
                        </a>

                        <!-- Inventory -->
                        <a href="#" onclick="modernApp.navigateTo('inventory')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-boxes w-5 h-5 mr-3"></i>
                            Inventory
                        </a>

                        <!-- Financial Reports -->
                        <a href="#" onclick="modernApp.navigateTo('reports')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                            Financial Reports
                        </a>

                        <!-- Udhar Management -->
                        <a href="#" onclick="modernApp.navigateTo('udhar')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-credit-card w-5 h-5 mr-3"></i>
                            Udhar Management
                        </a>

                        <!-- Staff Management -->
                        <a href="#" onclick="modernApp.navigateTo('staff')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-users w-5 h-5 mr-3"></i>
                            Staff Management
                        </a>

                        <!-- Expenses -->
                        <a href="#" onclick="modernApp.navigateTo('expenses')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-wallet w-5 h-5 mr-3"></i>
                            Expenses
                        </a>

                        <!-- Settings -->
                        <a href="#" onclick="modernApp.navigateTo('settings')" 
                           class="nav-item flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                            <i class="fas fa-cog w-5 h-5 mr-3"></i>
                            Settings
                        </a>
                    </nav>
                </div>

                <!-- Quick Actions -->
                <div class="p-4 border-t border-gray-200">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Quick Actions</h3>
                    <div class="space-y-2">
                        <button onclick="modernApp.openPOS()" 
                                class="w-full bg-gradient-to-r from-orange-500 to-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-700 transition-all duration-200">
                            <i class="fas fa-plus mr-2"></i>New Order
                        </button>
                        <button onclick="modernApp.openCashRegister()" 
                                class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-all duration-200">
                            <i class="fas fa-cash-register mr-2"></i>Cash Register
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto bg-gray-50">
                <div id="mainContent" class="p-6">
                    <!-- Content will be dynamically loaded here -->
                </div>
            </main>
        </div>
    </div>

    <!-- Modern Modal Container -->
    <div id="modalContainer"></div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts - Load in correct order -->
    <!-- Utilities first -->
    <script src="assets/js/utils/helpers.js"></script>

    <!-- Components -->
    <script src="assets/js/components/toast.js"></script>
    <script src="assets/js/components/modal.js"></script>

    <!-- Core systems -->
    <script src="assets/js/core/data-manager.js"></script>
    <script src="assets/js/core/financial-engine.js"></script>

    <!-- Modules -->
    <script src="assets/js/modules/dashboard.js"></script>
    <script src="assets/js/modules/pos.js"></script>
    <script src="assets/js/modules/orders.js"></script>
    <script src="assets/js/modules/menu.js"></script>
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/reports.js"></script>
    <script src="assets/js/modules/udhar.js"></script>
    <script src="assets/js/modules/staff.js"></script>
    <script src="assets/js/modules/expenses.js"></script>

    <!-- Main application -->
    <script src="assets/js/modern-app.js"></script>

    <script>
        // Initialize the modern application with error handling
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log('🚀 Starting Modern Zaiqa Restaurant System...');

                // Check if all required classes are loaded
                const requiredClasses = [
                    'DataManager', 'FinancialEngine', 'ToastManager', 'ModalManager',
                    'DashboardModule', 'POSModule', 'OrdersModule', 'Helpers'
                ];

                const missingClasses = requiredClasses.filter(className => !window[className]);

                if (missingClasses.length > 0) {
                    throw new Error(`Missing required classes: ${missingClasses.join(', ')}`);
                }

                // Initialize the application
                window.modernApp = new ModernRestaurantApp();

            } catch (error) {
                console.error('❌ Application initialization failed:', error);

                // Show error in a user-friendly way
                document.body.innerHTML = `
                    <div class="min-h-screen flex items-center justify-center bg-red-50">
                        <div class="text-center max-w-md mx-auto p-6">
                            <div class="text-6xl text-red-500 mb-4">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h1 class="text-2xl font-bold text-red-900 mb-2">System Error</h1>
                            <p class="text-red-700 mb-4">Failed to initialize the restaurant system</p>
                            <div class="bg-red-100 border border-red-300 rounded p-3 mb-4">
                                <p class="text-sm text-red-800">${error.message}</p>
                            </div>
                            <button onclick="location.reload()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                <i class="fas fa-redo mr-2"></i>Reload System
                            </button>
                        </div>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
