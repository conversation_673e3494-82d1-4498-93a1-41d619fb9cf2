// Zaiqa Restaurant System - Critical Fixes Testing Script
// Copy and paste this entire script into the browser console on http://localhost:3000

console.log('🧪 ZAIQA RESTAURANT SYSTEM - CRITICAL FIXES TESTING');
console.log('='.repeat(60));

// Test 1: Check App Object
console.log('\n1️⃣ CHECKING APPLICATION STATUS...');
if (typeof app !== 'undefined') {
    console.log('✅ App object found');
    console.log('✅ App type:', typeof app);
} else {
    console.log('❌ App object not found');
    console.log('Please make sure you are on the main application page');
    throw new Error('App object not available');
}

// Test 2: Check Critical Functions
console.log('\n2️⃣ CHECKING CRITICAL FUNCTIONS...');
const criticalFunctions = [
    'deleteBill',
    'processOrderDeletion', 
    'deleteUsageRecord',
    'showUsageRecordDetails',
    'getCustomCategories',
    'showAddCustomCategoryModal',
    'generateCustomReport',
    'showCustomPeriodFullPageReport'
];

let allFunctionsExist = true;
criticalFunctions.forEach(func => {
    if (typeof app[func] === 'function') {
        console.log(`✅ ${func} function exists`);
    } else {
        console.log(`❌ ${func} function missing`);
        allFunctionsExist = false;
    }
});

if (allFunctionsExist) {
    console.log('✅ All critical functions are present');
} else {
    console.log('❌ Some critical functions are missing');
}

// Test 3: Check Data
console.log('\n3️⃣ CHECKING DATA AVAILABILITY...');
try {
    const orders = app.getOrders();
    const usageRecords = app.getUsageRecords();
    const inventoryItems = app.getInventoryItems();
    
    console.log(`📋 Orders: ${orders.length}`);
    console.log(`📦 Usage Records: ${usageRecords.length}`);
    console.log(`🏪 Inventory Items: ${inventoryItems.length}`);
    
    // Check for orphaned usage records
    const orderIds = new Set(orders.map(o => o.id));
    const orderNumbers = new Set(orders.map(o => o.order_number));
    const orphanedRecords = usageRecords.filter(r => 
        r.orderId && !orderIds.has(r.orderId) && 
        r.orderNumber && !orderNumbers.has(r.orderNumber)
    );
    
    console.log(`🗑️ Orphaned Usage Records: ${orphanedRecords.length}`);
    
    if (orphanedRecords.length > 0) {
        console.log('❌ Found orphaned usage records:');
        orphanedRecords.forEach(record => {
            console.log(`   - ${record.inventoryName} (Order: ${record.orderNumber})`);
        });
    } else {
        console.log('✅ No orphaned usage records found');
    }
    
} catch (error) {
    console.log('❌ Error accessing data:', error.message);
}

// Test 4: Test Delete Buttons in Usage History
console.log('\n4️⃣ TESTING DELETE BUTTONS IN USAGE HISTORY...');
app.navigateToPage('inventory');

setTimeout(() => {
    // Check table delete buttons
    const tableDeleteButtons = document.querySelectorAll('#inventoryUsageTableBody [onclick*="deleteUsageRecord"]');
    console.log(`📊 Table delete buttons: ${tableDeleteButtons.length}`);
    
    // Check folder delete buttons
    const folderDeleteButtons = document.querySelectorAll('#usageHistoryFolders [onclick*="deleteUsageRecord"]');
    console.log(`📁 Folder delete buttons: ${folderDeleteButtons.length}`);
    
    if (tableDeleteButtons.length > 0 || folderDeleteButtons.length > 0) {
        console.log('✅ Delete buttons found in usage history');
    } else {
        console.log('❌ No delete buttons found in usage history');
    }
}, 1000);

// Test 5: Test Custom Categories
setTimeout(() => {
    console.log('\n5️⃣ TESTING CUSTOM CATEGORIES...');
    
    const categoryFilter = document.getElementById('inventoryCategoryFilter');
    if (categoryFilter) {
        const addCustomOption = Array.from(categoryFilter.options).find(opt => opt.value === 'add_custom');
        if (addCustomOption) {
            console.log('✅ "Add Custom Category" option found in dropdown');
        } else {
            console.log('❌ "Add Custom Category" option missing from dropdown');
        }
        
        const customCategories = app.getCustomCategories();
        console.log(`📂 Custom categories: ${customCategories.length}`);
        
    } else {
        console.log('❌ Category filter dropdown not found');
    }
}, 2000);

// Test 6: Test Bill Deletion Process
setTimeout(() => {
    console.log('\n6️⃣ TESTING BILL DELETION PROCESS...');
    
    const orders = app.getOrders();
    if (orders.length > 0) {
        const testOrder = orders[0];
        const usageRecords = app.getUsageRecords();
        const orderUsageRecords = usageRecords.filter(r => 
            r.orderId === testOrder.id || r.orderNumber === testOrder.order_number
        );
        
        console.log(`🔍 Test Order: ${testOrder.order_number}`);
        console.log(`📦 Usage records for this order: ${orderUsageRecords.length}`);
        
        if (orderUsageRecords.length > 0) {
            console.log('✅ Order has usage records - deletion test possible');
            console.log('⚠️ To test deletion, go to Bills page and delete this order');
        } else {
            console.log('⚠️ Order has no usage records - create an order with ingredients first');
        }
    } else {
        console.log('⚠️ No orders found - create some orders first');
    }
}, 3000);

// Test 7: Test Custom Reports
setTimeout(() => {
    console.log('\n7️⃣ TESTING CUSTOM REPORTS...');
    
    app.navigateToPage('reports');
    
    setTimeout(() => {
        const startDateInput = document.getElementById('reportStartDate');
        const endDateInput = document.getElementById('reportEndDate');
        
        if (startDateInput && endDateInput) {
            console.log('✅ Custom date inputs found');
            
            // Set test dates
            const today = new Date();
            const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            startDateInput.value = lastWeek.toISOString().split('T')[0];
            endDateInput.value = today.toISOString().split('T')[0];
            
            console.log('📅 Test dates set - you can now click "Generate Report" to test');
        } else {
            console.log('❌ Custom date inputs not found');
        }
    }, 1000);
}, 4000);

// Final Summary
setTimeout(() => {
    console.log('\n🎯 TESTING SUMMARY');
    console.log('='.repeat(40));
    console.log('✅ App object check completed');
    console.log('✅ Function availability check completed');
    console.log('✅ Data integrity check completed');
    console.log('✅ UI elements check completed');
    console.log('\n📋 MANUAL TESTS TO PERFORM:');
    console.log('1. Go to Bills page and delete a bill - check if usage records are removed');
    console.log('2. Go to Inventory → Usage History and click delete buttons');
    console.log('3. Go to Inventory and test "Add Custom Category" option');
    console.log('4. Go to Reports and generate a custom period report');
    console.log('\n🔧 UTILITY FUNCTIONS AVAILABLE:');
    console.log('- testAllCriticalFixes() - Run comprehensive tests');
    console.log('- fixDuplicateUsageRecords() - Fix duplicate usage records');
    console.log('- testBillDeletion(orderId) - Test specific bill deletion');
}, 5000);
