/**
 * AI Interface Cleanup - Remove Chatbot UI, Keep Intelligence
 * Removes chatbot interface while maintaining AI-powered insights and analytics
 */

class ZaiqaAICleanup {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize AI interface cleanup
     */
    init() {
        try {
            console.log('🤖 Initializing AI Interface Cleanup v' + this.version);
            
            // Remove chatbot UI elements
            this.removeChatbotInterface();
            
            // Keep AI intelligence features
            this.preserveAIIntelligence();
            
            // Fix cash transaction display
            this.fixCashTransactionDisplay();
            
            this.initialized = true;
            console.log('✅ AI Interface cleanup completed - Intelligence preserved');
            
        } catch (error) {
            console.error('❌ AI Interface cleanup failed:', error);
        }
    }

    /**
     * Remove chatbot interface elements
     */
    removeChatbotInterface() {
        try {
            // Remove chatbot toggle button
            const chatbotToggle = document.querySelector('.ai-assistant-toggle');
            if (chatbotToggle) {
                chatbotToggle.remove();
                console.log('✅ Chatbot toggle removed');
            }

            // Remove chatbot modal/container
            const chatbotModal = document.querySelector('.ai-assistant-modal');
            if (chatbotModal) {
                chatbotModal.remove();
                console.log('✅ Chatbot modal removed');
            }

            // Remove all AI chat related elements
            const aiElements = [
                '.chat-widget', '.ai-chat-container', '.assistant-chat',
                '.ai-chat-widget', '.ai-assistant-btn', '.floating-ai-btn',
                '.ai-assistant-panel', '.ai-chat-modal', '.ai-floating-widget',
                '#aiAssistant', '#aiChatWidget', '#aiFloatingButton',
                '.ai-assistant', '.chatbot-container', '.ai-interface',
                '.ai-chat-interface', '.ai-assistant-interface',
                '.floating-chat-btn', '.chat-bubble', '.ai-bubble'
            ];

            aiElements.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.remove();
                });
            });

            // Remove chat-related CSS classes from body
            document.body.classList.remove('chat-open', 'assistant-active', 'ai-active');

            // Hide AI assistant sidebar if it exists
            const aiSidebar = document.querySelector('.ai-assistant-sidebar');
            if (aiSidebar) {
                aiSidebar.style.display = 'none';
            }

            // Remove chat button from navigation
            const navChatButton = document.querySelector('a[href="#ai-assistant"], .nav-item[data-page="ai-assistant"]');
            if (navChatButton) {
                navChatButton.style.display = 'none';
            }

            // Disable AI initialization functions
            if (window.initializeAI) window.initializeAI = () => {};
            if (window.startAI) window.startAI = () => {};
            if (window.loadAIAssistant) window.loadAIAssistant = () => {};
            if (window.showAIChat) window.showAIChat = () => {};
            if (window.toggleAIAssistant) window.toggleAIAssistant = () => {};

            console.log('✅ Chatbot interface elements removed');

        } catch (error) {
            console.error('❌ Failed to remove chatbot interface:', error);
        }
    }

    /**
     * Preserve AI intelligence features for analytics and insights
     */
    preserveAIIntelligence() {
        try {
            // Keep AI-powered analytics functions
            if (window.aiAssistant) {
                // Preserve analytical functions
                const preservedFunctions = [
                    'analyzeBusinessMetrics',
                    'generateInsights',
                    'calculateTrends',
                    'optimizationSuggestions',
                    'riskAnalysis',
                    'forecastRevenue',
                    'analyzeCosts'
                ];

                const aiIntelligence = {};
                preservedFunctions.forEach(funcName => {
                    if (typeof window.aiAssistant[funcName] === 'function') {
                        aiIntelligence[funcName] = window.aiAssistant[funcName].bind(window.aiAssistant);
                    }
                });

                // Store preserved AI intelligence
                window.aiIntelligence = aiIntelligence;
                console.log('✅ AI intelligence functions preserved');
            }

            // Enhance reports with AI insights
            this.enhanceReportsWithAI();

        } catch (error) {
            console.error('❌ Failed to preserve AI intelligence:', error);
        }
    }

    /**
     * Enhance reports with AI insights
     */
    enhanceReportsWithAI() {
        try {
            // Add AI insights to reports page
            if (window.zaiqaReports && typeof window.zaiqaReports.addAIInsights !== 'function') {
                window.zaiqaReports.addAIInsights = function(data) {
                    try {
                        const insights = [];

                        // Revenue insights
                        if (data.revenue && data.revenue.totalRevenue > 0) {
                            const avgOrder = data.revenue.averageOrderValue || 0;
                            if (avgOrder < 500) {
                                insights.push({
                                    type: 'opportunity',
                                    title: 'Increase Average Order Value',
                                    description: `Current average order is PKR ${avgOrder.toFixed(0)}. Consider upselling or combo offers to increase to PKR 600+.`,
                                    impact: 'medium',
                                    icon: 'fas fa-arrow-up'
                                });
                            }
                        }

                        // Cost insights
                        if (data.expenses && data.revenue) {
                            const costRatio = (data.expenses.totalExpenses / data.revenue.totalRevenue) * 100;
                            if (costRatio > 60) {
                                insights.push({
                                    type: 'warning',
                                    title: 'High Cost Ratio',
                                    description: `Expenses are ${costRatio.toFixed(1)}% of revenue. Target should be below 50% for healthy margins.`,
                                    impact: 'high',
                                    icon: 'fas fa-exclamation-triangle'
                                });
                            }
                        }

                        // Cash flow insights
                        if (data.cashFlow && data.cashFlow.cashDifference !== 0) {
                            const diff = Math.abs(data.cashFlow.cashDifference);
                            if (diff > 100) {
                                insights.push({
                                    type: 'alert',
                                    title: 'Cash Discrepancy Detected',
                                    description: `Cash difference of PKR ${diff.toFixed(0)} found. Review transactions for accuracy.`,
                                    impact: 'high',
                                    icon: 'fas fa-exclamation-circle'
                                });
                            }
                        }

                        return insights;

                    } catch (error) {
                        console.error('❌ Failed to generate AI insights:', error);
                        return [];
                    }
                };
            }

        } catch (error) {
            console.error('❌ Failed to enhance reports with AI:', error);
        }
    }

    /**
     * Fix cash transaction display issues
     */
    fixCashTransactionDisplay() {
        try {
            // Override cash transaction display function
            if (window.app && typeof window.app.displayCashTransactions === 'function') {
                window.app.originalDisplayCashTransactions = window.app.displayCashTransactions;
                
                window.app.displayCashTransactions = function(containerId) {
                    try {
                        const container = document.getElementById(containerId);
                        if (!container) return;

                        // Get cash transactions with proper formatting
                        const cashTransactions = window.cashManager ? 
                            window.cashManager.getRecentCashTransactions(10) : [];

                        // Get recent expenses for context
                        const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                        const recentExpenses = expenses
                            .filter(exp => {
                                const expDate = new Date(exp.date || exp.created_at);
                                const today = new Date();
                                const diffTime = Math.abs(today - expDate);
                                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                                return diffDays <= 7; // Last 7 days
                            })
                            .sort((a, b) => new Date(b.created_at || b.date) - new Date(a.created_at || a.date))
                            .slice(0, 5);

                        // Combine and format transactions
                        const allTransactions = [];

                        // Add cash transactions
                        cashTransactions.forEach(transaction => {
                            allTransactions.push({
                                id: transaction.id,
                                type: 'cash',
                                description: transaction.description,
                                amount: transaction.amount,
                                timestamp: transaction.timestamp,
                                category: transaction.category,
                                displayType: transaction.type === 'receipt' ? 'Cash Receipt' : 'Cash Deduction'
                            });
                        });

                        // Add expense transactions
                        recentExpenses.forEach(expense => {
                            allTransactions.push({
                                id: expense.id,
                                type: 'expense',
                                description: expense.description,
                                amount: -Math.abs(expense.amount), // Show as negative
                                timestamp: expense.created_at || expense.date,
                                category: expense.category,
                                displayType: 'Expense'
                            });
                        });

                        // Sort by timestamp
                        allTransactions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

                        // Display transactions
                        if (allTransactions.length === 0) {
                            container.innerHTML = `
                                <div class="no-transactions">
                                    <i class="fas fa-info-circle"></i>
                                    <p>No recent cash transactions found</p>
                                </div>
                            `;
                        } else {
                            container.innerHTML = `
                                <div class="cash-transactions-list">
                                    ${allTransactions.map(transaction => this.formatTransactionDisplay(transaction)).join('')}
                                </div>
                            `;
                        }

                    } catch (error) {
                        console.error('❌ Failed to display cash transactions:', error);
                        container.innerHTML = `
                            <div class="error-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Error loading transactions</p>
                            </div>
                        `;
                    }
                };

                // Add transaction formatting function
                window.app.formatTransactionDisplay = function(transaction) {
                    const date = new Date(transaction.timestamp);
                    const timeString = date.toLocaleTimeString('en-US', { 
                        hour: '2-digit', 
                        minute: '2-digit',
                        hour12: true 
                    });
                    const dateString = date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                    });

                    const amountClass = transaction.amount >= 0 ? 'positive' : 'negative';
                    const amountPrefix = transaction.amount >= 0 ? '+' : '';
                    const typeIcon = transaction.type === 'cash' ? 
                        (transaction.amount >= 0 ? 'fas fa-plus-circle' : 'fas fa-minus-circle') :
                        'fas fa-receipt';

                    return `
                        <div class="transaction-item ${transaction.type}">
                            <div class="transaction-icon">
                                <i class="${typeIcon}"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-description">
                                    ${transaction.description}
                                </div>
                                <div class="transaction-meta">
                                    <span class="transaction-type">${transaction.displayType}</span>
                                    <span class="transaction-category">${transaction.category}</span>
                                </div>
                                <div class="transaction-time">
                                    ${timeString} • ${dateString}
                                </div>
                            </div>
                            <div class="transaction-amount ${amountClass}">
                                ${amountPrefix}PKR ${Math.abs(transaction.amount).toLocaleString()}
                            </div>
                        </div>
                    `;
                };
            }

            console.log('✅ Cash transaction display fixed');

        } catch (error) {
            console.error('❌ Failed to fix cash transaction display:', error);
        }
    }

    /**
     * Remove phantom expenses and clean up data
     */
    cleanupPhantomExpenses() {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            
            // Identify phantom expenses
            const phantomPatterns = [
                /auto expense/i,
                /phantom/i,
                /test expense/i,
                /dummy/i
            ];

            const cleanedExpenses = expenses.filter(expense => {
                const description = expense.description || '';
                const amount = parseFloat(expense.amount || 0);
                
                // Remove if matches phantom patterns
                const isPhantom = phantomPatterns.some(pattern => pattern.test(description));
                
                // Remove if amount is exactly 400 and description is suspicious
                const isSuspicious400 = amount === 400 && (
                    description.toLowerCase().includes('auto') ||
                    description.toLowerCase().includes('test') ||
                    description === ''
                );

                return !isPhantom && !isSuspicious400 && amount > 0;
            });

            if (cleanedExpenses.length !== expenses.length) {
                localStorage.setItem('expenses', JSON.stringify(cleanedExpenses));
                console.log(`✅ Cleaned up ${expenses.length - cleanedExpenses.length} phantom expenses`);
                
                // Show notification
                if (window.cashManager && typeof window.cashManager.showNotification === 'function') {
                    window.cashManager.showNotification(
                        `Cleaned up ${expenses.length - cleanedExpenses.length} phantom expenses`, 
                        'success'
                    );
                }
            }

        } catch (error) {
            console.error('❌ Failed to cleanup phantom expenses:', error);
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize AI cleanup
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.aiCleanup = new ZaiqaAICleanup();
        
        // Clean up phantom expenses after initialization
        setTimeout(() => {
            if (window.aiCleanup) {
                window.aiCleanup.cleanupPhantomExpenses();
            }
        }, 2000);
    }, 1000);
});

// Export for global use
window.ZaiqaAICleanup = ZaiqaAICleanup;
