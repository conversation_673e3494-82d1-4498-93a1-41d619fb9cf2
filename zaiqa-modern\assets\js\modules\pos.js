/**
 * Modern POS Module - Point of Sale System
 * Handles order creation, menu selection, and payment processing
 */

class POSModule {
    constructor(app) {
        this.app = app;
        this.currentOrder = {
            items: [],
            customerName: '',
            customerCount: 1,
            serviceType: 'dine_in',
            paymentMethod: 'cash',
            perHeadCharges: 0,
            additionalCharges: 0,
            discount: 0
        };
        this.selectedCategory = 'all';
    }

    /**
     * Initialize POS module
     */
    async init() {
        console.log('🛒 POS module initialized');
    }

    /**
     * Render POS interface
     */
    async render() {
        const menuItems = this.app.dataManager.get('menuItems');
        const categories = this.getUniqueCategories(menuItems);

        return `
            <div class="pos-container">
                <!-- POS Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Point of Sale</h1>
                        <p class="text-gray-600 mt-1">Create new orders and process payments</p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="modernApp.modules.pos.clearOrder()" class="btn btn-secondary">
                            <i class="fas fa-trash mr-2"></i>Clear Order
                        </button>
                        <button onclick="modernApp.modules.pos.openModal()" class="btn btn-primary">
                            <i class="fas fa-expand mr-2"></i>Full Screen POS
                        </button>
                    </div>
                </div>

                <!-- POS Layout -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Menu Items (Left Side) -->
                    <div class="lg:col-span-2">
                        <!-- Category Filter -->
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2">
                                <button onclick="modernApp.modules.pos.selectCategory('all')" 
                                        class="category-btn ${this.selectedCategory === 'all' ? 'active' : ''}" 
                                        data-category="all">
                                    All Items
                                </button>
                                ${categories.map(category => `
                                    <button onclick="modernApp.modules.pos.selectCategory('${category}')" 
                                            class="category-btn ${this.selectedCategory === category ? 'active' : ''}" 
                                            data-category="${category}">
                                        ${category.charAt(0).toUpperCase() + category.slice(1)}
                                    </button>
                                `).join('')}
                            </div>
                        </div>

                        <!-- Menu Items Grid -->
                        <div class="menu-items-grid">
                            ${this.generateMenuItemsGrid(menuItems)}
                        </div>
                    </div>

                    <!-- Order Summary (Right Side) -->
                    <div class="order-summary-panel">
                        ${this.generateOrderSummary()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate menu items grid
     */
    generateMenuItemsGrid(menuItems) {
        const filteredItems = this.selectedCategory === 'all' 
            ? menuItems 
            : menuItems.filter(item => item.category === this.selectedCategory);

        if (filteredItems.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3>No items found</h3>
                    <p>No menu items available in this category</p>
                </div>
            `;
        }

        return `
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                ${filteredItems.map(item => `
                    <div class="menu-item-card ${!item.isAvailable ? 'unavailable' : ''}" 
                         onclick="modernApp.modules.pos.addToOrder('${item.id}')">
                        <div class="menu-item-image">
                            <i class="fas fa-utensils text-2xl text-gray-400"></i>
                        </div>
                        <div class="menu-item-info">
                            <h4 class="menu-item-name">${item.name}</h4>
                            <p class="menu-item-price">PKR ${item.price.toLocaleString()}</p>
                            ${item.description ? `<p class="menu-item-description">${item.description}</p>` : ''}
                        </div>
                        ${!item.isAvailable ? '<div class="unavailable-overlay">Out of Stock</div>' : ''}
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate order summary panel
     */
    generateOrderSummary() {
        const subtotal = this.calculateSubtotal();
        const total = this.calculateTotal();

        return `
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold">Current Order</h3>
                </div>
                <div class="card-body">
                    <!-- Customer Info -->
                    <div class="mb-4">
                        <div class="form-group">
                            <label class="form-label">Customer Name</label>
                            <input type="text" id="customerName" class="form-control" 
                                   value="${this.currentOrder.customerName}" 
                                   onchange="modernApp.modules.pos.updateCustomerName(this.value)"
                                   placeholder="Walk-in Customer">
                        </div>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="form-group">
                                <label class="form-label">Customer Count</label>
                                <input type="number" id="customerCount" class="form-control" 
                                       value="${this.currentOrder.customerCount}" 
                                       onchange="modernApp.modules.pos.updateCustomerCount(this.value)"
                                       min="1" max="20">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Service Type</label>
                                <select id="serviceType" class="form-control" 
                                        onchange="modernApp.modules.pos.updateServiceType(this.value)">
                                    <option value="dine_in" ${this.currentOrder.serviceType === 'dine_in' ? 'selected' : ''}>Dine In</option>
                                    <option value="takeaway" ${this.currentOrder.serviceType === 'takeaway' ? 'selected' : ''}>Take Away</option>
                                    <option value="delivery" ${this.currentOrder.serviceType === 'delivery' ? 'selected' : ''}>Delivery</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="order-items mb-4">
                        <h4 class="font-semibold mb-2">Order Items</h4>
                        ${this.generateOrderItems()}
                    </div>

                    <!-- Additional Charges -->
                    <div class="additional-charges mb-4">
                        <h4 class="font-semibold mb-2">Additional Charges</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <label class="text-sm">Per Head Charges:</label>
                                <input type="number" class="form-control w-24 text-right" 
                                       value="${this.currentOrder.perHeadCharges}" 
                                       onchange="modernApp.modules.pos.updatePerHeadCharges(this.value)"
                                       min="0" step="0.01">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm">Additional Charges:</label>
                                <input type="number" class="form-control w-24 text-right" 
                                       value="${this.currentOrder.additionalCharges}" 
                                       onchange="modernApp.modules.pos.updateAdditionalCharges(this.value)"
                                       min="0" step="0.01">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm">Discount:</label>
                                <input type="number" class="form-control w-24 text-right" 
                                       value="${this.currentOrder.discount}" 
                                       onchange="modernApp.modules.pos.updateDiscount(this.value)"
                                       min="0" step="0.01">
                            </div>
                        </div>
                    </div>

                    <!-- Order Total -->
                    <div class="order-total mb-4">
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span>Subtotal:</span>
                                <span>PKR ${subtotal.toLocaleString()}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Per Head (${this.currentOrder.customerCount} × ${this.currentOrder.perHeadCharges}):</span>
                                <span>PKR ${(this.currentOrder.customerCount * this.currentOrder.perHeadCharges).toLocaleString()}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Additional Charges:</span>
                                <span>PKR ${this.currentOrder.additionalCharges.toLocaleString()}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Discount:</span>
                                <span class="text-red-600">-PKR ${this.currentOrder.discount.toLocaleString()}</span>
                            </div>
                            <hr class="my-2">
                            <div class="flex justify-between font-bold text-lg">
                                <span>Total:</span>
                                <span class="text-orange-600">PKR ${total.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="payment-method mb-4">
                        <label class="form-label">Payment Method</label>
                        <select id="paymentMethod" class="form-control" 
                                onchange="modernApp.modules.pos.updatePaymentMethod(this.value)">
                            <option value="cash" ${this.currentOrder.paymentMethod === 'cash' ? 'selected' : ''}>Cash</option>
                            <option value="card" ${this.currentOrder.paymentMethod === 'card' ? 'selected' : ''}>Card</option>
                            <option value="udhar" ${this.currentOrder.paymentMethod === 'udhar' ? 'selected' : ''}>Udhar</option>
                            <option value="online" ${this.currentOrder.paymentMethod === 'online' ? 'selected' : ''}>Online Transfer</option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-2">
                        <button onclick="modernApp.modules.pos.processOrder()" 
                                class="w-full btn btn-primary btn-lg" 
                                ${this.currentOrder.items.length === 0 ? 'disabled' : ''}>
                            <i class="fas fa-check mr-2"></i>Process Order
                        </button>
                        <button onclick="modernApp.modules.pos.saveAsDraft()" 
                                class="w-full btn btn-secondary"
                                ${this.currentOrder.items.length === 0 ? 'disabled' : ''}>
                            <i class="fas fa-save mr-2"></i>Save as Draft
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate order items list
     */
    generateOrderItems() {
        if (this.currentOrder.items.length === 0) {
            return '<p class="text-gray-500 text-center py-4">No items added</p>';
        }

        return `
            <div class="space-y-2">
                ${this.currentOrder.items.map((item, index) => `
                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div class="flex-1">
                            <p class="font-medium">${item.name}</p>
                            <p class="text-sm text-gray-600">PKR ${item.price.toLocaleString()} each</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="modernApp.modules.pos.decreaseQuantity(${index})" 
                                    class="btn-action btn-edit">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="w-8 text-center font-medium">${item.quantity}</span>
                            <button onclick="modernApp.modules.pos.increaseQuantity(${index})" 
                                    class="btn-action btn-edit">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button onclick="modernApp.modules.pos.removeItem(${index})" 
                                    class="btn-action btn-delete ml-2">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Get unique categories from menu items
     */
    getUniqueCategories(menuItems) {
        const categories = [...new Set(menuItems.map(item => item.category))];
        return categories.filter(category => category && category.trim() !== '');
    }

    /**
     * Select category
     */
    selectCategory(category) {
        this.selectedCategory = category;
        this.app.refreshCurrentPage();
    }

    /**
     * Add item to order
     */
    addToOrder(itemId) {
        const menuItems = this.app.dataManager.get('menuItems');
        const menuItem = menuItems.find(item => item.id === itemId);
        
        if (!menuItem || !menuItem.isAvailable) {
            this.app.showToast('Item is not available', 'warning');
            return;
        }

        // Check if item already exists in order
        const existingItemIndex = this.currentOrder.items.findIndex(item => item.id === itemId);
        
        if (existingItemIndex >= 0) {
            this.currentOrder.items[existingItemIndex].quantity += 1;
        } else {
            this.currentOrder.items.push({
                id: menuItem.id,
                name: menuItem.name,
                price: menuItem.price,
                quantity: 1
            });
        }

        this.refreshOrderSummary();
        this.app.showToast(`${menuItem.name} added to order`, 'success');
    }

    /**
     * Remove item from order
     */
    removeItem(index) {
        const item = this.currentOrder.items[index];
        this.currentOrder.items.splice(index, 1);
        this.refreshOrderSummary();
        this.app.showToast(`${item.name} removed from order`, 'info');
    }

    /**
     * Increase item quantity
     */
    increaseQuantity(index) {
        this.currentOrder.items[index].quantity += 1;
        this.refreshOrderSummary();
    }

    /**
     * Decrease item quantity
     */
    decreaseQuantity(index) {
        if (this.currentOrder.items[index].quantity > 1) {
            this.currentOrder.items[index].quantity -= 1;
            this.refreshOrderSummary();
        }
    }

    /**
     * Update customer name
     */
    updateCustomerName(name) {
        this.currentOrder.customerName = name;
    }

    /**
     * Update customer count
     */
    updateCustomerCount(count) {
        this.currentOrder.customerCount = parseInt(count) || 1;
        this.refreshOrderSummary();
    }

    /**
     * Update service type
     */
    updateServiceType(type) {
        this.currentOrder.serviceType = type;
    }

    /**
     * Update payment method
     */
    updatePaymentMethod(method) {
        this.currentOrder.paymentMethod = method;
    }

    /**
     * Update per head charges
     */
    updatePerHeadCharges(amount) {
        this.currentOrder.perHeadCharges = parseFloat(amount) || 0;
        this.refreshOrderSummary();
    }

    /**
     * Update additional charges
     */
    updateAdditionalCharges(amount) {
        this.currentOrder.additionalCharges = parseFloat(amount) || 0;
        this.refreshOrderSummary();
    }

    /**
     * Update discount
     */
    updateDiscount(amount) {
        this.currentOrder.discount = parseFloat(amount) || 0;
        this.refreshOrderSummary();
    }

    /**
     * Calculate subtotal
     */
    calculateSubtotal() {
        return this.currentOrder.items.reduce((total, item) => {
            return total + (item.price * item.quantity);
        }, 0);
    }

    /**
     * Calculate total
     */
    calculateTotal() {
        const subtotal = this.calculateSubtotal();
        const perHeadTotal = this.currentOrder.customerCount * this.currentOrder.perHeadCharges;
        const total = subtotal + perHeadTotal + this.currentOrder.additionalCharges - this.currentOrder.discount;
        return Math.max(0, total);
    }

    /**
     * Refresh order summary
     */
    refreshOrderSummary() {
        const summaryPanel = document.querySelector('.order-summary-panel');
        if (summaryPanel) {
            summaryPanel.innerHTML = this.generateOrderSummary();
        }
    }

    /**
     * Process order
     */
    async processOrder() {
        try {
            if (this.currentOrder.items.length === 0) {
                this.app.showToast('Please add items to the order', 'warning');
                return;
            }

            const orderData = {
                orderNumber: this.generateOrderNumber(),
                customerName: this.currentOrder.customerName || 'Walk-in Customer',
                customerCount: this.currentOrder.customerCount,
                serviceType: this.currentOrder.serviceType,
                paymentMethod: this.currentOrder.paymentMethod,
                items: this.currentOrder.items,
                totalAmount: this.calculateTotal(),
                perHeadCharges: this.currentOrder.perHeadCharges,
                additionalCharges: this.currentOrder.additionalCharges,
                discount: this.currentOrder.discount,
                status: 'completed',
                createdAt: new Date().toISOString()
            };

            // Save order
            await this.app.dataManager.add('orders', orderData);

            // Clear current order
            this.clearOrder();

            this.app.showToast('Order processed successfully!', 'success');

            // Optionally navigate to orders page
            // await this.app.navigateTo('orders');

        } catch (error) {
            console.error('❌ Order processing failed:', error);
            this.app.showToast('Failed to process order', 'error');
        }
    }

    /**
     * Save as draft
     */
    async saveAsDraft() {
        // Implementation for saving draft orders
        this.app.showToast('Order saved as draft', 'info');
    }

    /**
     * Clear order
     */
    clearOrder() {
        this.currentOrder = {
            items: [],
            customerName: '',
            customerCount: 1,
            serviceType: 'dine_in',
            paymentMethod: 'cash',
            perHeadCharges: 0,
            additionalCharges: 0,
            discount: 0
        };
        this.refreshOrderSummary();
    }

    /**
     * Generate order number
     */
    generateOrderNumber() {
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0].replace(/-/g, '');
        const timeStr = now.getTime().toString().slice(-4);
        return `ORD${dateStr}${timeStr}`;
    }

    /**
     * Open POS modal (full screen)
     */
    async openModal() {
        const modal = this.app.modalManager.create({
            title: 'Point of Sale System',
            size: 'full',
            content: await this.render()
        });

        modal.show();
    }

    /**
     * Called when page loads
     */
    async onPageLoad() {
        // Add any page-specific initialization
        this.addPOSStyles();
    }

    /**
     * Add POS-specific styles
     */
    addPOSStyles() {
        if (!document.getElementById('pos-styles')) {
            const styles = document.createElement('style');
            styles.id = 'pos-styles';
            styles.textContent = `
                .menu-item-card {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 12px;
                    padding: 16px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    position: relative;
                }
                
                .menu-item-card:hover {
                    border-color: #f97316;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
                }
                
                .menu-item-card.unavailable {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
                
                .menu-item-image {
                    text-align: center;
                    margin-bottom: 12px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f8fafc;
                    border-radius: 8px;
                }
                
                .menu-item-name {
                    font-weight: 600;
                    color: #1f2937;
                    margin-bottom: 4px;
                    font-size: 14px;
                }
                
                .menu-item-price {
                    color: #f97316;
                    font-weight: 700;
                    font-size: 16px;
                    margin-bottom: 4px;
                }
                
                .menu-item-description {
                    color: #6b7280;
                    font-size: 12px;
                    line-height: 1.4;
                }
                
                .unavailable-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 12px;
                }
                
                .category-btn {
                    padding: 8px 16px;
                    border: 1px solid #d1d5db;
                    border-radius: 20px;
                    background: white;
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                
                .category-btn:hover {
                    border-color: #f97316;
                    color: #f97316;
                }
                
                .category-btn.active {
                    background: #f97316;
                    border-color: #f97316;
                    color: white;
                }
                
                .order-summary-panel {
                    position: sticky;
                    top: 20px;
                    height: fit-content;
                }
                
                @media (max-width: 1024px) {
                    .order-summary-panel {
                        position: static;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
    }
}

// Export for global use
window.POSModule = POSModule;
