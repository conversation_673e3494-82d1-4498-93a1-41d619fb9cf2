/**
 * Orders Module - Order Management System
 * Handles order viewing, editing, and management
 */

console.log('📋 Loading OrdersModule class...');

class OrdersModule {
    constructor(app) {
        this.app = app;
        this.currentFilter = 'all';
        this.searchQuery = '';
    }

    async init() {
        console.log('📋 Orders module initialized');
    }

    async render() {
        const orders = this.getFilteredOrders();
        
        return `
            <div class="orders-container">
                <!-- Page Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Orders</h1>
                        <p class="text-gray-600 mt-1">Manage and track all restaurant orders</p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="modernApp.openPOS()" class="btn btn-primary">
                            <i class="fas fa-plus mr-2"></i>New Order
                        </button>
                        <button onclick="modernApp.modules.orders.exportOrders()" class="btn btn-secondary">
                            <i class="fas fa-download mr-2"></i>Export
                        </button>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="card mb-6">
                    <div class="card-body">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1">
                                <input type="text" placeholder="Search orders..." 
                                       class="form-control" 
                                       value="${this.searchQuery}"
                                       onchange="modernApp.modules.orders.updateSearch(this.value)">
                            </div>
                            <div class="flex gap-2">
                                <select class="form-control" onchange="modernApp.modules.orders.updateFilter(this.value)">
                                    <option value="all" ${this.currentFilter === 'all' ? 'selected' : ''}>All Orders</option>
                                    <option value="completed" ${this.currentFilter === 'completed' ? 'selected' : ''}>Completed</option>
                                    <option value="pending" ${this.currentFilter === 'pending' ? 'selected' : ''}>Pending</option>
                                    <option value="cancelled" ${this.currentFilter === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                                </select>
                                <button onclick="modernApp.modules.orders.refreshOrders()" class="btn btn-secondary">
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="card">
                    <div class="card-body p-0">
                        ${this.generateOrdersTable(orders)}
                    </div>
                </div>
            </div>
        `;
    }

    generateOrdersTable(orders) {
        if (orders.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <h3>No orders found</h3>
                    <p>No orders match your current filters</p>
                    <button onclick="modernApp.openPOS()" class="btn btn-primary mt-4">
                        <i class="fas fa-plus mr-2"></i>Create First Order
                    </button>
                </div>
            `;
        }

        return `
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Items</th>
                            <th>Amount</th>
                            <th>Payment</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map(order => `
                            <tr>
                                <td>
                                    <span class="font-medium">${order.orderNumber}</span>
                                </td>
                                <td>
                                    <div>
                                        <p class="font-medium">${order.customerName}</p>
                                        <p class="text-sm text-gray-500">${order.customerCount} customer(s)</p>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-sm">${order.items.length} item(s)</span>
                                </td>
                                <td>
                                    <span class="font-semibold">PKR ${order.totalAmount.toLocaleString()}</span>
                                </td>
                                <td>
                                    <span class="capitalize">${order.paymentMethod}</span>
                                </td>
                                <td>
                                    <span class="status-badge status-${order.status}">${order.status}</span>
                                </td>
                                <td>
                                    <span class="text-sm">${new Date(order.createdAt).toLocaleDateString()}</span>
                                    <br>
                                    <span class="text-xs text-gray-500">${new Date(order.createdAt).toLocaleTimeString()}</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button onclick="modernApp.modules.orders.viewOrder('${order.id}')" 
                                                class="btn-action btn-view" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="modernApp.modules.orders.editOrder('${order.id}')" 
                                                class="btn-action btn-edit" title="Edit Order">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="modernApp.modules.orders.deleteOrder('${order.id}')" 
                                                class="btn-action btn-delete" title="Delete Order">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    getFilteredOrders() {
        let orders = this.app.dataManager.get('orders');
        
        // Apply status filter
        if (this.currentFilter !== 'all') {
            orders = orders.filter(order => order.status === this.currentFilter);
        }
        
        // Apply search filter
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            orders = orders.filter(order => 
                order.orderNumber.toLowerCase().includes(query) ||
                order.customerName.toLowerCase().includes(query)
            );
        }
        
        // Sort by creation date (newest first)
        return orders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    }

    updateFilter(filter) {
        this.currentFilter = filter;
        this.app.refreshCurrentPage();
    }

    updateSearch(query) {
        this.searchQuery = query;
        this.app.refreshCurrentPage();
    }

    async refreshOrders() {
        await this.app.refreshCurrentPage();
        this.app.showToast('Orders refreshed', 'info');
    }

    async viewOrder(orderId) {
        const order = this.app.dataManager.findById('orders', orderId);
        if (!order) {
            this.app.showToast('Order not found', 'error');
            return;
        }

        const modal = this.app.modalManager.create({
            title: `Order Details - ${order.orderNumber}`,
            size: 'large',
            content: this.generateOrderDetailsModal(order)
        });

        modal.show();
    }

    generateOrderDetailsModal(order) {
        return `
            <div class="order-details">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Order Information -->
                    <div>
                        <h4 class="font-semibold mb-3">Order Information</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>Order Number:</span>
                                <span class="font-medium">${order.orderNumber}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Customer:</span>
                                <span class="font-medium">${order.customerName}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Customer Count:</span>
                                <span>${order.customerCount}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Service Type:</span>
                                <span class="capitalize">${order.serviceType}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Payment Method:</span>
                                <span class="capitalize">${order.paymentMethod}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Status:</span>
                                <span class="status-badge status-${order.status}">${order.status}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Date & Time:</span>
                                <span>${new Date(order.createdAt).toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div>
                        <h4 class="font-semibold mb-3">Order Items</h4>
                        <div class="space-y-2">
                            ${order.items.map(item => `
                                <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                    <div>
                                        <p class="font-medium">${item.name}</p>
                                        <p class="text-sm text-gray-600">PKR ${item.price.toLocaleString()} × ${item.quantity}</p>
                                    </div>
                                    <span class="font-semibold">PKR ${(item.price * item.quantity).toLocaleString()}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-semibold mb-3">Order Summary</h4>
                    <div class="space-y-1 text-sm">
                        <div class="flex justify-between">
                            <span>Items Subtotal:</span>
                            <span>PKR ${order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0).toLocaleString()}</span>
                        </div>
                        ${order.perHeadCharges > 0 ? `
                            <div class="flex justify-between">
                                <span>Per Head Charges:</span>
                                <span>PKR ${(order.perHeadCharges * order.customerCount).toLocaleString()}</span>
                            </div>
                        ` : ''}
                        ${order.additionalCharges > 0 ? `
                            <div class="flex justify-between">
                                <span>Additional Charges:</span>
                                <span>PKR ${order.additionalCharges.toLocaleString()}</span>
                            </div>
                        ` : ''}
                        ${order.discount > 0 ? `
                            <div class="flex justify-between">
                                <span>Discount:</span>
                                <span class="text-red-600">-PKR ${order.discount.toLocaleString()}</span>
                            </div>
                        ` : ''}
                        <hr class="my-2">
                        <div class="flex justify-between font-bold text-lg">
                            <span>Total Amount:</span>
                            <span class="text-orange-600">PKR ${order.totalAmount.toLocaleString()}</span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="modernApp.modules.orders.printOrder('${order.id}')" class="btn btn-secondary">
                        <i class="fas fa-print mr-2"></i>Print Receipt
                    </button>
                    <button onclick="modernApp.modules.orders.editOrder('${order.id}')" class="btn btn-primary">
                        <i class="fas fa-edit mr-2"></i>Edit Order
                    </button>
                </div>
            </div>
        `;
    }

    async editOrder(orderId) {
        this.app.showToast('Edit order functionality coming soon', 'info');
    }

    async deleteOrder(orderId) {
        const confirmed = await ModalUtils.confirm(
            'Are you sure you want to delete this order? This action cannot be undone.',
            'Delete Order'
        );

        if (confirmed) {
            try {
                await this.app.dataManager.delete('orders', orderId);
                this.app.showToast('Order deleted successfully', 'success');
                await this.app.refreshCurrentPage();
            } catch (error) {
                console.error('❌ Delete order failed:', error);
                this.app.showToast('Failed to delete order', 'error');
            }
        }
    }

    async printOrder(orderId) {
        this.app.showToast('Print functionality coming soon', 'info');
    }

    async exportOrders() {
        try {
            const orders = this.app.dataManager.get('orders');
            const exportData = {
                orders,
                exportedAt: new Date().toISOString(),
                totalOrders: orders.length,
                totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0)
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-orders-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.app.showToast('Orders exported successfully', 'success');
        } catch (error) {
            console.error('❌ Export failed:', error);
            this.app.showToast('Failed to export orders', 'error');
        }
    }

    async onPageLoad() {
        // Add any page-specific initialization
    }
}

// Export for global use
console.log('✅ OrdersModule class defined, exporting to window...');
window.OrdersModule = OrdersModule;
console.log('✅ OrdersModule exported successfully');
