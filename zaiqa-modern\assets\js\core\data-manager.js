/**
 * Modern Data Manager - Bulletproof Data Persistence & Validation
 * Handles all data operations with comprehensive error handling and validation
 */

console.log('📦 Loading DataManager class...');

class DataManager {
    constructor() {
        this.storagePrefix = 'zaiqa_modern_';
        this.dataSchemas = this.initializeSchemas();
        this.cache = new Map();
        this.observers = new Map();
        this.isInitialized = false;

        console.log('📦 DataManager constructor completed');
    }

    /**
     * Initialize the data manager
     */
    async init() {
        try {
            console.log('🚀 Initializing Modern Data Manager...');
            
            // Initialize data structures
            await this.initializeDataStructures();
            
            // Validate existing data
            await this.validateAllData();
            
            // Setup auto-save
            this.setupAutoSave();
            
            this.isInitialized = true;
            console.log('✅ Data Manager initialized successfully');
            
        } catch (error) {
            console.error('❌ Data Manager initialization failed:', error);
            throw error;
        }
    }

    /**
     * Define data schemas for validation
     */
    initializeSchemas() {
        return {
            orders: {
                id: { type: 'string', required: true },
                orderNumber: { type: 'string', required: true },
                customerName: { type: 'string', default: 'Walk-in Customer' },
                items: { type: 'array', required: true },
                totalAmount: { type: 'number', required: true, min: 0 },
                paymentMethod: { type: 'string', default: 'cash' },
                serviceType: { type: 'string', default: 'dine_in' },
                status: { type: 'string', default: 'completed' },
                customerCount: { type: 'number', default: 1, min: 1 },
                perHeadCharges: { type: 'number', default: 0, min: 0 },
                additionalCharges: { type: 'number', default: 0, min: 0 },
                discount: { type: 'number', default: 0, min: 0 },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            menuItems: {
                id: { type: 'string', required: true },
                name: { type: 'string', required: true },
                category: { type: 'string', required: true },
                price: { type: 'number', required: true, min: 0 },
                description: { type: 'string', default: '' },
                isAvailable: { type: 'boolean', default: true },
                ingredients: { type: 'array', default: [] },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            inventory: {
                id: { type: 'string', required: true },
                name: { type: 'string', required: true },
                quantity: { type: 'number', required: true, min: 0 },
                unit: { type: 'string', required: true },
                unitPrice: { type: 'number', required: true, min: 0 },
                category: { type: 'string', default: 'general' },
                lowStockThreshold: { type: 'number', default: 10, min: 0 },
                supplier: { type: 'string', default: '' },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            expenses: {
                id: { type: 'string', required: true },
                description: { type: 'string', required: true },
                amount: { type: 'number', required: true, min: 0 },
                category: { type: 'string', required: true },
                date: { type: 'string', required: true },
                paymentMethod: { type: 'string', default: 'cash' },
                supplier: { type: 'string', default: '' },
                notes: { type: 'string', default: '' },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            udhars: {
                id: { type: 'string', required: true },
                customerName: { type: 'string', required: true },
                phone: { type: 'string', default: '' },
                totalCredit: { type: 'number', default: 0, min: 0 },
                totalPaid: { type: 'number', default: 0, min: 0 },
                balance: { type: 'number', default: 0 },
                transactions: { type: 'array', default: [] },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            staff: {
                id: { type: 'string', required: true },
                name: { type: 'string', required: true },
                phone: { type: 'string', default: '' },
                position: { type: 'string', required: true },
                salary: { type: 'number', required: true, min: 0 },
                isActive: { type: 'boolean', default: true },
                joinDate: { type: 'string', required: true },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            cashRegister: {
                id: { type: 'string', required: true },
                date: { type: 'string', required: true },
                openingBalance: { type: 'number', required: true, min: 0 },
                closingBalance: { type: 'number', default: 0, min: 0 },
                totalSales: { type: 'number', default: 0, min: 0 },
                totalExpenses: { type: 'number', default: 0, min: 0 },
                notes: { type: 'string', default: '' },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            }
        };
    }

    /**
     * Initialize data structures in localStorage
     */
    async initializeDataStructures() {
        const dataTypes = Object.keys(this.dataSchemas);
        
        for (const dataType of dataTypes) {
            const key = this.getStorageKey(dataType);
            
            if (!localStorage.getItem(key)) {
                localStorage.setItem(key, JSON.stringify([]));
                console.log(`📦 Initialized ${dataType} storage`);
            }
        }
    }

    /**
     * Validate all existing data
     */
    async validateAllData() {
        const dataTypes = Object.keys(this.dataSchemas);
        let totalIssues = 0;
        
        for (const dataType of dataTypes) {
            const data = this.get(dataType);
            const validatedData = [];
            
            for (const item of data) {
                try {
                    const validatedItem = this.validateItem(item, dataType);
                    validatedData.push(validatedItem);
                } catch (error) {
                    console.warn(`⚠️ Invalid ${dataType} item removed:`, error.message);
                    totalIssues++;
                }
            }
            
            // Save cleaned data
            this.set(dataType, validatedData);
        }
        
        if (totalIssues > 0) {
            console.log(`🔧 Data validation completed: ${totalIssues} issues fixed`);
        } else {
            console.log('✅ All data is valid');
        }
    }

    /**
     * Get storage key with prefix
     */
    getStorageKey(dataType) {
        return `${this.storagePrefix}${dataType}`;
    }

    /**
     * Validate a single item against schema
     */
    validateItem(item, dataType) {
        const schema = this.dataSchemas[dataType];
        if (!schema) {
            throw new Error(`Unknown data type: ${dataType}`);
        }

        const validatedItem = {};
        
        // Validate each field
        for (const [field, rules] of Object.entries(schema)) {
            let value = item[field];
            
            // Check required fields
            if (rules.required && (value === undefined || value === null || value === '')) {
                throw new Error(`Required field missing: ${field}`);
            }
            
            // Apply default values
            if (value === undefined && rules.default !== undefined) {
                value = rules.default;
            }
            
            // Type validation
            if (value !== undefined && value !== null) {
                value = this.validateFieldType(value, rules, field);
            }
            
            validatedItem[field] = value;
        }
        
        return validatedItem;
    }

    /**
     * Validate field type and constraints
     */
    validateFieldType(value, rules, fieldName) {
        switch (rules.type) {
            case 'string':
                if (typeof value !== 'string') {
                    value = String(value);
                }
                break;
                
            case 'number':
                const numValue = parseFloat(value);
                if (isNaN(numValue)) {
                    throw new Error(`Invalid number for field: ${fieldName}`);
                }
                if (rules.min !== undefined && numValue < rules.min) {
                    throw new Error(`Value below minimum for field: ${fieldName}`);
                }
                if (rules.max !== undefined && numValue > rules.max) {
                    throw new Error(`Value above maximum for field: ${fieldName}`);
                }
                value = numValue;
                break;
                
            case 'boolean':
                value = Boolean(value);
                break;
                
            case 'array':
                if (!Array.isArray(value)) {
                    value = [];
                }
                break;
                
            default:
                throw new Error(`Unknown field type: ${rules.type}`);
        }
        
        return value;
    }

    /**
     * Get data from storage with caching
     */
    get(dataType) {
        try {
            // Check cache first
            if (this.cache.has(dataType)) {
                return this.cache.get(dataType);
            }
            
            const key = this.getStorageKey(dataType);
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            
            // Cache the data
            this.cache.set(dataType, data);
            
            return data;
        } catch (error) {
            console.error(`❌ Error getting ${dataType}:`, error);
            return [];
        }
    }

    /**
     * Set data to storage with validation
     */
    set(dataType, data) {
        try {
            if (!Array.isArray(data)) {
                throw new Error('Data must be an array');
            }
            
            const key = this.getStorageKey(dataType);
            localStorage.setItem(key, JSON.stringify(data));
            
            // Update cache
            this.cache.set(dataType, data);
            
            // Notify observers
            this.notifyObservers(dataType, data);
            
            return true;
        } catch (error) {
            console.error(`❌ Error setting ${dataType}:`, error);
            return false;
        }
    }

    /**
     * Add new item with validation
     */
    add(dataType, item) {
        try {
            // Generate ID if not provided
            if (!item.id) {
                item.id = this.generateId();
            }
            
            // Add timestamps
            const now = new Date().toISOString();
            item.createdAt = item.createdAt || now;
            item.updatedAt = now;
            
            // Validate item
            const validatedItem = this.validateItem(item, dataType);
            
            // Get current data
            const data = this.get(dataType);
            
            // Check for duplicate ID
            if (data.some(existing => existing.id === validatedItem.id)) {
                throw new Error('Item with this ID already exists');
            }
            
            // Add item
            data.push(validatedItem);
            
            // Save
            this.set(dataType, data);
            
            console.log(`✅ Added new ${dataType}:`, validatedItem.id);
            return validatedItem;
            
        } catch (error) {
            console.error(`❌ Error adding ${dataType}:`, error);
            throw error;
        }
    }

    /**
     * Update existing item
     */
    update(dataType, id, updates) {
        try {
            const data = this.get(dataType);
            const index = data.findIndex(item => item.id === id);
            
            if (index === -1) {
                throw new Error('Item not found');
            }
            
            // Merge updates
            const updatedItem = { ...data[index], ...updates };
            updatedItem.updatedAt = new Date().toISOString();
            
            // Validate updated item
            const validatedItem = this.validateItem(updatedItem, dataType);
            
            // Update in array
            data[index] = validatedItem;
            
            // Save
            this.set(dataType, data);
            
            console.log(`✅ Updated ${dataType}:`, id);
            return validatedItem;
            
        } catch (error) {
            console.error(`❌ Error updating ${dataType}:`, error);
            throw error;
        }
    }

    /**
     * Delete item
     */
    delete(dataType, id) {
        try {
            const data = this.get(dataType);
            const filteredData = data.filter(item => item.id !== id);
            
            if (filteredData.length === data.length) {
                throw new Error('Item not found');
            }
            
            this.set(dataType, filteredData);
            
            console.log(`✅ Deleted ${dataType}:`, id);
            return true;
            
        } catch (error) {
            console.error(`❌ Error deleting ${dataType}:`, error);
            throw error;
        }
    }

    /**
     * Find item by ID
     */
    findById(dataType, id) {
        const data = this.get(dataType);
        return data.find(item => item.id === id);
    }

    /**
     * Find items by criteria
     */
    findBy(dataType, criteria) {
        const data = this.get(dataType);
        return data.filter(item => {
            return Object.entries(criteria).every(([key, value]) => {
                return item[key] === value;
            });
        });
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Subscribe to data changes
     */
    subscribe(dataType, callback) {
        if (!this.observers.has(dataType)) {
            this.observers.set(dataType, []);
        }
        this.observers.get(dataType).push(callback);
    }

    /**
     * Notify observers of data changes
     */
    notifyObservers(dataType, data) {
        if (this.observers.has(dataType)) {
            this.observers.get(dataType).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Observer callback error:', error);
                }
            });
        }
    }

    /**
     * Setup auto-save functionality
     */
    setupAutoSave() {
        // Save data every 30 seconds
        setInterval(() => {
            this.cache.clear(); // Clear cache to force fresh reads
        }, 30000);
    }

    /**
     * Export all data
     */
    exportData() {
        const exportData = {};
        const dataTypes = Object.keys(this.dataSchemas);
        
        for (const dataType of dataTypes) {
            exportData[dataType] = this.get(dataType);
        }
        
        exportData.exportedAt = new Date().toISOString();
        exportData.version = '2.0';
        
        return exportData;
    }

    /**
     * Import data with validation
     */
    async importData(importData) {
        try {
            if (!importData || typeof importData !== 'object') {
                throw new Error('Invalid import data');
            }
            
            const dataTypes = Object.keys(this.dataSchemas);
            
            for (const dataType of dataTypes) {
                if (importData[dataType] && Array.isArray(importData[dataType])) {
                    // Validate each item
                    const validatedData = [];
                    for (const item of importData[dataType]) {
                        try {
                            const validatedItem = this.validateItem(item, dataType);
                            validatedData.push(validatedItem);
                        } catch (error) {
                            console.warn(`Skipping invalid ${dataType} item:`, error.message);
                        }
                    }
                    
                    this.set(dataType, validatedData);
                }
            }
            
            console.log('✅ Data imported successfully');
            return true;
            
        } catch (error) {
            console.error('❌ Data import failed:', error);
            throw error;
        }
    }

    /**
     * Clear all data (with confirmation)
     */
    clearAllData() {
        const dataTypes = Object.keys(this.dataSchemas);
        
        for (const dataType of dataTypes) {
            this.set(dataType, []);
        }
        
        this.cache.clear();
        console.log('🗑️ All data cleared');
    }

    /**
     * Get system statistics
     */
    getSystemStats() {
        const stats = {};
        const dataTypes = Object.keys(this.dataSchemas);
        
        for (const dataType of dataTypes) {
            const data = this.get(dataType);
            stats[dataType] = {
                count: data.length,
                lastUpdated: data.length > 0 ? Math.max(...data.map(item => new Date(item.updatedAt).getTime())) : null
            };
        }
        
        return stats;
    }
}

// Export for global use
console.log('✅ DataManager class defined, exporting to window...');
window.DataManager = DataManager;
console.log('✅ DataManager exported successfully:', typeof window.DataManager);
