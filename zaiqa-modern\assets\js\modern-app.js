/**
 * Modern Restaurant App - Main Application Controller
 * Orchestrates all modules and provides unified interface
 */

class ModernRestaurantApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.modules = {};
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Initializing Modern Zaiqa Restaurant System...');
            
            // Show loading screen
            this.showLoadingScreen();
            
            // Initialize core systems
            await this.initializeCore();
            
            // Initialize modules
            await this.initializeModules();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial page
            await this.loadInitialPage();
            
            // Hide loading screen and show app
            this.hideLoadingScreen();
            
            this.isInitialized = true;
            console.log('✅ Modern Restaurant System initialized successfully');
            
            // Show welcome notification
            this.showToast('Welcome to Modern Zaiqa Restaurant System!', 'success');
            
        } catch (error) {
            console.error('❌ Application initialization failed:', error);
            this.showErrorScreen(error);
        }
    }

    /**
     * Initialize core systems
     */
    async initializeCore() {
        try {
            // Initialize data manager
            this.dataManager = new DataManager();
            await this.dataManager.init();
            
            // Initialize financial engine
            this.financialEngine = new FinancialEngine(this.dataManager);

            // Initialize toast system
            this.toastManager = new ToastManager();

            // Initialize modal system
            this.modalManager = new ModalManager();

            // Initialize sample data if needed
            await Helpers.initializeSampleData(this.dataManager);

            console.log('✅ Core systems initialized');
            
        } catch (error) {
            console.error('❌ Core initialization failed:', error);
            throw error;
        }
    }

    /**
     * Initialize all modules
     */
    async initializeModules() {
        try {
            // Initialize modules
            this.modules = {
                dashboard: new DashboardModule(this),
                pos: new POSModule(this),
                orders: new OrdersModule(this),
                menu: new MenuModule(this),
                inventory: new InventoryModule(this),
                reports: new ReportsModule(this),
                udhar: new UdharModule(this),
                staff: new StaffModule(this),
                expenses: new ExpensesModule(this)
            };
            
            // Initialize each module
            for (const [name, module] of Object.entries(this.modules)) {
                if (module.init) {
                    await module.init();
                }
                console.log(`✅ ${name} module initialized`);
            }
            
        } catch (error) {
            console.error('❌ Module initialization failed:', error);
            throw error;
        }
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Handle navigation clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-navigate]')) {
                e.preventDefault();
                const page = e.target.getAttribute('data-navigate');
                this.navigateTo(page);
            }
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.refreshCurrentPage();
            }
        });

        // Auto-save data every 30 seconds
        setInterval(() => {
            this.autoSave();
        }, 30000);

        // Update stats every 10 seconds
        setInterval(() => {
            this.updateTopBarStats();
        }, 10000);
    }

    /**
     * Load initial page
     */
    async loadInitialPage() {
        await this.navigateTo('dashboard');
    }

    /**
     * Navigate to a specific page
     */
    async navigateTo(pageName) {
        try {
            if (!this.modules[pageName]) {
                throw new Error(`Module ${pageName} not found`);
            }

            // Update navigation state
            this.updateNavigationState(pageName);
            
            // Load page content
            const mainContent = document.getElementById('mainContent');
            if (mainContent) {
                // Show loading state
                mainContent.innerHTML = this.getLoadingHTML();
                
                // Load module content
                const content = await this.modules[pageName].render();
                mainContent.innerHTML = content;
                
                // Initialize module-specific functionality
                if (this.modules[pageName].onPageLoad) {
                    await this.modules[pageName].onPageLoad();
                }
            }
            
            this.currentPage = pageName;
            console.log(`📄 Navigated to ${pageName}`);
            
        } catch (error) {
            console.error(`❌ Navigation to ${pageName} failed:`, error);
            this.showToast(`Failed to load ${pageName} page`, 'error');
        }
    }

    /**
     * Update navigation state
     */
    updateNavigationState(activePage) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to current page
        const activeNavItem = document.querySelector(`[onclick="modernApp.navigateTo('${activePage}')"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.navigateTo('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    this.navigateTo('pos');
                    break;
                case '3':
                    e.preventDefault();
                    this.navigateTo('orders');
                    break;
                case 's':
                    e.preventDefault();
                    this.saveData();
                    break;
            }
        }
        
        // Function key shortcuts
        switch (e.key) {
            case 'F1':
                e.preventDefault();
                this.openPOS();
                break;
            case 'F2':
                e.preventDefault();
                this.openCashRegister();
                break;
            case 'Escape':
                this.closeAllModals();
                break;
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Trigger resize event for modules
        Object.values(this.modules).forEach(module => {
            if (module.onResize) {
                module.onResize();
            }
        });
    }

    /**
     * Refresh current page
     */
    async refreshCurrentPage() {
        if (this.currentPage && this.modules[this.currentPage]) {
            await this.navigateTo(this.currentPage);
        }
    }

    /**
     * Auto-save data
     */
    autoSave() {
        try {
            // Trigger auto-save for all modules
            Object.values(this.modules).forEach(module => {
                if (module.autoSave) {
                    module.autoSave();
                }
            });
            
            console.log('💾 Auto-save completed');
        } catch (error) {
            console.error('❌ Auto-save failed:', error);
        }
    }

    /**
     * Update top bar statistics
     */
    updateTopBarStats() {
        try {
            const today = this.financialEngine.getCurrentBusinessDate();
            const todayRange = this.financialEngine.getBusinessDateRange(today);
            
            // Calculate today's metrics
            const revenue = this.financialEngine.calculateRevenue(todayRange);
            const cashFlow = this.financialEngine.calculateCashFlow(today);
            
            // Update top bar elements
            this.updateElement('todayRevenue', `PKR ${revenue.totalRevenue.toLocaleString()}`);
            this.updateElement('todayOrders', revenue.ordersCount.toString());
            this.updateElement('cashInHand', `PKR ${cashFlow.closingBalance.toLocaleString()}`);
            
        } catch (error) {
            console.error('❌ Stats update failed:', error);
        }
    }

    /**
     * Update element content safely
     */
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }

    /**
     * Open POS system
     */
    async openPOS() {
        if (this.modules.pos && this.modules.pos.openModal) {
            await this.modules.pos.openModal();
        } else {
            await this.navigateTo('pos');
        }
    }

    /**
     * Open cash register
     */
    async openCashRegister() {
        if (this.modules.dashboard && this.modules.dashboard.openCashRegister) {
            await this.modules.dashboard.openCashRegister();
        }
    }

    /**
     * Close all modals
     */
    closeAllModals() {
        if (this.modalManager) {
            this.modalManager.closeAll();
        }
    }

    /**
     * Save data manually
     */
    saveData() {
        try {
            // Force save all data
            this.autoSave();
            this.showToast('Data saved successfully', 'success');
        } catch (error) {
            console.error('❌ Manual save failed:', error);
            this.showToast('Failed to save data', 'error');
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        if (this.toastManager) {
            this.toastManager.show(message, type);
        }
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
        }
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        const app = document.getElementById('app');
        
        if (loadingScreen && app) {
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                app.classList.remove('hidden');
            }, 1000); // Show loading for at least 1 second
        }
    }

    /**
     * Show error screen
     */
    showErrorScreen(error) {
        const app = document.getElementById('app');
        if (app) {
            app.innerHTML = `
                <div class="min-h-screen flex items-center justify-center bg-red-50">
                    <div class="text-center">
                        <div class="text-6xl text-red-500 mb-4">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-red-900 mb-2">System Error</h1>
                        <p class="text-red-700 mb-4">Failed to initialize the restaurant system</p>
                        <p class="text-sm text-red-600 mb-6">${error.message}</p>
                        <button onclick="location.reload()" class="btn btn-primary">
                            <i class="fas fa-redo mr-2"></i>Reload System
                        </button>
                    </div>
                </div>
            `;
            app.classList.remove('hidden');
        }
        
        this.hideLoadingScreen();
    }

    /**
     * Get loading HTML
     */
    getLoadingHTML() {
        return `
            <div class="flex items-center justify-center h-64">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-4 border-orange-500 border-t-transparent mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading...</p>
                </div>
            </div>
        `;
    }

    /**
     * Get system information
     */
    getSystemInfo() {
        return {
            version: '2.0.0',
            initialized: this.isInitialized,
            currentPage: this.currentPage,
            modules: Object.keys(this.modules),
            dataStats: this.dataManager ? this.dataManager.getSystemStats() : null
        };
    }

    /**
     * Export system data
     */
    exportData() {
        if (this.dataManager) {
            return this.dataManager.exportData();
        }
        return null;
    }

    /**
     * Import system data
     */
    async importData(data) {
        if (this.dataManager) {
            await this.dataManager.importData(data);
            await this.refreshCurrentPage();
            this.showToast('Data imported successfully', 'success');
        }
    }

    /**
     * Reset system (with confirmation)
     */
    async resetSystem() {
        const confirmed = confirm('Are you sure you want to reset the entire system? This will delete all data and cannot be undone.');
        
        if (confirmed) {
            try {
                if (this.dataManager) {
                    this.dataManager.clearAllData();
                }
                
                await this.refreshCurrentPage();
                this.showToast('System reset successfully', 'success');
                
            } catch (error) {
                console.error('❌ System reset failed:', error);
                this.showToast('Failed to reset system', 'error');
            }
        }
    }
}

// Global error handler
window.addEventListener('error', (e) => {
    console.error('Global error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});

// Export for global use
window.ModernRestaurantApp = ModernRestaurantApp;
