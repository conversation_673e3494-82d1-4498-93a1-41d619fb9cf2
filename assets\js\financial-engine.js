/**
 * Zaiqa Restaurant Financial Engine - Clean Architecture
 * 100% Accurate Financial Calculations and Data Management
 * Built from scratch with modern JavaScript best practices
 */

class ZaiqaFinancialEngine {
    constructor() {
        this.version = '2.0.0';
        this.initialized = false;
        this.businessDayCutoff = 2; // 2:00 AM cutoff for business day
        
        // Initialize the financial engine
        this.init();
    }

    /**
     * Initialize the financial engine
     */
    init() {
        try {
            console.log('💰 Initializing Zaiqa Financial Engine v' + this.version);
            
            // Validate browser support
            this.validateBrowserSupport();
            
            // Initialize data validation rules
            this.initializeValidationRules();
            
            // Set up error handling
            this.setupErrorHandling();
            
            this.initialized = true;
            console.log('✅ Financial Engine initialized successfully');
            
        } catch (error) {
            console.error('❌ Financial Engine initialization failed:', error);
            throw new Error('Failed to initialize Financial Engine: ' + error.message);
        }
    }

    /**
     * Validate browser support for required features
     */
    validateBrowserSupport() {
        const requiredFeatures = [
            'localStorage',
            'JSON',
            'Date',
            'Number.isFinite',
            'Array.isArray'
        ];

        for (const feature of requiredFeatures) {
            if (!this.checkFeatureSupport(feature)) {
                throw new Error(`Browser does not support required feature: ${feature}`);
            }
        }
    }

    /**
     * Check if a specific feature is supported
     */
    checkFeatureSupport(feature) {
        switch (feature) {
            case 'localStorage':
                return typeof Storage !== 'undefined';
            case 'JSON':
                return typeof JSON !== 'undefined';
            case 'Date':
                return typeof Date !== 'undefined';
            case 'Number.isFinite':
                return typeof Number.isFinite === 'function';
            case 'Array.isArray':
                return typeof Array.isArray === 'function';
            default:
                return false;
        }
    }

    /**
     * Initialize validation rules for financial data
     */
    initializeValidationRules() {
        this.validationRules = {
            amount: {
                required: true,
                type: 'number',
                min: 0,
                max: 999999999.99
            },
            date: {
                required: true,
                type: 'string',
                format: 'YYYY-MM-DD'
            },
            orderNumber: {
                required: true,
                type: 'string',
                minLength: 3,
                maxLength: 50
            },
            customerCount: {
                required: true,
                type: 'number',
                min: 1,
                max: 100
            }
        };
    }

    /**
     * Set up comprehensive error handling
     */
    setupErrorHandling() {
        this.errorHandler = {
            log: (error, context = '') => {
                const timestamp = new Date().toISOString();
                const errorLog = {
                    timestamp,
                    error: error.message || error,
                    context,
                    stack: error.stack || 'No stack trace available'
                };
                
                console.error('💰 Financial Engine Error:', errorLog);
                
                // Store error in localStorage for debugging
                this.storeErrorLog(errorLog);
            },
            
            handle: (error, context = '') => {
                this.errorHandler.log(error, context);
                
                // Return a safe default value based on context
                if (context.includes('calculation')) {
                    return 0;
                } else if (context.includes('array')) {
                    return [];
                } else if (context.includes('object')) {
                    return {};
                } else {
                    return null;
                }
            }
        };
    }

    /**
     * Store error log for debugging purposes
     */
    storeErrorLog(errorLog) {
        try {
            const existingLogs = JSON.parse(localStorage.getItem('zaiqa_error_logs') || '[]');
            existingLogs.push(errorLog);
            
            // Keep only last 100 error logs
            if (existingLogs.length > 100) {
                existingLogs.splice(0, existingLogs.length - 100);
            }
            
            localStorage.setItem('zaiqa_error_logs', JSON.stringify(existingLogs));
        } catch (storageError) {
            console.warn('Could not store error log:', storageError);
        }
    }

    /**
     * Get current business date based on 2:00 AM cutoff
     */
    getCurrentBusinessDate() {
        try {
            const now = new Date();
            const businessDate = new Date(now);
            
            // If it's before 2:00 AM, consider it part of the previous business day
            if (now.getHours() < this.businessDayCutoff) {
                businessDate.setDate(businessDate.getDate() - 1);
            }
            
            return businessDate.toISOString().split('T')[0];
            
        } catch (error) {
            return this.errorHandler.handle(error, 'getCurrentBusinessDate');
        }
    }

    /**
     * Get business date range for a specific date
     */
    getBusinessDateRange(date = null) {
        try {
            const targetDate = date || this.getCurrentBusinessDate();
            const startTime = new Date(targetDate + 'T02:00:00.000Z');
            const endTime = new Date(startTime);
            endTime.setDate(endTime.getDate() + 1);
            endTime.setHours(1, 59, 59, 999);
            
            return {
                start: startTime.toISOString(),
                end: endTime.toISOString(),
                businessDate: targetDate
            };
            
        } catch (error) {
            return this.errorHandler.handle(error, 'getBusinessDateRange');
        }
    }

    /**
     * Validate financial data with comprehensive checks
     */
    validateData(data, rules) {
        try {
            const errors = [];
            
            for (const [field, rule] of Object.entries(rules)) {
                const value = data[field];
                
                // Check required fields
                if (rule.required && (value === undefined || value === null || value === '')) {
                    errors.push(`${field} is required`);
                    continue;
                }
                
                // Skip validation if field is not required and empty
                if (!rule.required && (value === undefined || value === null || value === '')) {
                    continue;
                }
                
                // Type validation
                if (rule.type === 'number' && !Number.isFinite(Number(value))) {
                    errors.push(`${field} must be a valid number`);
                    continue;
                }
                
                if (rule.type === 'string' && typeof value !== 'string') {
                    errors.push(`${field} must be a string`);
                    continue;
                }
                
                // Range validation for numbers
                if (rule.type === 'number') {
                    const numValue = Number(value);
                    if (rule.min !== undefined && numValue < rule.min) {
                        errors.push(`${field} must be at least ${rule.min}`);
                    }
                    if (rule.max !== undefined && numValue > rule.max) {
                        errors.push(`${field} must not exceed ${rule.max}`);
                    }
                }
                
                // Length validation for strings
                if (rule.type === 'string') {
                    if (rule.minLength !== undefined && value.length < rule.minLength) {
                        errors.push(`${field} must be at least ${rule.minLength} characters`);
                    }
                    if (rule.maxLength !== undefined && value.length > rule.maxLength) {
                        errors.push(`${field} must not exceed ${rule.maxLength} characters`);
                    }
                }
                
                // Date format validation
                if (rule.format === 'YYYY-MM-DD') {
                    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                    if (!dateRegex.test(value)) {
                        errors.push(`${field} must be in YYYY-MM-DD format`);
                    }
                }
            }
            
            return {
                isValid: errors.length === 0,
                errors: errors
            };
            
        } catch (error) {
            return this.errorHandler.handle(error, 'validateData');
        }
    }

    /**
     * Perform safe mathematical calculations with precision handling
     */
    calculate(operation, ...values) {
        try {
            // Validate all values are numbers
            const numbers = values.map(val => {
                const num = Number(val);
                if (!Number.isFinite(num)) {
                    throw new Error(`Invalid number: ${val}`);
                }
                return num;
            });
            
            let result;
            
            switch (operation) {
                case 'add':
                    result = numbers.reduce((sum, num) => sum + num, 0);
                    break;
                    
                case 'subtract':
                    result = numbers.reduce((diff, num, index) => 
                        index === 0 ? num : diff - num
                    );
                    break;
                    
                case 'multiply':
                    result = numbers.reduce((product, num) => product * num, 1);
                    break;
                    
                case 'divide':
                    if (numbers.length !== 2) {
                        throw new Error('Division requires exactly 2 numbers');
                    }
                    if (numbers[1] === 0) {
                        throw new Error('Division by zero');
                    }
                    result = numbers[0] / numbers[1];
                    break;
                    
                case 'percentage':
                    if (numbers.length !== 2) {
                        throw new Error('Percentage calculation requires exactly 2 numbers');
                    }
                    result = (numbers[0] / numbers[1]) * 100;
                    break;
                    
                default:
                    throw new Error(`Unknown operation: ${operation}`);
            }
            
            // Round to 2 decimal places to avoid floating point precision issues
            return Math.round(result * 100) / 100;
            
        } catch (error) {
            return this.errorHandler.handle(error, `calculate_${operation}`);
        }
    }

    /**
     * Check if the financial engine is properly initialized
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * Get financial engine status and health check
     */
    getStatus() {
        return {
            version: this.version,
            initialized: this.initialized,
            businessDayCutoff: this.businessDayCutoff,
            currentBusinessDate: this.getCurrentBusinessDate(),
            timestamp: new Date().toISOString()
        };
    }
}

// Export the financial engine for global use
window.ZaiqaFinancialEngine = ZaiqaFinancialEngine;
