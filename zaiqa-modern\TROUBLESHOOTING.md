# 🔧 Troubleshooting Guide - Zaiqa Restaurant System

## 🚨 Common Issues & Solutions

### Issue 1: "DataManager is not defined" Error

**Symptoms:**
- System shows "Failed to initialize" error
- Missing required classes error
- Console shows class loading errors

**Solutions:**
1. **Check File Paths**
   ```
   Verify all files exist in correct locations:
   - assets/js/core/data-manager.js
   - assets/js/core/financial-engine.js
   - All module files in assets/js/modules/
   ```

2. **Clear Browser Cache**
   ```
   Press Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
   Or manually clear cache in browser settings
   ```

3. **Test Class Loading**
   ```
   Open: test.html
   Check: All classes show ✅ LOADED
   If any show ❌ MISSING, check file paths
   ```

4. **Check Browser Console**
   ```
   Press F12 → Console tab
   Look for JavaScript errors
   Fix any syntax errors shown
   ```

### Issue 2: Sample Data Not Loading

**Symptoms:**
- Dashboard shows empty metrics
- No menu items in POS
- Orders page is empty

**Solutions:**
1. **Reset Sample Data**
   ```javascript
   // Open browser console (F12) and run:
   localStorage.clear();
   location.reload();
   ```

2. **Manual Data Initialization**
   ```javascript
   // In browser console:
   Helpers.initializeSampleData(modernApp.dataManager);
   location.reload();
   ```

3. **Check localStorage**
   ```javascript
   // In browser console:
   console.log(localStorage);
   // Should show zaiqa_modern_* keys
   ```

### Issue 3: POS System Not Working

**Symptoms:**
- Menu items don't appear
- Can't add items to cart
- Order processing fails

**Solutions:**
1. **Check Menu Data**
   ```javascript
   // In browser console:
   console.log(modernApp.dataManager.get('menuItems'));
   // Should show array of menu items
   ```

2. **Verify POS Module**
   ```javascript
   // In browser console:
   console.log(typeof POSModule);
   // Should show "function"
   ```

3. **Reset POS State**
   ```
   Navigate away from POS and back
   Or refresh the page
   ```

### Issue 4: Charts Not Displaying

**Symptoms:**
- Dashboard charts are blank
- Chart.js errors in console

**Solutions:**
1. **Check Chart.js Loading**
   ```javascript
   // In browser console:
   console.log(typeof Chart);
   // Should show "function"
   ```

2. **Verify Canvas Elements**
   ```
   Check if chart canvas elements exist in DOM
   Look for <canvas id="revenueChart"> etc.
   ```

3. **Check Data for Charts**
   ```javascript
   // In browser console:
   const analytics = modernApp.financialEngine.calculateAnalytics();
   console.log(analytics);
   ```

### Issue 5: Responsive Design Issues

**Symptoms:**
- Layout broken on mobile
- Elements overlapping
- Sidebar not working

**Solutions:**
1. **Check Viewport Meta Tag**
   ```html
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   ```

2. **Verify CSS Loading**
   ```
   Check if all CSS files are loading:
   - assets/css/modern-style.css
   - assets/css/components.css
   - assets/css/animations.css
   ```

3. **Test Different Screen Sizes**
   ```
   Use browser dev tools (F12)
   Toggle device toolbar
   Test various screen sizes
   ```

## 🛠️ Advanced Troubleshooting

### Complete System Reset

If all else fails, perform a complete reset:

1. **Backup Current Data** (if needed)
   ```javascript
   const backup = modernApp.dataManager.exportData();
   console.log(JSON.stringify(backup));
   // Copy the output to save your data
   ```

2. **Clear All Data**
   ```javascript
   localStorage.clear();
   sessionStorage.clear();
   ```

3. **Hard Refresh**
   ```
   Press Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)
   ```

4. **Verify Fresh Start**
   ```
   Open test.html first
   Ensure all classes load
   Then open index.html
   ```

### Browser Compatibility Issues

**Supported Browsers:**
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

**Unsupported:**
- ❌ Internet Explorer
- ❌ Very old browser versions

**If using unsupported browser:**
1. Update to latest version
2. Try different browser
3. Check console for specific errors

### File Permission Issues

**On Windows:**
1. Right-click folder → Properties
2. Security tab → Edit
3. Give full control to your user

**On Mac/Linux:**
```bash
chmod -R 755 zaiqa-modern/
```

### Local Server Issues

**Python Server Not Starting:**
1. Check Python installation: `python --version`
2. Try different port: `python -m http.server 8000`
3. Check for port conflicts
4. Run as administrator if needed

**Alternative Servers:**
```bash
# Node.js
npx serve .

# PHP
php -S localhost:3000

# Live Server (VS Code extension)
Right-click index.html → Open with Live Server
```

## 📞 Getting Help

### Before Asking for Help

1. **Check Browser Console** (F12 → Console)
2. **Run test.html** to verify class loading
3. **Try different browser**
4. **Clear cache and reload**
5. **Check file permissions**

### Information to Provide

When reporting issues, include:
- Browser name and version
- Operating system
- Error messages from console
- Steps to reproduce the issue
- Screenshot of the problem

### Self-Diagnosis Checklist

- [ ] All files exist in correct locations
- [ ] Browser console shows no errors
- [ ] test.html shows all classes loaded
- [ ] Sample data appears in localStorage
- [ ] Charts and components render properly
- [ ] Navigation works between pages
- [ ] POS system can create orders
- [ ] Data persists after page reload

## 🎯 Prevention Tips

1. **Regular Backups**
   ```javascript
   // Export data weekly
   const backup = modernApp.dataManager.exportData();
   // Save to file
   ```

2. **Browser Updates**
   - Keep browser updated
   - Clear cache regularly
   - Disable conflicting extensions

3. **File Integrity**
   - Don't modify core files unless needed
   - Keep backup of working version
   - Test changes in development first

4. **Performance Monitoring**
   - Watch browser console for warnings
   - Monitor localStorage usage
   - Check for memory leaks in long sessions

---

**🔧 Most issues can be resolved by clearing cache and reloading the page!**
