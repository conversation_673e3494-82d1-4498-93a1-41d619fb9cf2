/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* Color Variables */
:root {
    --primary-color: #D97706;
    --primary-dark: #92400E;
    --primary-light: #FED7AA;
    --success-color: #059669;
    --warning-color: #D97706;
    --error-color: #DC2626;
    --info-color: #3b82f6;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --white: #ffffff;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --gray-50: #1e293b;
    --gray-100: #334155;
    --gray-200: #475569;
    --gray-300: #64748b;
    --gray-400: #94a3b8;
    --gray-500: #cbd5e1;
    --gray-600: #e2e8f0;
    --gray-700: #f1f5f9;
    --gray-800: #f8fafc;
    --gray-900: #ffffff;

    --white: #1e293b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] body {
    background-color: #0f172a;
    color: #f1f5f9;
}

/* Login Screen */
.login-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    padding: 2rem;
}

.login-container {
    background: var(--white);
    padding: 3rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.login-header .logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.login-header h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
}

.login-header p {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    width: 100%;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    color: var(--gray-600);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover {
    background-color: var(--gray-50);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.demo-credentials {
    background-color: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.demo-credentials h4 {
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.credentials-grid {
    display: grid;
    gap: 0.5rem;
}

.credential-item {
    font-size: 0.75rem;
    color: var(--gray-600);
}

/* Main Application Layout */
.main-app {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: var(--white);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-header .logo i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.sidebar-header .logo span {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
}

.sidebar-toggle:hover {
    background-color: var(--gray-100);
}

.user-info {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--primary-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
}

.user-role {
    font-size: 0.75rem;
    color: var(--gray-500);
    text-transform: capitalize;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s;
}

.sidebar-nav .nav-link:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
}

.sidebar-nav .nav-link.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.sidebar-nav .nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.header-left p {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.header-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.text-success {
    color: var(--success-color);
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

[data-theme="dark"] .theme-toggle:hover {
    background-color: var(--gray-200);
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.notification-btn:hover {
    background-color: var(--gray-100);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--error-color);
    color: var(--white);
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 1.25rem;
    text-align: center;
}

/* Page Content */
.page-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Dashboard Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-light);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Inventory Filters */
.inventory-filters {
    background-color: var(--white);
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.filter-section {
    padding: 1.5rem;
}

.filter-section h4 {
    color: var(--gray-900);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-section h4 i {
    color: var(--primary-color);
}

.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: var(--gray-50);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.results-count {
    font-weight: 500;
}

.category-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.stock-amount {
    font-weight: 600;
}

.stock-amount.in-stock {
    color: var(--success-color);
}

.stock-amount.low-stock {
    color: var(--warning-color);
}

.stock-amount.out-of-stock {
    color: var(--error-color);
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.in-stock {
    background-color: #dcfce7;
    color: var(--success-color);
}

.status-badge.low-stock {
    background-color: #fef3c7;
    color: var(--warning-color);
}

.status-badge.out-of-stock {
    background-color: #fee2e2;
    color: var(--error-color);
}

.inventory-row.low-stock {
    background-color: #fefbf3;
}

.inventory-row.out-of-stock {
    background-color: #fef2f2;
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: var(--gray-500);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--gray-400);
}

.no-results h4 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

/* Full Page Report Modal */
.full-page-report {
    z-index: 1000;
}

.extra-large-modal {
    max-width: 90vw;
    max-height: 90vh;
    width: 1200px;
}

.report-period-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.period-badge {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.date-range {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.report-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.summary-card.revenue {
    border-left: 4px solid var(--success-color);
}

.summary-card.expenses {
    border-left: 4px solid var(--error-color);
}

.summary-card.profit.positive {
    border-left: 4px solid var(--success-color);
}

.summary-card.profit.negative {
    border-left: 4px solid var(--error-color);
}

.summary-card.customers {
    border-left: 4px solid var(--info-color);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.summary-card.revenue .card-icon {
    background-color: #dcfce7;
    color: var(--success-color);
}

.summary-card.expenses .card-icon {
    background-color: #fee2e2;
    color: var(--error-color);
}

.summary-card.profit.positive .card-icon {
    background-color: #dcfce7;
    color: var(--success-color);
}

.summary-card.profit.negative .card-icon {
    background-color: #fee2e2;
    color: var(--error-color);
}

.summary-card.customers .card-icon {
    background-color: #dbeafe;
    color: var(--info-color);
}

.card-content h4 {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.card-content .amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.card-content small {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.report-section {
    margin-bottom: 2rem;
}

.report-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.report-section h4 i {
    color: var(--primary-color);
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.report-table th,
.report-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.report-table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
}

.rank-badge {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.report-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.orders-list,
.expenses-list {
    max-height: 400px;
    overflow-y: auto;
}

.order-item,
.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--white);
}

.order-item:last-child,
.expense-item:last-child {
    border-bottom: none;
}

.order-info,
.expense-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.order-number,
.expense-name {
    font-weight: 500;
    color: var(--gray-900);
}

.order-time,
.expense-date {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.order-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.order-amount,
.expense-amount {
    font-weight: 600;
    color: var(--gray-900);
}

.order-items {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.report-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

/* Full Page Report Styles */
.full-page-report-container {
    min-height: 100vh;
    background-color: var(--gray-50);
    padding: 2rem;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
}

.report-header-left {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.back-to-reports {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background-color: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
    white-space: nowrap;
}

.back-to-reports:hover {
    background-color: var(--gray-200);
    color: var(--gray-900);
}

.report-title-section h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.report-title-section h2 i {
    color: var(--primary-color);
}

.report-header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.report-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* Enhanced Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-content {
    background-color: var(--white);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-header h3 i {
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: color 0.2s;
}

.modal-close:hover {
    color: var(--gray-600);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.form-control {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background-color: var(--white);
    color: var(--gray-900);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
}

.form-control:disabled {
    background-color: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

/* Button Enhancements */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    color: var(--gray-600);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--gray-50);
    color: var(--gray-900);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-success:hover:not(:disabled) {
    background-color: #047857;
}

.btn-danger {
    background-color: var(--error-color);
    color: var(--white);
}

.btn-danger:hover:not(:disabled) {
    background-color: #b91c1c;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
    border-radius: 0.25rem;
}

.btn-error {
    background-color: var(--error-color);
    color: var(--white);
    border: 1px solid var(--error-color);
}

.btn-error:hover:not(:disabled) {
    background-color: #b91c1c;
    border-color: #b91c1c;
}

/* Yesterday Data Management Styles */
.yesterday-management-tabs {
    width: 100%;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid var(--gray-200);
    margin-bottom: 2rem;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-weight: 500;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    color: var(--primary-color);
    background-color: var(--gray-50);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: var(--gray-50);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.yesterday-overview {
    padding: 1rem 0;
}

.overview-header {
    text-align: center;
    margin-bottom: 2rem;
}

.overview-header h3 {
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-color);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.25rem;
}

.stat-content h4 {
    margin: 0 0 0.5rem 0;
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-value {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.stat-content small {
    color: var(--gray-500);
    font-size: 0.75rem;
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--error-color) !important;
}

.data-tables {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.table-section h4 {
    margin-bottom: 1rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.table-container {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.data-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.data-table td {
    color: var(--gray-900);
    font-size: 0.875rem;
}

.data-table tr:hover {
    background: var(--gray-50);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.form-header h3 {
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.editable-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.editable-item {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    padding: 1rem;
    display: flex;
    justify-content: between;
    align-items: center;
    transition: all 0.2s;
}

.editable-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(251, 146, 60, 0.1);
}

.item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.item-info strong {
    color: var(--gray-900);
    font-weight: 600;
}

.item-info .amount {
    color: var(--primary-color);
    font-weight: 600;
}

.item-info .customer,
.item-info .category,
.item-info .time {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
}

.edit-sections {
    display: grid;
    gap: 2rem;
}

.edit-section {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.edit-section h4 {
    margin-bottom: 1rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .data-tables {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .tab-buttons {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }
}

/* Cash Management Styles */
.cash-management {
    padding: 1rem 0;
}

.cash-sections {
    display: grid;
    gap: 2rem;
    margin-top: 2rem;
}

.cash-section {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.cash-section h4 {
    margin-bottom: 1rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cash-info {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.cash-info p {
    margin: 0.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.withdrawals-table {
    margin: 1rem 0;
}

/* Day Finalization Styles */
.day-finalization {
    padding: 1rem 0;
}

.finalization-summary {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.summary-item .label {
    font-weight: 500;
    color: var(--gray-700);
}

.summary-item .value {
    font-weight: 700;
    color: var(--gray-900);
}

.finalization-actions {
    display: grid;
    gap: 2rem;
}

.action-section {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.action-section h4 {
    margin-bottom: 1rem;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.closure-options,
.advanced-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.closure-options .btn,
.advanced-options .btn {
    flex: 1;
    min-width: 150px;
}

.debug-info {
    font-family: monospace;
    font-size: 0.75rem;
    color: var(--gray-600);
}

/* Enhanced Button Styles */
.btn-warning {
    background-color: #f59e0b;
    color: var(--white);
    border: 1px solid #f59e0b;
}

.btn-warning:hover:not(:disabled) {
    background-color: #d97706;
    border-color: #d97706;
}

.btn-info {
    background-color: #3b82f6;
    color: var(--white);
    border: 1px solid #3b82f6;
}

.btn-info:hover:not(:disabled) {
    background-color: #2563eb;
    border-color: #2563eb;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .cash-sections {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .closure-options,
    .advanced-options {
        flex-direction: column;
    }

    .closure-options .btn,
    .advanced-options .btn {
        min-width: auto;
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Category Management Styles */
.color-picker-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.color-input {
    width: 50px;
    height: 40px;
    border: 1px solid var(--gray-300);
    border-radius: 0.25rem;
    cursor: pointer;
}

.color-preview {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid var(--gray-300);
}

.existing-categories {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.existing-categories h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: var(--white);
}

.category-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.category-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid var(--gray-300);
}

.category-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.category-name {
    font-weight: 500;
    color: var(--gray-900);
}

.category-description {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.category-actions {
    display: flex;
    gap: 0.5rem;
}

/* Custom Report Styles */
.report-sections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.report-section {
    background-color: var(--white);
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.section-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header h3 i {
    color: var(--primary-color);
}

.section-badge {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.section-content {
    padding: 1.5rem;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
}

.report-table th,
.report-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.report-table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.report-table tbody tr:hover {
    background-color: var(--gray-50);
}

.rank-badge {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.profit-positive {
    color: var(--success-color);
    font-weight: 600;
}

.profit-negative {
    color: var(--error-color);
    font-weight: 600;
}

.margin-good {
    color: var(--success-color);
    font-weight: 600;
}

.margin-ok {
    color: var(--warning-color);
    font-weight: 600;
}

.margin-poor {
    color: var(--error-color);
    font-weight: 600;
}

.performance-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.performance-badge.excellent {
    background-color: #dcfce7;
    color: var(--success-color);
}

.performance-badge.good {
    background-color: #fef3c7;
    color: var(--warning-color);
}

.performance-badge.poor {
    background-color: #fee2e2;
    color: var(--error-color);
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.trend-indicator.up {
    color: var(--success-color);
}

.trend-indicator.down {
    color: var(--error-color);
}

.trend-indicator.same {
    color: var(--gray-500);
}

/* KPIs Grid */
.kpis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.kpi-item {
    background-color: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
}

.kpi-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.kpi-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.kpi-value.positive {
    color: var(--success-color);
}

.kpi-value.negative {
    color: var(--error-color);
}

.kpi-value.good {
    color: var(--success-color);
}

.kpi-value.warning {
    color: var(--warning-color);
}

.kpi-value.danger {
    color: var(--error-color);
}

/* Financial Health Grid */
.financial-health-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.health-item {
    background-color: var(--gray-50);
    padding: 1.5rem;
    border-radius: 0.5rem;
    text-align: center;
}

.health-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.health-value {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.health-status {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Usage Record Details Modal */
.record-details {
    max-height: 70vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.detail-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.detail-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detail-section h4 i {
    color: var(--primary-color);
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detail-item span {
    font-weight: 500;
    color: var(--gray-900);
}

.quantity-used.positive {
    color: var(--success-color);
}

.quantity-used.negative {
    color: var(--error-color);
}

.item-id,
.record-id,
.order-id {
    font-family: monospace;
    background-color: var(--gray-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .full-page-report-container {
        padding: 1rem;
    }

    .report-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .report-header-left {
        flex-direction: column;
        gap: 1rem;
    }

    .report-header-actions {
        justify-content: stretch;
    }

    .report-header-actions .btn {
        flex: 1;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .filter-controls {
        grid-template-columns: 1fr;
    }

    .report-grid {
        grid-template-columns: 1fr;
    }

    .report-summary-cards {
        grid-template-columns: 1fr;
    }

    .report-sections-grid {
        grid-template-columns: 1fr;
    }

    .kpis-grid {
        grid-template-columns: 1fr;
    }

    .financial-health-grid {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }
}

/* Item Usage History Modal Styles */
.item-info-summary {
    background-color: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stock-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--success-color);
}

.usage-count {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-color);
}

.price-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.usage-history-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.usage-history-section h4 i {
    color: var(--primary-color);
}

.usage-records-table {
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background-color: var(--gray-50);
}

.date-time {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.date {
    font-weight: 500;
    color: var(--gray-900);
    font-size: 0.875rem;
}

.time {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.quantity-used {
    font-weight: 600;
    color: var(--error-color);
}

.menu-item {
    font-weight: 500;
    color: var(--gray-900);
}

.order-number {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--gray-600);
    background-color: var(--gray-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.usage-type-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.usage-type-badge.order_confirmation {
    background-color: #dcfce7;
    color: var(--success-color);
}

.usage-type-badge.manual {
    background-color: #fef3c7;
    color: var(--warning-color);
}

.usage-type-badge.adjustment {
    background-color: #dbeafe;
    color: var(--info-color);
}

.usage-type-badge.waste {
    background-color: #fee2e2;
    color: var(--error-color);
}

.usage-type-badge.return {
    background-color: #f3e8ff;
    color: #7c3aed;
}

.no-usage-records {
    text-align: center;
    padding: 3rem;
    color: var(--gray-500);
}

.no-usage-records i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--gray-400);
}

.no-usage-records h5 {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* Enhanced Table Styles */
.inventory-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.inventory-table th,
.inventory-table td {
    padding: 1rem 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.inventory-table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.inventory-table tbody tr:hover {
    background-color: var(--gray-50);
}

.inventory-table tbody tr:last-child td {
    border-bottom: none;
}

/* Text Utilities */
.text-muted {
    color: var(--gray-500);
    font-size: 0.875rem;
}

/* Success/Error States */
.text-success {
    color: var(--success-color);
}

.text-error {
    color: var(--error-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-info {
    color: var(--info-color);
}
}

/* Enhanced Table Management Styles */
.table-view-container {
    position: relative;
    min-height: 500px;
}

.table-map-view, .table-grid-view {
    display: none;
}

.table-map-view.active, .table-grid-view.active {
    display: block;
}

/* Interactive Table Map */
.restaurant-floor {
    position: relative;
    width: 100%;
    height: 600px;
    background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    border: 2px solid var(--gray-200);
    border-radius: 1rem;
    overflow: hidden;
}

.floor-layout {
    position: relative;
    width: 100%;
    height: 100%;
}

.interactive-table {
    position: absolute;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.interactive-table:hover {
    transform: scale(1.05);
    z-index: 20;
}

.interactive-table.dragging {
    opacity: 0.7;
    transform: rotate(5deg);
}

.table-visual {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.table-top {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: var(--shadow);
    border: 3px solid;
}

.table-top.small {
    width: 60px;
    height: 60px;
    font-size: 0.75rem;
}

.table-top.medium {
    width: 80px;
    height: 80px;
    font-size: 0.875rem;
}

.table-top.large {
    width: 100px;
    height: 100px;
    font-size: 1rem;
}

/* Table Status Colors */
.interactive-table.available .table-top {
    background-color: #dcfce7;
    border-color: #16a34a;
    color: #15803d;
}

.interactive-table.occupied .table-top {
    background-color: #fecaca;
    border-color: #dc2626;
    color: #b91c1c;
}

.interactive-table.reserved .table-top {
    background-color: #fef3c7;
    border-color: #d97706;
    color: #92400e;
}

.interactive-table.cleaning .table-top {
    background-color: #dbeafe;
    border-color: #3b82f6;
    color: #1d4ed8;
}

.table-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: var(--gray-600);
}

.table-customers {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    max-width: 100px;
    justify-content: center;
}

.customer-icon {
    width: 16px;
    height: 16px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.625rem;
}

/* Pricing Info Banner */
.pricing-info-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
}

.pricing-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pricing-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.pricing-item i {
    font-size: 1.25rem;
    opacity: 0.9;
}

/* Enhanced Menu Items */
.menu-item-card {
    background: var(--white);
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.menu-item-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.menu-item-pricing {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

.pricing-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.price-label {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
}

.price-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
}

.price-value.takeaway {
    color: var(--success-color);
}

.price-value.dine-in {
    color: var(--primary-color);
}

.cold-drink-badge {
    background-color: #dbeafe;
    color: #1d4ed8;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Recipe Management */
.recipe-ingredients {
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--gray-50);
    border-radius: 0.5rem;
}

.ingredient-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.ingredient-item:last-child {
    border-bottom: none;
}

.ingredient-name {
    font-weight: 500;
    color: var(--gray-900);
}

.ingredient-quantity {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Enhanced POS System Styles */
.service-type-section {
    background-color: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.service-type-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.service-type-header label {
    font-weight: 600;
    color: var(--gray-700);
}

.customer-count-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.customer-count-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.customer-count-controls input {
    width: 80px;
    text-align: center;
}

.per-head-charge-info {
    margin-top: 0.75rem;
}

.per-head-charge-info label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
    cursor: pointer;
}

.pos-menu-item {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.pos-menu-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.pos-menu-item.unavailable {
    opacity: 0.6;
    cursor: not-allowed;
}

.pos-menu-item.unavailable:hover {
    transform: none;
    box-shadow: var(--shadow);
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.menu-item-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.menu-item-description {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.menu-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.5rem;
}

.prep-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.unavailable-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.unavailable-reason {
    color: var(--error-color);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Bill Management Enhancements */
.bill-summary {
    background-color: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.bill-summary h4 {
    margin: 0 0 0.75rem 0;
    color: var(--gray-900);
}

.bill-line-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.bill-line-item:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.bill-line-item.total {
    border-top: 2px solid var(--primary-color);
    padding-top: 0.75rem;
    margin-top: 0.5rem;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .table-map-view {
        height: 400px;
    }

    .interactive-table .table-top.large {
        width: 70px;
        height: 70px;
        font-size: 0.875rem;
    }

    .interactive-table .table-top.medium {
        width: 60px;
        height: 60px;
        font-size: 0.75rem;
    }

    .interactive-table .table-top.small {
        width: 50px;
        height: 50px;
        font-size: 0.625rem;
    }

    .pricing-info {
        flex-direction: column;
        gap: 0.75rem;
    }

    .menu-item-pricing {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .customer-count-controls {
        justify-content: center;
    }
}

/* ========================================
   REPORTS PAGE - CLEAN ARCHITECTURE STYLES
   ======================================== */

.reports-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--gray-200);
}

.header-content h1.page-title {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-subtitle {
    color: var(--gray-600);
    margin: 5px 0 0 0;
    font-size: 1.1rem;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Period Selection */
.period-selection-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.period-selection-card h3 {
    margin: 0 0 20px 0;
    color: var(--gray-900);
    font-size: 1.3rem;
}

.period-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.period-btn {
    padding: 12px 24px;
    border: 2px solid var(--gray-300);
    background: white;
    color: var(--gray-700);
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.period-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.period-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.custom-date-inputs {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-200);
}

.date-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.date-input-group label {
    font-weight: 500;
    color: var(--gray-700);
}

.date-input-group input {
    padding: 10px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
}

/* KPI Grid */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.kpi-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.kpi-card:hover {
    transform: translateY(-2px);
}

.kpi-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.kpi-card.revenue .kpi-icon { background: linear-gradient(135deg, #10b981, #059669); }
.kpi-card.expenses .kpi-icon { background: linear-gradient(135deg, #ef4444, #dc2626); }
.kpi-card.profit.positive .kpi-icon { background: linear-gradient(135deg, #22c55e, #16a34a); }
.kpi-card.profit.negative .kpi-icon { background: linear-gradient(135deg, #f59e0b, #d97706); }
.kpi-card.udhar .kpi-icon { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.kpi-card.inventory .kpi-icon { background: linear-gradient(135deg, #06b6d4, #0891b2); }
.kpi-card.average .kpi-icon { background: linear-gradient(135deg, #6366f1, #4f46e5); }

.kpi-content h4 {
    margin: 0 0 8px 0;
    color: var(--gray-600);
    font-size: 14px;
    font-weight: 500;
}

.kpi-value {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-900);
}

.kpi-subtitle {
    color: var(--gray-600);
    font-size: 12px;
}

/* Enhanced Form Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.radio-group {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.radio-label input[type="radio"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.inventory-options {
    background-color: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
    margin-top: 0.75rem;
}

.new-inventory-fields {
    background-color: var(--white);
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
    margin-top: 0.75rem;
}

/* Cash Register Styles */
.cash-register-summary {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.cash-flow-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.cash-card {
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-radius: 0.5rem;
    padding: 1rem;
    border: 2px solid var(--gray-200);
    transition: all 0.3s ease;
}

.cash-card.recorded {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #dcfce7, var(--white));
}

.cash-card.pending {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, #fef3c7, var(--white));
}

.cash-card.sales {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #dbeafe, var(--white));
}

.cash-card.expected {
    border-color: var(--info-color);
    background: linear-gradient(135deg, #e0f2fe, var(--white));
}

.cash-card .card-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.cash-card .amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.cash-card .timestamp {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.cash-difference-summary {
    margin-top: 1.5rem;
}

.difference-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px solid;
}

.difference-card.perfect {
    background-color: #dcfce7;
    border-color: var(--success-color);
    color: var(--success-dark);
}

.difference-card.excess {
    background-color: #fef3c7;
    border-color: var(--warning-color);
    color: var(--warning-dark);
}

.difference-card.short {
    background-color: #fecaca;
    border-color: var(--error-color);
    color: var(--error-dark);
}

.difference-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.difference-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.125rem;
}

.difference-content p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
}

.cash-status-card.no-data {
    text-align: center;
    padding: 2rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
    border: 2px dashed var(--gray-300);
}

.cash-status-card .status-icon {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.cash-status-card h4 {
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.cash-status-card p {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
}

/* Cash Register Report Styles */
.large-modal .modal-content {
    max-width: 90vw;
    width: 1200px;
}

.report-filters {
    background-color: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.filter-row {
    display: flex;
    align-items: end;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
}

.cash-register-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
}

.cash-register-table th,
.cash-register-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.cash-register-table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.cash-register-table tr.completed {
    background-color: rgba(34, 197, 94, 0.05);
}

.cash-register-table tr.incomplete {
    background-color: rgba(251, 191, 36, 0.05);
}

.cash-register-table .missing {
    color: var(--gray-400);
    font-style: italic;
}

.cash-register-table .difference.perfect {
    color: var(--success-color);
    font-weight: 600;
}

.cash-register-table .difference.excess {
    color: var(--warning-color);
    font-weight: 600;
}

.cash-register-table .difference.short {
    color: var(--error-color);
    font-weight: 600;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.complete {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.status-badge.incomplete {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.report-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-stat {
    background: var(--white);
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.summary-stat .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-light);
    color: var(--primary-color);
    font-size: 1.25rem;
}

.summary-stat .stat-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.25rem;
    color: var(--gray-900);
}

.summary-stat .stat-content p {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.summary-stat .stat-content small {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.no-data-message {
    text-align: center;
    padding: 2rem;
    color: var(--gray-500);
}

.no-data-message i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Cash Register Actions */
.cash-register-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.cash-summary {
    background-color: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.expected-balance {
    font-weight: 600;
    color: var(--primary-color);
    border-top: 1px solid var(--primary-color);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

.cash-difference {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--info-light);
    border: 1px solid var(--info-color);
}

.difference-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
}

.difference-amount.positive {
    color: var(--warning-color);
}

.difference-amount.negative {
    color: var(--error-color);
}

/* Low Stock Alert Styles */
.alert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: var(--gray-50);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.alert-info .item-name {
    font-weight: 600;
    color: var(--gray-900);
}

.alert-info .item-stock {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.alert-info .min-stock {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.alert-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.alert-badge.low {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.alert-badge.critical {
    background-color: var(--error-light);
    color: var(--error-dark);
}

.alert-badge.out {
    background-color: var(--gray-800);
    color: var(--white);
}

/* Equipment Management Styles */
.equipment-summary {
    margin-bottom: 2rem;
}

.equipment-filters {
    margin-bottom: 2rem;
}

.filters-card {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    padding: 1.5rem;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.filters-header h4 {
    margin: 0;
    color: var(--gray-900);
}

.filters-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.equipment-list {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.equipment-table {
    padding: 1.5rem;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.table-header h4 {
    margin: 0;
    color: var(--gray-900);
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.equipment-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.equipment-info strong {
    color: var(--gray-900);
    font-weight: 600;
}

.equipment-info small {
    color: var(--gray-500);
    font-size: 0.8rem;
}

.quantity-display {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.quantity-display small {
    color: var(--gray-500);
    font-size: 0.75rem;
}

.condition-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
}

.condition-badge.good {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.condition-badge.medium {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.condition-badge.warning {
    background-color: var(--warning-color);
    color: var(--white);
}

.condition-badge.critical {
    background-color: var(--error-light);
    color: var(--error-dark);
}

.stock-status {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
}

.stock-status.normal {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.stock-status.low {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.stock-status.critical {
    background-color: var(--error-light);
    color: var(--error-dark);
}

.stock-status.overstocked {
    background-color: var(--info-light);
    color: var(--info-dark);
}

/* Equipment Details Modal Styles */
.equipment-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.details-section {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.details-section h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-size: 1.125rem;
    font-weight: 600;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-weight: 600;
    color: var(--gray-600);
    font-size: 0.875rem;
}

.detail-item span {
    color: var(--gray-900);
    font-size: 0.9rem;
}

.maintenance-history {
    max-height: 300px;
    overflow-y: auto;
}

.maintenance-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.maintenance-table th,
.maintenance-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.maintenance-table th {
    background-color: var(--gray-100);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.maintenance-type {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
}

.maintenance-type.repair {
    background-color: var(--error-light);
    color: var(--error-dark);
}

.maintenance-type.maintenance {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.maintenance-type.inspection {
    background-color: var(--info-light);
    color: var(--info-dark);
}

.stat-change.positive {
    background-color: #dcfce7;
    color: var(--success-color);
}

.stat-change.neutral {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-content {
    padding: 1.5rem;
}

.order-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background-color: var(--gray-50);
    border-radius: 0.5rem;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.order-id {
    font-weight: 600;
    color: var(--gray-900);
}

.order-table {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.order-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.preparing {
    background-color: #fed7aa;
    color: #ea580c;
}

.status-badge.ready {
    background-color: #dcfce7;
    color: var(--success-color);
}

.status-badge.confirmed {
    background-color: #dbeafe;
    color: #2563eb;
}

.order-amount {
    font-weight: 600;
    color: var(--gray-900);
}

.inventory-alerts {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
}

.alert-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.item-name {
    font-weight: 600;
    color: var(--gray-900);
}

.item-stock {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.alert-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.alert-badge.critical {
    background-color: #fee2e2;
    color: var(--error-color);
}

.alert-badge.low {
    background-color: #fef3c7;
    color: #d97706;
}

.quick-actions {
    margin-top: 2rem;
}

.quick-actions h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-btn {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    padding: 1.5rem;
    border-radius: 0.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    color: var(--gray-700);
}

.action-btn:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 2rem;
    color: var(--primary-color);
}

.action-btn span {
    font-weight: 500;
}

.text-warning {
    color: var(--warning-color);
}

/* Page Specific Styles */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.page-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
}

/* Tables Page */
.table-stats {
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.stat-row {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.stat-value.text-success {
    color: var(--success-color);
}

.stat-value.text-error {
    color: var(--error-color);
}

.stat-value.text-warning {
    color: var(--warning-color);
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.table-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 2px solid transparent;
    transition: all 0.2s;
}

.table-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.table-card.available {
    border-color: var(--success-color);
}

.table-card.occupied {
    border-color: var(--error-color);
}

.table-card.reserved {
    border-color: var(--warning-color);
}

.table-card.cleaning {
    border-color: var(--gray-400);
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.table-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.table-status {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.table-info {
    margin-bottom: 1rem;
}

.table-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

/* Orders Page */
.orders-filter {
    margin-bottom: 2rem;
}

.filter-tabs {
    display: flex;
    gap: 0.5rem;
    background-color: var(--white);
    padding: 0.5rem;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
}

.filter-tab {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--gray-600);
    transition: all 0.2s;
}

.filter-tab.active,
.filter-tab:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.orders-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.order-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.2s;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.order-header h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.order-details {
    margin-bottom: 1rem;
}

.order-details p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.order-actions {
    display: flex;
    gap: 0.5rem;
}

/* Menu Page */
.menu-categories {
    margin-bottom: 2rem;
}

.category-tabs {
    display: flex;
    gap: 0.5rem;
    background-color: var(--white);
    padding: 0.5rem;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    overflow-x: auto;
}

.category-tab {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--gray-600);
    transition: all 0.2s;
    white-space: nowrap;
}

.category-tab.active,
.category-tab:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.menu-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.menu-item-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.2s;
}

.menu-item-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.item-header h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.availability {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.availability.available {
    background-color: #dcfce7;
    color: var(--success-color);
}

.availability.unavailable {
    background-color: #fee2e2;
    color: var(--error-color);
}

.item-details {
    margin-bottom: 1rem;
}

.category {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.pricing {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.price {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.dine-in-price {
    font-size: 0.875rem;
    color: var(--warning-color);
}

.item-actions {
    display: flex;
    gap: 0.5rem;
}

/* Inventory Page */
.inventory-alerts {
    margin-bottom: 2rem;
}

.alert-card {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.75rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert-card i {
    color: var(--error-color);
    font-size: 1.25rem;
}

.inventory-table {
    background-color: var(--white);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.inventory-table table {
    width: 100%;
    border-collapse: collapse;
}

.inventory-table th,
.inventory-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.inventory-table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

.stock-status {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.stock-status.good {
    background-color: #dcfce7;
    color: var(--success-color);
}

.stock-status.low {
    background-color: #fef3c7;
    color: var(--warning-color);
}

.stock-status.critical {
    background-color: #fee2e2;
    color: var(--error-color);
}

/* Billing Page */
.billing-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.billing-stats .stat-card {
    background-color: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    text-align: center;
}

.billing-stats h4 {
    font-size: 1rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.billing-stats .amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.bills-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.bill-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.2s;
}

.bill-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.bill-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.bill-header h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.payment-status {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.payment-status.paid {
    background-color: #dcfce7;
    color: var(--success-color);
}

.payment-status.pending {
    background-color: #fef3c7;
    color: var(--warning-color);
}

.bill-details {
    margin-bottom: 1rem;
}

.bill-details p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.bill-actions {
    display: flex;
    gap: 0.5rem;
}

/* Enhanced Reports Page */
.reports-summary .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    text-align: center;
}

.summary-card .summary-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    color: white;
    font-size: 1.5rem;
}

.summary-card h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.summary-card p {
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.summary-card small {
    font-size: 0.875rem;
}

.growth.positive {
    color: var(--success-color);
}

.growth.negative {
    color: var(--error-color);
}

.profit-margin {
    color: var(--primary-color);
    font-weight: 600;
}

/* Analytics Sections */
.analytics-section {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.section-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.section-header i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

/* Top Items Grid */
.top-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.top-item-card {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid var(--primary-color);
}

.item-rank {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.125rem;
}

.item-details h5 {
    margin: 0 0 0.5rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.item-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.item-stats .quantity {
    color: var(--gray-600);
}

.item-stats .revenue {
    color: var(--success-color);
    font-weight: 600;
}

.item-stats .percentage {
    color: var(--primary-color);
    font-weight: 600;
}

/* Profit Analysis Table */
.profit-analysis-table {
    overflow-x: auto;
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.analysis-table th,
.analysis-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.analysis-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

.analysis-table .profit {
    color: var(--success-color);
    font-weight: 600;
}

.analysis-table .loss {
    color: var(--error-color);
    font-weight: 600;
}

.status-badge.profitable {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.loss {
    background: var(--error-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Expense Grid */
.expense-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.expense-card {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    border-left: 4px solid var(--warning-color);
}

.expense-card h5 {
    margin: 0 0 0.75rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.expense-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--warning-color);
    margin-bottom: 0.75rem;
}

.expense-details {
    font-size: 0.875rem;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    color: var(--gray-600);
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.analytics-card .card-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
}

.analytics-card h4 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

/* Trend Chart */
.trend-chart {
    display: flex;
    align-items: end;
    gap: 0.5rem;
    height: 150px;
    padding: 1rem 0;
}

.trend-day {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
}

.trend-bar {
    background: var(--primary-color);
    width: 100%;
    min-height: 4px;
    border-radius: 2px;
    margin-bottom: 0.5rem;
}

.trend-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.trend-value {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-900);
}

/* Peak Hours */
.peak-hours {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.hour-stat {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.hour-time {
    font-weight: 600;
    color: var(--gray-900);
    min-width: 60px;
}

.hour-bar {
    flex: 1;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.hour-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 4px;
}

.hour-orders {
    font-size: 0.875rem;
    color: var(--gray-600);
    min-width: 80px;
    text-align: right;
}

/* Category Breakdown */
.category-breakdown {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category-item {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
}

.category-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.category-name {
    font-weight: 600;
    color: var(--gray-900);
}

.category-percentage {
    font-weight: 600;
    color: var(--primary-color);
}

.category-bar {
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.category-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 3px;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* KPI Grid */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.kpi-card {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    border-top: 4px solid var(--primary-color);
}

.kpi-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.kpi-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.kpi-unit {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
}

/* Billing Filters */
.billing-filters {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.filter-buttons {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-300);
    background: var(--white);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.filter-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.summary-text {
    color: var(--gray-700);
}

.summary-amount {
    font-weight: 600;
    color: var(--primary-color);
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.report-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.2s;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.report-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.report-card p {
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-control {
    padding: 0.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* Settings Page */
.settings-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.settings-card {
    background-color: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.settings-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.settings-actions {
    display: flex;
    gap: 1rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--white);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    background: var(--white);
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
}

.modal-body {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-shrink: 0;
    background: var(--white);
    border-radius: 0 0 0.75rem 0.75rem;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background-color: var(--white);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
    padding: 1rem;
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Calculator Widget */
.calculator-widget {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: var(--white);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    width: 250px;
}

.calculator-header {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem;
    border-radius: 0.75rem 0.75rem 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calc-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calculator-display {
    padding: 1rem;
}

.calculator-display input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    text-align: right;
    font-size: 1.125rem;
    font-weight: 600;
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    padding: 1rem;
}

.calc-btn {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s;
}

.calc-btn:hover {
    background: var(--gray-50);
}

.calc-btn.calc-operator {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.calc-btn.calc-equals {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
    grid-row: span 2;
}

.calc-btn.calc-zero {
    grid-column: span 2;
}

.calc-btn.calc-clear {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.calc-btn.calc-pkr {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

/* To-Do List Styles - Updated to match theme */
.dashboard-grid-extended {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.todo-list, .purchase-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.todo-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    border-left: 4px solid transparent;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
}

.todo-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.todo-item[data-priority="high"] {
    border-left-color: var(--error-color);
    background: linear-gradient(135deg, #fff5f5 0%, var(--white) 100%);
}

.todo-item[data-priority="medium"] {
    border-left-color: var(--primary-color);
    background: linear-gradient(135deg, #fef7ed 0%, var(--white) 100%);
}

.todo-item[data-priority="low"] {
    border-left-color: var(--success-color);
    background: linear-gradient(135deg, #f0fdf4 0%, var(--white) 100%);
}

.todo-item.completed {
    opacity: 0.7;
    background: var(--gray-50);
}

.todo-content {
    flex: 1;
}

.todo-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.todo-checkbox {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.todo-title {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.95rem;
}

.todo-title.strikethrough {
    text-decoration: line-through;
    color: var(--gray-500);
}

.priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.priority-badge.priority-high {
    background: var(--error-color);
    color: white;
}

.priority-badge.priority-medium {
    background: var(--primary-color);
    color: white;
}

.priority-badge.priority-low {
    background: var(--success-color);
    color: white;
}

.todo-details {
    display: flex;
    gap: 1.5rem;
    font-size: 0.8rem;
    color: var(--gray-600);
}

.todo-assignee, .todo-time {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.todo-assignee i, .todo-time i {
    color: var(--primary-color);
}

.todo-actions {
    display: flex;
    gap: 0.375rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--gray-100);
    border-radius: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    transition: all 0.2s;
}

.btn-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.btn-icon.delete:hover {
    background: var(--error-color);
    color: white;
}

/* Purchase List Styles - Updated to match theme */
.purchase-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    border-left: 4px solid transparent;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
}

.purchase-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.purchase-item[data-urgency="high"] {
    border-left-color: var(--error-color);
    background: linear-gradient(135deg, #fff5f5 0%, var(--white) 100%);
}

.purchase-item[data-urgency="medium"] {
    border-left-color: var(--primary-color);
    background: linear-gradient(135deg, #fef7ed 0%, var(--white) 100%);
}

.purchase-item[data-urgency="low"] {
    border-left-color: var(--success-color);
    background: linear-gradient(135deg, #f0fdf4 0%, var(--white) 100%);
}

.purchase-item.received {
    opacity: 0.7;
    background: var(--gray-50);
}

.purchase-content {
    flex: 1;
}

.purchase-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.purchase-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.95rem;
}

.urgency-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.urgency-badge.urgency-high {
    background: var(--error-color);
    color: white;
}

.urgency-badge.urgency-medium {
    background: var(--primary-color);
    color: white;
}

.urgency-badge.urgency-low {
    background: var(--success-color);
    color: white;
}

.purchase-details {
    display: flex;
    gap: 1.5rem;
    font-size: 0.8rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.purchase-quantity, .purchase-cost, .purchase-supplier {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.purchase-quantity i, .purchase-cost i, .purchase-supplier i {
    color: var(--primary-color);
}

.purchase-status {
    margin-top: 0.25rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
}

.status-badge.status-pending {
    background: var(--warning-color);
    color: white;
}

.status-badge.status-ordered {
    background: #2563eb;
    color: white;
}

.status-badge.status-received {
    background: var(--success-color);
    color: white;
}

.purchase-actions {
    display: flex;
    gap: 0.375rem;
}

/* Form Enhancements */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-row.three-cols {
    grid-template-columns: 1fr 1fr 1fr;
}

.large-modal .modal-content {
    max-width: 600px;
}

.empty-state {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: 2rem;
}

/* Menu Item Enhancements */
.description {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0.5rem 0;
}



.takeaway-price {
    font-size: 0.875rem;
    color: var(--success-color);
    display: block;
}

.prep-time {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.5rem;
}

.btn-error {
    background-color: var(--error-color);
    color: white;
}

.btn-error:hover {
    background-color: #b91c1c;
}

/* POS System Enhancements */
.pos-container {
    font-family: 'Inter', sans-serif;
}

/* Enhanced Action Grid */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

/* POS Modal Styles - Fixed Layout */
.pos-modal {
    z-index: 1002;
}

.pos-modal-content {
    max-width: 1500px;
    width: 95vw;
    height: 88vh;
    max-height: 88vh;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin: 6vh auto;
}

.pos-modal-content .modal-header {
    flex-shrink: 0;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.pos-modal-content .modal-header h3 {
    font-size: 1rem;
    margin: 0;
    font-weight: 600;
}

.pos-modal-content .modal-body {
    flex: 1;
    overflow: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
}

.pos-container {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    height: 100%;
    overflow: hidden;
    background: var(--gray-100);
    gap: 1px;
    flex: 1;
}

.pos-menu {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    padding: 0.5rem;
    background: var(--white);
}

.pos-cart {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    padding: 0.5rem;
    background: var(--gray-50);
}

.menu-categories {
    display: flex;
    gap: 0.375rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.category-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.75rem;
    transition: all 0.2s;
}

.category-btn:hover {
    background: var(--gray-100);
}

.category-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.menu-grid {
    flex: 1;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 0.375rem;
    padding-right: 0.25rem;
}

.pos-menu .menu-item,
.menu-item-card {
    background: var(--white);
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--gray-200);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.pos-menu .menu-item:hover,
.menu-item-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.pos-menu .menu-item h4,
.menu-item-card h5 {
    margin: 0 0 0.25rem 0;
    color: var(--gray-900);
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.pos-menu .menu-item p,
.menu-item-card p {
    margin: 0 0 0.25rem 0;
    color: var(--gray-600);
    font-size: 0.625rem;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
}

.pos-menu .menu-item .price,
.menu-item-card .price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.75rem;
    margin-top: auto;
}

.pos-cart h3 {
    margin: 0 0 0.375rem 0;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-900);
    flex-shrink: 0;
}

.service-type {
    margin: 0.375rem 0;
    flex-shrink: 0;
}

.service-type label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--gray-700);
    font-size: 0.7rem;
}

.service-type select {
    width: 100%;
    padding: 0.25rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.25rem;
    font-size: 0.7rem;
    background: var(--white);
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    margin: 0.375rem 0;
    min-height: 80px;
    max-height: 120px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: var(--white);
    margin-bottom: 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.cart-item:last-child {
    margin-bottom: 0;
}

.cart-item-info {
    flex: 1;
    min-width: 0;
}

.cart-item-info h6 {
    margin: 0 0 0.125rem 0;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-900);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cart-item-info .price {
    color: var(--primary-color);
    font-size: 0.625rem;
    font-weight: 500;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-shrink: 0;
}

.quantity-btn {
    width: 20px;
    height: 20px;
    border: none;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.625rem;
    transition: all 0.2s;
}

.quantity-btn:hover {
    background: #c2410c;
    transform: scale(1.1);
}

.quantity-display {
    min-width: 20px;
    text-align: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-900);
}

/* Additional charges section - Ultra Compact */
.additional-charges-section {
    margin: 0.125rem 0;
    flex-shrink: 0;
}

.additional-charges-section h4 {
    margin: 0 0 0.125rem 0;
    font-size: 0.65rem;
    font-weight: 600;
    color: var(--gray-900);
}

.charge-input-group {
    display: grid;
    grid-template-columns: 1fr 60px auto;
    gap: 0.125rem;
    margin-bottom: 0.125rem;
}

.charge-input-group input {
    padding: 0.125rem 0.25rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.125rem;
    font-size: 0.6rem;
    height: 24px;
}

.charge-input-group button {
    padding: 0.125rem 0.25rem;
    font-size: 0.55rem;
    border-radius: 0.125rem;
    height: 24px;
    white-space: nowrap;
}

/* Discount section - Ultra Compact */
.discount-section {
    margin: 0.125rem 0;
    flex-shrink: 0;
}

.discount-section h4 {
    margin: 0 0 0.125rem 0;
    font-size: 0.65rem;
    font-weight: 600;
    color: var(--gray-900);
}

.discount-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.125rem;
    margin-bottom: 0.125rem;
}

.discount-controls input,
.discount-controls select {
    padding: 0.125rem 0.25rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.125rem;
    font-size: 0.6rem;
    height: 24px;
}

.cart-total {
    background: var(--white);
    padding: 0.25rem;
    border-radius: 0.125rem;
    border: 1px solid var(--gray-200);
    margin: 0.125rem 0;
    flex-shrink: 0;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.0625rem;
    font-size: 0.6rem;
}

.total-row:last-child {
    margin-bottom: 0;
    font-size: 0.65rem;
    font-weight: 600;
    padding-top: 0.0625rem;
    border-top: 1px solid var(--gray-300);
}

.payment-section {
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.payment-section h4 {
    margin: 0 0 0.125rem 0;
    color: var(--gray-900);
    font-weight: 600;
    font-size: 0.65rem;
}

.payment-methods {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.125rem;
    margin: 0.125rem 0;
}

.payment-btn {
    padding: 0.125rem;
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: 0.125rem;
    cursor: pointer;
    text-align: center;
    transition: all 0.2s;
    font-size: 0.55rem;
    height: 28px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.payment-btn:hover {
    background: var(--gray-50);
}

.payment-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.payment-btn i {
    font-size: 0.65rem;
    margin-bottom: 0.0625rem;
}

/* Final action buttons - Critical Fix */
.payment-section button[style*="width: 100%"] {
    width: 100% !important;
    padding: 0.25rem !important;
    font-size: 0.6rem !important;
    margin-top: 0.125rem !important;
    border-radius: 0.125rem !important;
    flex-shrink: 0 !important;
    height: 28px !important;
}

.payment-section .btn.btn-primary {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

.payment-section .btn.btn-outline {
    background: transparent !important;
    border: 1px solid var(--gray-300) !important;
    color: var(--gray-700) !important;
}

.payment-section .btn.btn-outline:hover {
    background: var(--gray-50) !important;
}

/* Expenses Management Styles */
.expense-summary {
    margin-bottom: 2rem;
}

.expense-breakdown {
    color: var(--gray-500);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
}

.automatic-expenses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.auto-expense-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.auto-expense-card h5 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auto-expense-card h5 i {
    color: var(--primary-color);
}

.expense-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.expense-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-100);
    font-size: 0.875rem;
}

.expense-item:last-child {
    border-bottom: none;
}

.expense-item span:first-child {
    color: var(--gray-600);
}

.expense-item span:last-child {
    font-weight: 600;
    color: var(--gray-900);
}

.expenses-table-container {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: 1rem;
}

.expenses-table {
    width: 100%;
    border-collapse: collapse;
}

.expenses-table th {
    background: var(--gray-50);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 2px solid var(--gray-200);
    font-size: 0.875rem;
}

.expenses-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.expenses-table tr:hover {
    background: var(--gray-50);
}

.expenses-table .amount {
    font-weight: 600;
    color: var(--primary-color);
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.category-badge.vegetables {
    background: #dcfce7;
    color: #166534;
}

.category-badge.utilities {
    background: #dbeafe;
    color: #1e40af;
}

.category-badge.equipment {
    background: #fef3c7;
    color: #92400e;
}

.category-badge.maintenance {
    background: #fce7f3;
    color: #be185d;
}

.category-badge.transport {
    background: #e0e7ff;
    color: #3730a3;
}

.category-badge.other {
    background: var(--gray-100);
    color: var(--gray-700);
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge.success {
    background: #dcfce7;
    color: #166534;
}

.badge.secondary {
    background: var(--gray-100);
    color: var(--gray-600);
}

.inventory-fields {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--gray-200);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkmark {
    font-size: 0.875rem;
}

.section-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.section-actions .form-control {
    width: auto;
    min-width: 150px;
}

/* Cash Balance Health Indicators */
.cash-balance-card.cash-healthy {
    border-left: 4px solid var(--success-color);
}

.cash-balance-card.cash-healthy .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #10b981);
}

.cash-balance-card.cash-warning {
    border-left: 4px solid var(--warning-color);
}

.cash-balance-card.cash-warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #f59e0b);
}

.cash-balance-card.cash-critical {
    border-left: 4px solid var(--error-color);
}

.cash-balance-card.cash-critical .stat-icon {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
}

.cash-balance-card.cash-critical .stat-change {
    color: var(--error-color) !important;
    font-weight: 600;
}

.cash-balance-card.cash-warning .stat-change {
    color: var(--warning-color) !important;
    font-weight: 600;
}

.cash-balance-card.cash-healthy .stat-change {
    color: var(--success-color) !important;
    font-weight: 600;
}

/* End of Day Functionality Styles */
.end-of-day-actions {
    margin-left: 1rem;
}

.end-of-day-modal {
    max-width: 800px;
    width: 90vw;
}

.end-of-day-summary {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.summary-section {
    background: var(--gray-50);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
}

.summary-section h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.performance-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.performance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.performance-item.total {
    grid-column: 1 / -1;
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.performance-item .label {
    font-weight: 500;
    color: var(--gray-700);
}

.performance-item.total .label {
    color: white;
}

.performance-item .value {
    font-weight: 600;
}

.performance-item .value.positive {
    color: var(--success-color);
}

.performance-item .value.negative {
    color: var(--error-color);
}

.performance-item.total .value {
    color: white;
}

.withdrawal-note {
    color: var(--gray-600);
    margin-bottom: 1rem;
    font-style: italic;
}

.withdrawal-input {
    display: grid;
    gap: 1rem;
}

.finalization-warning {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 0.5rem;
    padding: 1rem;
}

.finalization-warning p {
    margin: 0 0 0.5rem 0;
    color: var(--gray-800);
}

.finalization-warning ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
    color: var(--gray-700);
}

.finalization-warning li {
    margin-bottom: 0.25rem;
}

.warning-text {
    color: var(--warning-color);
    font-weight: 600;
    margin-top: 0.5rem;
}

.warning-text i {
    margin-right: 0.25rem;
}

.end-of-day-summary-card {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 2px solid var(--primary-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.summary-header h4 {
    margin: 0;
    color: var(--gray-900);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.finalized-badge {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.stat-label {
    font-weight: 500;
    color: var(--gray-700);
}

.stat-value {
    font-weight: 600;
    color: var(--gray-900);
}

.stat-value.positive {
    color: var(--success-color);
}

.stat-value.negative {
    color: var(--error-color);
}

.withdrawal-notes {
    margin-top: 1rem;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
    color: var(--gray-700);
}

/* Header Actions Layout */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-range span {
    color: var(--gray-600);
    font-weight: 500;
}

/* Inventory Usage Tracking Styles */
.usage-history-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: 0.75rem;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.usage-history-table th {
    background: var(--gray-50);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 2px solid var(--gray-200);
    font-size: 0.875rem;
}

.usage-history-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.usage-history-table tr:hover {
    background: var(--gray-50);
}

.quantity-used {
    font-weight: 600;
    color: var(--error-color);
}

.remaining-stock {
    font-weight: 600;
    color: var(--success-color);
}

.reason-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.reason-badge.cooking {
    background: #fef3c7;
    color: #92400e;
}

.reason-badge.cleaning {
    background: #dbeafe;
    color: #1e40af;
}

.reason-badge.maintenance {
    background: #fce7f3;
    color: #be185d;
}

.reason-badge.waste {
    background: #fee2e2;
    color: #dc2626;
}

.reason-badge.testing {
    background: #e0e7ff;
    color: #3730a3;
}

.reason-badge.other {
    background: var(--gray-100);
    color: var(--gray-700);
}

.usage-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.usage-history-container {
    max-height: 500px;
    overflow-y: auto;
}

.usage-history-table.full {
    font-size: 0.875rem;
}

.usage-history-table.full th,
.usage-history-table.full td {
    padding: 0.75rem;
}

/* Header Actions Layout */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Purchase List Integration Styles */
.purchase-summary {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--gray-200);
}

.purchase-summary h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item span:first-child {
    font-weight: 500;
    color: var(--gray-600);
}

.summary-item span:last-child {
    font-weight: 600;
    color: var(--gray-900);
}

.expense-info {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.expense-info h5 {
    margin: 0 0 0.75rem 0;
    color: #0369a1;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.expense-info ul {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--gray-700);
}

.expense-info li {
    margin-bottom: 0.25rem;
}

/* Enhanced Khata Management Styles */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    white-space: nowrap;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: none;
    border: 1px solid var(--gray-300);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    color: var(--gray-600);
}

.dropdown-toggle:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 150px;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.dropdown-menu button:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.dropdown-menu button:last-child {
    color: var(--error-color);
}

.dropdown-menu button:last-child:hover {
    background: #fee2e2;
    color: var(--error-color);
}

.supplier-info-card {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--gray-200);
}

.supplier-info-card h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.info-row:last-child {
    border-bottom: none;
}

.info-row span:first-child {
    font-weight: 500;
    color: var(--gray-600);
}

.info-row span:last-child {
    font-weight: 600;
    color: var(--gray-900);
}

.balance-preview {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.balance-preview h5 {
    margin: 0 0 0.75rem 0;
    color: #0369a1;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.preview-calculation {
    color: var(--gray-700);
}

.preview-calculation div {
    margin-bottom: 0.25rem;
}

.statement-header {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-200);
}

.supplier-details h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.supplier-details p {
    margin: 0.25rem 0;
    color: var(--gray-600);
}

.balance-summary {
    text-align: right;
}

.current-balance h4 {
    margin: 0 0 0.5rem 0;
    color: var(--gray-700);
    font-weight: 600;
}

.current-balance .amount {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.current-balance small {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.statement-filters {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--gray-200);
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.statement-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: 0.75rem;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.statement-table th {
    background: var(--gray-50);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 2px solid var(--gray-200);
    font-size: 0.875rem;
}

.statement-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.statement-table tr:hover {
    background: var(--gray-50);
}

.transaction-type {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.transaction-type.purchase {
    background: #fef3c7;
    color: #92400e;
}

.transaction-type.payment {
    background: #d1fae5;
    color: #065f46;
}

.balance-change {
    font-weight: 600;
}

.balance-change.negative {
    color: var(--error-color);
}

.balance-change.positive {
    color: var(--success-color);
}

.running-balance {
    font-weight: 600;
}

.running-balance.negative {
    color: var(--error-color);
}

.running-balance.positive {
    color: var(--success-color);
}

.statement-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.alert {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert.alert-info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.alert.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

/* Enhanced Analytics Styles */
.inventory-analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.inventory-stat-card {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--white);
    background: var(--primary-color);
}

.stat-icon.warning {
    background: #f59e0b;
}

.stat-icon.success {
    background: #10b981;
}

.stat-icon.info {
    background: #3b82f6;
}

.stat-content h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.stat-content p {
    margin: 0 0 0.25rem 0;
    color: var(--gray-600);
    font-weight: 500;
}

.stat-content small {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.top-usage-items {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.top-usage-items h5 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.usage-items-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.usage-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.usage-rank {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 0.875rem;
}

.usage-name {
    font-weight: 500;
    color: var(--gray-900);
}

.usage-quantity {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.usage-reason {
    padding: 0.25rem 0.75rem;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

/* Financial Health Dashboard */
.financial-health-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.financial-card {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.financial-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.financial-header h5 {
    margin: 0;
    color: var(--gray-900);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.financial-content {
    padding: 1.5rem;
}

.financial-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.financial-item:last-child {
    border-bottom: none;
}

.financial-item.total {
    border-top: 2px solid var(--gray-200);
    margin-top: 0.5rem;
    padding-top: 1rem;
}

.financial-card.revenue .financial-header {
    background: #f0fdf4;
    color: #166534;
}

.financial-card.expenses .financial-header {
    background: #fef2f2;
    color: #991b1b;
}

.financial-card.profit .financial-header {
    background: #eff6ff;
    color: #1e40af;
}

.amount.info {
    color: var(--primary-color);
    font-weight: 600;
}

/* Customer Analytics */
.customer-analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.customer-metric {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    text-align: center;
}

.metric-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 1.25rem;
}

.metric-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.metric-content p {
    margin: 0 0 0.25rem 0;
    color: var(--gray-600);
    font-weight: 500;
}

.metric-content small {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.customer-segments {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.customer-segments h5 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.segments-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.segment-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
}

.segment-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.segment-name {
    font-weight: 500;
    color: var(--gray-900);
}

.segment-count {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.segment-bar {
    width: 100px;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.segment-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.segment-percentage {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    min-width: 40px;
    text-align: right;
}

/* Enhanced Analytics Modals */
.analytics-tabs {
    display: flex;
    border-bottom: 2px solid var(--gray-200);
    margin-bottom: 2rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab-btn:hover {
    color: var(--gray-900);
    background: var(--gray-50);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.inventory-overview {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.stat-card h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.stock-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.25rem;
}

.stock-item.good {
    background: #f0fdf4;
    color: #166534;
}

.stock-item.warning {
    background: #fefbf2;
    color: #92400e;
}

.stock-item.danger {
    background: #fef2f2;
    color: #991b1b;
}

.value-items-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.value-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.item-name {
    font-weight: 500;
    color: var(--gray-900);
}

.item-quantity {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.item-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Financial Breakdown Modal */
.financial-breakdown {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.breakdown-section h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.breakdown-chart {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chart-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.chart-bar {
    height: 20px;
    background: var(--gray-200);
    border-radius: 10px;
    overflow: hidden;
}

.chart-bar.revenue .bar-fill {
    background: #10b981;
}

.bar-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.chart-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.expense-breakdown-chart {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.expense-chart-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.expense-bar {
    height: 16px;
    background: var(--gray-200);
    border-radius: 8px;
    overflow: hidden;
}

.expense-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.expense-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.profitability-metrics {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

/* Customer Insights Modal */
.customer-insights {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.insights-section h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.segments-chart {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.segment-chart-item {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    align-items: center;
}

.segment-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.segment-name {
    font-weight: 500;
    color: var(--gray-900);
}

.segment-stats {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.behavior-insights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.insight-card {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.insight-card h5 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.insight-metric {
    text-align: center;
    margin-bottom: 1rem;
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.metric-label {
    display: block;
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.insight-card p {
    margin: 0;
    color: var(--gray-700);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Udhars and Khata Pages Styles */
.udhars-summary, .khata-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s;
}

.summary-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.summary-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #f59e0b);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.summary-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
}

.summary-content p {
    color: var(--gray-600);
    margin: 0;
    font-size: 0.875rem;
}

.udhars-list, .khata-list {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.list-header h4 {
    margin: 0;
    color: var(--gray-900);
    font-weight: 600;
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.udhars-table, .khata-table {
    padding: 1.5rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
}

.data-table th {
    background: var(--gray-50);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 2px solid var(--gray-200);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: top;
}

.data-table tr:hover {
    background: var(--gray-50);
}

.customer-info, .supplier-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.customer-info strong, .supplier-info strong {
    color: var(--gray-900);
    font-weight: 600;
}

.customer-info small, .supplier-info small {
    color: var(--gray-500);
    font-size: 0.8rem;
}

.amount {
    font-weight: 600;
    font-size: 1rem;
}

.amount.positive {
    color: var(--success-color);
}

.amount.negative {
    color: var(--error-color);
}

.amount small {
    display: block;
    font-size: 0.75rem;
    font-weight: 400;
    color: var(--gray-500);
    margin-top: 0.125rem;
}

.status-badge.high {
    background: var(--error-color);
    color: white;
}

.status-badge.medium {
    background: var(--warning-color);
    color: white;
}

.status-badge.good {
    background: var(--success-color);
    color: white;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    background: var(--primary-color);
    color: white;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Sidebar Footer Styles */
.restaurant-info {
    text-align: center;
    padding: 1rem;
}

.restaurant-name {
    font-weight: 600;
    color: var(--gray-700);
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
}

.restaurant-tagline {
    color: var(--gray-500);
    margin: 0;
    font-size: 0.75rem;
}

/* Staff Management Styles */
.staff-summary {
    margin-bottom: 2rem;
}

.staff-list {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.staff-table {
    padding: 1.5rem;
}

.staff-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.staff-info strong {
    color: var(--gray-900);
    font-weight: 600;
}

.staff-info small {
    color: var(--gray-500);
    font-size: 0.8rem;
}

.position-badge {
    padding: 0.25rem 0.75rem;
    background: var(--primary-color);
    color: white;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.current-balance {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
}

.text-danger {
    color: var(--error-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

/* Table Management Styles */
.table-stats {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stat-row {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.text-error {
    color: var(--error-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.table-legend {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid transparent;
}

.legend-color.available {
    background: var(--success-color);
}

.legend-color.occupied {
    background: var(--error-color);
}

.legend-color.reserved {
    background: var(--warning-color);
}

.legend-color.cleaning {
    background: var(--info-color);
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.table-card {
    background: var(--white);
    border-radius: 0.75rem;
    border: 2px solid var(--gray-200);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.table-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.table-card.available {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.table-card.occupied {
    border-color: var(--error-color);
    background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.table-card.reserved {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.table-card.cleaning {
    border-color: var(--info-color);
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.table-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
}

.table-capacity {
    font-size: 0.875rem;
    color: var(--gray-600);
    background: var(--gray-100);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
}

.table-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-indicator.available {
    background: var(--success-color);
}

.status-indicator.occupied {
    background: var(--error-color);
}

.status-indicator.reserved {
    background: var(--warning-color);
}

.status-indicator.cleaning {
    background: var(--info-color);
}

.status-text {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: capitalize;
}

.table-info {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.customer-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.order-info {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.table-actions {
    margin-top: 1rem;
}

.table-actions .btn {
    width: 100%;
}

/* Table Order Modal Styles */
.table-order-modal .modal-content {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
}

.table-order-info {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.order-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    font-size: 0.875rem;
}

.detail-item strong {
    color: var(--gray-900);
    margin-right: 0.5rem;
}

.order-items-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h4 {
    margin: 0;
    color: var(--gray-900);
}

.order-items-list {
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    overflow: hidden;
}

.order-item-row {
    display: grid;
    grid-template-columns: 2fr 120px 120px 60px;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    align-items: center;
}

.order-item-row:last-child {
    border-bottom: none;
}

.item-info h5 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.item-description {
    margin: 0;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity {
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.item-price {
    text-align: right;
}

.unit-price {
    display: block;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.total-price {
    display: block;
    font-weight: 600;
    color: var(--gray-900);
}

.order-total-section {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.total-breakdown {
    max-width: 300px;
    margin-left: auto;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.total-row.total-final {
    border-top: 1px solid var(--gray-300);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-size: 1.1rem;
}

.menu-items-selection {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.menu-item-option {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s;
}

.menu-item-option:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.menu-item-option h5 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.menu-item-option p {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.item-price {
    font-weight: 600;
    color: var(--primary-color);
}

.category-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.table-info-display {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.info-item {
    font-size: 0.875rem;
}

.info-item strong {
    color: var(--gray-900);
    margin-right: 0.5rem;
}

/* Table Management Settings */
.settings-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--gray-200);
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--gray-600);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.settings-tab {
    padding: 1rem 0;
}

.tables-management-list {
    display: grid;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.table-management-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.table-management-item .table-info h5 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.table-management-item .table-info p {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.table-management-item .table-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-error {
    background-color: var(--error-color);
    color: white;
    border: 1px solid var(--error-color);
}

.btn-error:hover:not(:disabled) {
    background-color: #dc2626;
}

.btn-error:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.current-info {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.current-info p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
}

.large-modal .modal-content {
    max-width: 800px;
    width: 90vw;
}

/* Reports Styles */
.reports-summary {
    margin-bottom: 2rem;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.report-card {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.2s;
}

.report-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.report-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.report-card .card-header h4 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.report-card .card-content {
    padding: 1.5rem;
}

.report-card .card-content p {
    margin: 0 0 1rem 0;
    color: var(--gray-600);
    font-size: 0.875rem;
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quick-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.quick-stats .stat-item:last-child {
    border-bottom: none;
}

.quick-stats .stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.quick-stats .stat-value {
    font-weight: 600;
    color: var(--gray-900);
}

.date-range {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-range span {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.date-range input {
    width: 150px;
}

.report-summary {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.report-summary h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.summary-item .label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.summary-item .value {
    font-weight: 700;
    color: var(--gray-900);
    font-size: 1rem;
}

.report-table {
    margin-top: 2rem;
}

.report-table h4 {
    margin: 0 0 1rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.report-table .data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.report-table .data-table th {
    background: var(--gray-50);
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--gray-900);
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
}

.report-table .data-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--gray-100);
    font-size: 0.875rem;
    color: var(--gray-700);
}

.report-table .data-table tr:last-child td {
    border-bottom: none;
}

.report-table .data-table tr:hover {
    background: var(--gray-50);
}

.status-badge.critical {
    background: var(--error-color);
    color: white;
}

.status-badge.warning {
    background: var(--warning-color);
    color: white;
}

.status-badge.good {
    background: var(--success-color);
    color: white;
}

.status-badge.medium {
    background: var(--info-color);
    color: white;
}

/* Remove conflicting POS styles - using optimized compact version */

/* Conflicting styles removed - using compact optimized version from earlier */

.discount-controls input:hover:not(:disabled),
.discount-controls select:hover:not(:disabled) {
    border-color: var(--primary-color);
}

.discount-controls input:disabled,
.discount-controls select:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
    border-color: var(--gray-200);
}

.discount-display {
    margin-top: 1rem;
}

.current-discount {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--success-color);
    color: white;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.current-discount span:first-child {
    font-weight: 600;
}

.current-discount span:nth-child(2) {
    font-style: italic;
    opacity: 0.9;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
}

.total-row:last-child {
    border-bottom: none;
}

.total-row.discount-row {
    color: var(--error-color);
}

.total-row.total-final {
    border-top: 2px solid var(--gray-300);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
    font-size: 1rem;
    font-weight: 700;
    color: var(--gray-900);
}

.btn-icon.delete {
    background: var(--error-color);
    color: white;
    border: 1px solid var(--error-color);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.btn-icon.delete:hover {
    background: #dc2626;
}

/* Remove duplicate POS styles - using optimized version above */

/* Bill Management Styles */
.order-items-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.order-item-row {
    display: grid;
    grid-template-columns: 2fr 80px 100px auto;
    gap: 0.5rem;
    align-items: center;
    padding: 0.5rem;
    background: var(--white);
    border-radius: 0.25rem;
    border: 1px solid var(--gray-200);
}

.order-item-row input {
    padding: 0.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.order-item-row input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(251, 146, 60, 0.1);
}

.bill-card {
    background: var(--white);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s;
}

.bill-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.bill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
}

.bill-header h4 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.payment-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.payment-status.paid {
    background: var(--success-color);
    color: white;
}

.payment-status.pending {
    background: var(--warning-color);
    color: white;
}

.payment-status.completed {
    background: var(--success-color);
    color: white;
}

.bill-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.bill-details p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bill-details i {
    color: var(--primary-color);
    width: 16px;
}

.bill-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bill-actions .btn {
    flex: 1;
    min-width: 80px;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--gray-600);
}

.empty-state h3 {
    margin: 1rem 0 0.5rem 0;
    color: var(--gray-700);
}

.empty-state p {
    margin-bottom: 1.5rem;
    color: var(--gray-500);
}

/* Modern POS Responsive Design */
@media (max-width: 1400px) {
    .redesigned-pos .pos-modal-content {
        width: 95vw;
        height: 90vh;
    }

    .modern-layout {
        grid-template-columns: 1.4fr 1fr;
    }

    .modern-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 1rem;
    }

    .search-box {
        width: 250px;
    }
}

@media (max-width: 1200px) {
    .redesigned-pos .pos-modal-content {
        width: 98vw;
        height: 95vh;
    }

    .modern-layout {
        grid-template-columns: 1.2fr 1fr;
    }

    .modern-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        padding: 1rem;
    }

    .modern-menu-item {
        padding: 1rem;
    }

    .search-box {
        width: 200px;
    }

    .panel-header {
        padding: 1rem;
    }

    .modern-section {
        margin: 0.75rem 1rem;
    }
}

@media (max-width: 900px) {
    .modern-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }

    .pos-cart-panel {
        max-height: 50vh;
        overflow-y: auto;
    }

    .modern-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        max-height: 50vh;
    }

    .search-box {
        width: 150px;
    }

    .modern-tabs {
        padding: 0.75rem 1rem;
        gap: 0.25rem;
    }

    .tab-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .service-options {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .payment-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .action-section {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

/* Redesigned POS Modal Responsive (Legacy) */
@media (max-width: 1400px) {
    .touch-optimized {
        grid-template-columns: 1.5fr 1fr;
    }

    .compact-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

@media (max-width: 1200px) {
    .redesigned-pos .pos-modal-content {
        width: 98vw;
        height: 95vh;
    }

    .touch-optimized {
        grid-template-columns: 1.3fr 1fr;
    }

    .compact-categories {
        gap: 0.2rem;
    }

    .compact-btn {
        min-width: 60px;
        min-height: 50px;
        font-size: 0.7rem;
    }

    .compact-grid {
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    }
}

/* Legacy POS Modal Responsive */
@media (max-width: 1200px) {
    .pos-modal-content {
        width: 98vw;
        height: 98vh;
    }

    .pos-container {
        grid-template-columns: 1fr 380px;
        gap: 1rem;
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .redesigned-pos .pos-modal-content {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
        margin: 0;
    }

    .modern-body {
        height: calc(100vh - 60px);
    }

    .modern-header {
        padding: 0.75rem 1rem;
    }

    .pos-title {
        font-size: 1rem;
    }

    .pos-status {
        display: none;
    }

    .header-btn {
        padding: 0.5rem;
    }

    .modern-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }

    .pos-cart-panel {
        order: 1;
        max-height: 45vh;
        overflow-y: auto;
    }

    .pos-menu-panel {
        order: 2;
        max-height: 50vh;
    }

    .panel-header {
        padding: 0.75rem 1rem;
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .panel-header h4 {
        font-size: 1rem;
    }

    .search-box {
        width: 100%;
    }

    .order-info {
        align-items: flex-start;
    }

    .modern-tabs {
        padding: 0.5rem 1rem;
        gap: 0.25rem;
        overflow-x: auto;
    }

    .tab-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        min-width: 80px;
        flex-shrink: 0;
    }

    .modern-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 0.75rem;
        padding: 1rem;
    }

    .modern-menu-item {
        padding: 1rem;
    }

    .item-header {
        gap: 0.75rem;
    }

    .item-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .item-name {
        font-size: 1rem;
    }

    .item-description {
        font-size: 0.75rem;
    }

    .price-value {
        font-size: 1.25rem;
    }

    .add-btn {
        width: 36px;
        height: 36px;
        font-size: 0.875rem;
    }

    .modern-section {
        margin: 0.5rem 1rem;
    }

    .section-header {
        padding: 0.75rem 1rem;
    }

    .service-options {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .service-option {
        padding: 0.75rem;
    }

    .customer-controls {
        padding: 1rem;
    }

    .control-group {
        margin-bottom: 1rem;
    }

    .number-selector,
    .price-selector {
        gap: 0.25rem;
    }

    .selector-btn,
    .reset-btn {
        width: 36px;
        height: 36px;
    }

    .number-input,
    .price-input {
        height: 36px;
        font-size: 0.875rem;
    }

    .payment-grid {
        padding: 1rem;
        gap: 0.5rem;
    }

    .payment-option {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .payment-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .payment-label {
        font-size: 0.875rem;
    }

    .payment-desc {
        font-size: 0.625rem;
    }

    .action-section {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 1rem;
    }

    .action-btn {
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
    }

    .grand-total {
        margin: 0.5rem 1rem;
        padding: 1rem;
    }

    .total-label {
        font-size: 1rem;
    }

    .total-amount {
        font-size: 1.25rem;
    }

    /* Legacy Mobile Styles */
    .touch-optimized {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .touch-cart {
        order: 1;
        max-height: 45vh;
        overflow-y: auto;
    }

    .touch-menu {
        order: 2;
        max-height: 50vh;
    }

    .compact-categories {
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 0.25rem;
        padding: 0.5rem 0.25rem;
    }

    .compact-btn {
        min-width: 55px;
        min-height: 45px;
        font-size: 0.65rem;
        flex-shrink: 0;
    }

    .compact-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.4rem;
    }

    .touch-menu-item {
        min-height: 75px;
        padding: 0.5rem;
    }

    .compact-name {
        font-size: 0.75rem;
    }

    .compact-price .price-amount {
        font-size: 0.9rem;
    }

    .enhanced-section {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .per-head-input-group {
        flex-wrap: wrap;
    }

    .compact-input {
        width: 50px;
        height: 32px;
        font-size: 0.8rem;
    }

    .touch-btn {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
}

/* Legacy Mobile Styles */
@media (max-width: 768px) {
    .pos-modal-content {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }

    .pos-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0.75rem;
    }

    .pos-cart {
        order: 2;
        max-height: 50vh;
        overflow-y: auto;
    }

    .pos-menu {
        order: 1;
        max-height: 40vh;
    }

    .menu-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
    }

    .cart-items {
        max-height: 120px;
    }

    .charge-input-group {
        grid-template-columns: 1fr;
    }

    .discount-controls {
        grid-template-columns: 1fr;
    }

    .order-item-row {
        grid-template-columns: 1fr;
        gap: 0.25rem;
    }

    .bill-details {
        grid-template-columns: 1fr;
    }

    .bill-actions {
        flex-direction: column;
    }
}

/* Ingredients Management Styles */
.ingredients-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.ingredients-section h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ingredients-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.ingredient-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.ingredient-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ingredient-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.ingredient-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
}

.ingredient-quantity {
    font-size: 0.75rem;
    color: var(--primary-color);
    font-weight: 500;
}

.add-ingredient-row {
    display: grid;
    grid-template-columns: 2fr 1fr auto;
    gap: 0.5rem;
    align-items: center;
}

.add-ingredient-row select,
.add-ingredient-row input {
    padding: 0.75rem;
    border: 2px solid var(--gray-300);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    background: var(--white);
    transition: all 0.2s ease;
}

.add-ingredient-row select:focus,
.add-ingredient-row input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.1);
}

/* Menu Item Availability Styles */
.menu-item.unavailable {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--gray-100);
    border-color: var(--gray-300);
}

.menu-item.unavailable:hover {
    transform: none;
    box-shadow: none;
}

.menu-item.unavailable h4 {
    color: var(--gray-500);
}

/* Inventory Alert Styles */
.alert-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid;
}

.alert-card.success {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-card.warning {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.alert-card.error {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-card i {
    font-size: 1.25rem;
}

.alert-card span {
    flex: 1;
    font-weight: 500;
}

.alert-card button {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Low Stock and Out of Stock Item Styles */
.low-stock-list,
.out-of-stock-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.low-stock-item,
.out-of-stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.item-info strong {
    color: var(--gray-900);
    font-size: 1rem;
}

.stock-info {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.stock-info.critical {
    color: var(--error-color);
    font-weight: 600;
}

/* Current Stock Info Styles */
.current-stock-info {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--gray-200);
}

.current-stock-info p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
}

.current-stock-info strong {
    color: var(--gray-900);
}

@media (max-width: 768px) {
    .add-ingredient-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .ingredient-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .low-stock-item,
    .out-of-stock-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

/* Calculator Styles */
.calculator-modal {
    max-width: 350px;
    width: 90vw;
}

.calculator {
    background: var(--gray-900);
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.calculator-display {
    margin-bottom: 1rem;
}

.calculator-screen {
    width: 100%;
    height: 60px;
    background: var(--gray-800);
    border: none;
    border-radius: 0.5rem;
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: right;
    padding: 0 1rem;
    font-family: 'Courier New', monospace;
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem;
}

.calc-btn {
    height: 50px;
    border: none;
    border-radius: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calc-number {
    background: var(--gray-700);
    color: white;
}

.calc-number:hover {
    background: var(--gray-600);
}

.calc-operator {
    background: var(--primary-color);
    color: white;
}

.calc-operator:hover {
    background: #ea580c;
}

.calc-clear {
    background: var(--error-color);
    color: white;
}

.calc-clear:hover {
    background: #dc2626;
}

.calc-equals {
    background: var(--success-color);
    color: white;
    grid-row: span 2;
}

.calc-equals:hover {
    background: #059669;
}

.calc-zero {
    grid-column: span 2;
}

.calc-plus {
    grid-row: span 2;
}

/* Floating Action Buttons */
.floating-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    z-index: 1000;
}

.fab {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: var(--primary-color);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(251, 146, 60, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.fab:hover {
    background: #ea580c;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(251, 146, 60, 0.4);
}

.fab:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(251, 146, 60, 0.3);
}

.fab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.fab:hover::before {
    left: 100%;
}

.fab-calculator {
    background: var(--primary-color);
}

.fab-pos {
    background: var(--success-color);
}

.fab-pos:hover {
    background: #059669;
}

/* Floating Action Buttons Responsive */
@media (max-width: 768px) {
    .floating-actions {
        bottom: 1rem;
        right: 1rem;
        gap: 0.75rem;
    }

    .fab {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .floating-actions {
        bottom: 0.75rem;
        right: 0.75rem;
        gap: 0.5rem;
    }

    .fab {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

/* Enhanced Reports Styles */
.analytics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.overview-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.overview-card.revenue {
    border-left: 4px solid var(--primary-color);
}

.overview-card.profit {
    border-left: 4px solid var(--success-color);
}

.overview-card.customers {
    border-left: 4px solid var(--info-color);
}

.overview-card.growth {
    border-left: 4px solid var(--warning-color);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.revenue .card-icon {
    background: var(--primary-color);
}

.profit .card-icon {
    background: var(--success-color);
}

.customers .card-icon {
    background: var(--info-color);
}

.growth .card-icon {
    background: var(--warning-color);
}

.card-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-content .amount {
    margin: 0 0 0.25rem 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
}

.card-content small {
    color: var(--gray-500);
    font-size: 0.75rem;
}

.top-items-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.top-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.item-name {
    font-weight: 600;
    color: var(--gray-900);
}

.item-stats {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.item-percentage {
    font-weight: 600;
    color: var(--primary-color);
}

.category-breakdown {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category-item {
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.category-name {
    font-weight: 600;
    color: var(--gray-900);
}

.category-amount {
    font-weight: 600;
    color: var(--primary-color);
}

.category-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.category-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.peak-hours {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.hour-stat {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.hour-time {
    width: 60px;
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
}

.hour-bar {
    flex: 1;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
}

.hour-fill {
    height: 100%;
    background: var(--success-color);
    transition: width 0.3s ease;
}

.hour-orders {
    width: 80px;
    text-align: right;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.daily-trend {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.day-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.day-date {
    font-weight: 600;
    color: var(--gray-900);
    min-width: 60px;
}

.day-amount {
    font-weight: 600;
    color: var(--primary-color);
}

.day-orders {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.profit-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.profit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.profit-label {
    font-weight: 500;
    color: var(--gray-700);
}

.profit-value {
    font-weight: 600;
}

.profit-value.positive {
    color: var(--success-color);
}

.profit-value.negative {
    color: var(--error-color);
}

.inventory-costs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: var(--gray-50);
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.cost-name {
    font-weight: 500;
    color: var(--gray-900);
}

.cost-usage {
    color: var(--gray-600);
}

.cost-amount {
    font-weight: 600;
    color: var(--primary-color);
}

.cost-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--primary-color);
    color: white;
    border-radius: 0.5rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 80px;
    }

    .sidebar .user-details,
    .sidebar .nav-link span {
        display: none;
    }

    .header {
        padding: 1rem;
    }

    .page-content {
        padding: 1rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .tables-grid {
        grid-template-columns: 1fr;
    }

    .orders-list {
        grid-template-columns: 1fr;
    }

    .menu-items-grid {
        grid-template-columns: 1fr;
    }

    .bills-list {
        grid-template-columns: 1fr;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .settings-sections {
        grid-template-columns: 1fr;
    }

    .stat-row {
        flex-direction: column;
        gap: 1rem;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .date-range {
        flex-direction: column;
        align-items: stretch;
    }

    .settings-actions {
        flex-direction: column;
    }

    .dashboard-grid-extended {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-row.three-cols {
        grid-template-columns: 1fr;
    }

    .calculator-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        top: auto;
        transform: none;
        width: 280px;
    }

    .todo-details, .purchase-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Modern Redesigned POS System Styles */
.redesigned-pos .pos-modal-content {
    max-width: 98vw;
    max-height: 95vh;
    width: 1600px;
    height: 950px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.redesigned-pos .pos-modal-content.fullscreen {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
}

/* Modern Header Styles */
.modern-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.pos-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.pos-logo i {
    font-size: 1.5rem;
    color: #fbbf24;
}

.pos-title {
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.pos-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

.status-text {
    font-size: 0.875rem;
    font-weight: 500;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.header-right {
    display: flex;
    gap: 0.5rem;
}

.header-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.header-btn.close-btn:hover {
    background: rgba(239, 68, 68, 0.8);
}
















    background: #f8fafc;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e5e7eb;
}

.price-section {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.currency {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.amount {
    font-size: 1.75rem;
    font-weight: 800;
    color: #059669;
}

.add-to-cart-btn {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    border-radius: 15px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.add-to-cart-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.out-of-stock {
    font-size: 0.75rem;
    color: #ef4444;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Compact Header Styles */
.compact-header {
    padding: 0.75rem 1rem !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.compact-header h3 {
    font-size: 1.1rem !important;
    margin: 0;
    font-weight: 600;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.header-controls .btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.header-controls .btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Modern Body and Layout */
.modern-body {
    background: #f8fafc;
    padding: 0;
    height: calc(100vh - 80px);
    overflow: hidden;
}

.modern-layout {
    display: grid;
    grid-template-columns: 1.6fr 1fr;
    gap: 0;
    height: 100%;
    background: #f8fafc;
}

.modern-panel {
    background: white;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.pos-menu-panel {
    border-right: 1px solid #e5e7eb;
}

.pos-cart-panel {
    background: #f9fafb;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.pos-cart-panel.simplified-panel {
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

.pos-cart-panel.simplified-panel::-webkit-scrollbar {
    width: 6px;
}

.pos-cart-panel.simplified-panel::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.pos-cart-panel.simplified-panel::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.pos-cart-panel.simplified-panel::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.panel-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h4 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-header i {
    color: #6366f1;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 0.875rem;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    font-size: 0.875rem;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #6366f1;
    background: white;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.order-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.order-number {
    font-size: 1rem;
    font-weight: 700;
    color: #6366f1;
}

.order-time {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Touch-Optimized Container (Legacy) */
.touch-optimized {
    grid-template-columns: 1.8fr 1fr;
    gap: 1px;
    padding: 0;
    height: 100%;
    background: #f1f5f9;
}

/* Scrollable Content */
.scrollable-content {
    overflow: hidden;
}

.scrollable-menu {
    overflow-y: auto;
    max-height: calc(100vh - 200px);
}

.scrollable-menu::-webkit-scrollbar {
    width: 6px;
}

.scrollable-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.scrollable-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.scrollable-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.simplified-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    height: 100%;
}

.simplified-menu {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Modern Category Tabs */
.modern-tabs {
    display: flex;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    overflow-x: auto;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    white-space: nowrap;
    min-width: fit-content;
}

.tab-btn:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-color: #6366f1;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.tab-btn i {
    font-size: 1rem;
}

/* Modern Menu Items Grid */
.modern-grid {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    background: #f9fafb;
}

.modern-grid::-webkit-scrollbar {
    width: 6px;
}

.modern-grid::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.modern-grid::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.modern-grid::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Modern Menu Item Cards */
.modern-menu-item {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
}

.modern-menu-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    border-color: #6366f1;
}

.modern-menu-item.unavailable {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f9fafb;
}

.modern-menu-item.takeaway-only {
    border-left: 4px solid #f59e0b;
}

.modern-menu-item.takeaway-only::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 40px 40px 0;
    border-color: transparent #f59e0b transparent transparent;
}

.item-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.item-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.item-info {
    flex: 1;
}

.item-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.3;
}

.item-description {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
}

.takeaway-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 32px;
    height: 32px;
    background: #f59e0b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    z-index: 2;
}

.item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-price {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
}

.price-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.price-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
}

.add-btn {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    border-radius: 10px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.add-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.unavailable-text {
    font-size: 0.75rem;
    color: #ef4444;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.unavailable-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #ef4444;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Touch Menu Styles (Legacy) */
.touch-menu {
    background: white;
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.compact-categories {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 8px;
}

.compact-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem 0.75rem;
    min-width: 70px;
    min-height: 60px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    text-align: center;
}

.compact-btn:hover {
    background: #f1f5f9;
    border-color: #3b82f6;
    transform: translateY(-1px);
}

.compact-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #2563eb;
}

.compact-btn i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.compact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.5rem;
    overflow-y: auto;
    flex: 1;
    padding: 0.25rem;
}

.touch-friendly {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.touch-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    min-width: 120px;
    min-height: 80px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.touch-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-2px);
}

.touch-btn.active {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

.touch-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.simplified-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    overflow-y: auto;
    flex: 1;
    padding: 1rem 0;
}

/* Touch Menu Item Styles */
.touch-menu-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
}

.touch-menu-item:hover {
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.touch-menu-item.unavailable {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8fafc;
}

.touch-menu-item.takeaway-only {
    border-left: 3px solid #f59e0b;
}

.compact-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.compact-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.compact-name {
    font-size: 0.85rem;
    font-weight: 600;
    margin: 0;
    color: #1f2937;
    line-height: 1.2;
}

.takeaway-badge {
    color: #f59e0b;
    font-size: 0.9rem;
}

.compact-price {
    text-align: center;
    margin-top: auto;
}

.compact-price .price-amount {
    font-size: 1rem;
    font-weight: bold;
    color: #059669;
}

.takeaway-only-text {
    display: block;
    font-size: 0.7rem;
    color: #f59e0b;
    margin-top: 0.25rem;
}

.simplified-menu-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.simplified-menu-item:hover {
    border-color: #007bff;
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.simplified-menu-item.unavailable {
    opacity: 0.5;
    cursor: not-allowed;
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.item-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: #333;
}



.menu-item-price {
    text-align: center;
}

.price-amount {
    font-size: 1.3rem;
    font-weight: bold;
    color: #28a745;
}

/* Simplified Cart Panel Styles */
.simplified-panel {
    background: white;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.cart-header {
    padding: 0.5rem 0.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-header i {
    color: #6366f1;
}

.order-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.order-number {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6366f1;
}

.order-time {
    font-size: 0.75rem;
    color: #6b7280;
}

.service-toggle {
    display: flex;
    gap: 0.375rem;
    padding: 0.75rem 1rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.service-btn {
    flex: 1;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
    min-height: 50px;
}

.service-btn:hover {
    border-color: #6366f1;
    transform: translateY(-1px);
}

.service-btn.active {
    border-color: #6366f1;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
}

.service-btn i {
    font-size: 1rem;
}

.service-btn span {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Modern Cart Panel Styles (Legacy) */
.modern-section {
    margin: 1rem 1.5rem;
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.section-header {
    padding: 1rem 1.5rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header i {
    color: #6366f1;
    margin-right: 0.5rem;
}

.section-header span {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
}

.item-count {
    background: #6366f1;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Service Selector */
.service-selector {
    margin: 1rem 1.5rem;
}

.selector-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.service-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.service-option {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
}

.service-option:hover {
    border-color: #6366f1;
    transform: translateY(-2px);
}

.service-option.active {
    border-color: #6366f1;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
}

.service-option i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.service-option span {
    font-size: 0.875rem;
    font-weight: 600;
    display: block;
    margin-bottom: 0.25rem;
}

.option-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #f59e0b;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.625rem;
    font-weight: 700;
    text-transform: uppercase;
}

/* Customer Section */
.customer-section {
    margin: 1rem 1.5rem;
}

.customer-controls {
    padding: 1.5rem;
}

.control-group {
    margin-bottom: 1.5rem;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.number-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selector-btn {
    width: 40px;
    height: 40px;
    background: #6366f1;
    border: none;
    border-radius: 10px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.selector-btn:hover {
    background: #5b21b6;
    transform: scale(1.05);
}

.number-input {
    width: 80px;
    height: 40px;
    text-align: center;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    background: white;
}

.price-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price-input {
    flex: 1;
    height: 40px;
    padding: 0 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
}

.reset-btn {
    width: 40px;
    height: 40px;
    background: #6b7280;
    border: none;
    border-radius: 10px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.reset-btn:hover {
    background: #4b5563;
}

.toggle-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.modern-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-track {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e5e7eb;
    border-radius: 24px;
    transition: 0.3s;
}

.toggle-thumb {
    position: absolute;
    cursor: pointer;
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background: white;
    border-radius: 50%;
    transition: 0.3s;
}

.modern-toggle input:checked + .toggle-track {
    background: #6366f1;
}

.modern-toggle input:checked + .toggle-track + .toggle-thumb {
    transform: translateX(26px);
}

.toggle-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

/* Cart Items Container */
.cart-items-container {
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem 1.5rem;
}

.cart-empty {
    text-align: center;
    padding: 3rem 1.5rem;
    color: #6b7280;
}

.cart-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #d1d5db;
}

.cart-empty p {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cart-empty small {
    font-size: 0.875rem;
}

/* Touch Cart Styles (Legacy) */
.touch-cart {
    background: #f8fafc;
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.compact-toggle {
    display: flex;
    gap: 0.25rem;
    background: white;
    padding: 0.25rem;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.compact-service-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.compact-service-btn:hover {
    background: #f1f5f9;
}

.compact-service-btn.active {
    background: #3b82f6;
    color: white;
}

.compact-service-btn i {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

/* Enhanced Per Head Controls */
.enhanced-section {
    background: white;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border: 1px solid #e2e8f0;
}

.compact-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.compact-label {
    font-size: 0.7rem;
    font-weight: 600;
    color: #374151;
    margin: 0;
    white-space: nowrap;
}

.touch-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.touch-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #3b82f6;
    background: white;
    color: #3b82f6;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.touch-btn:hover {
    background: #3b82f6;
    color: white;
}

.compact-input {
    width: 60px;
    height: 36px;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 600;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
}

.per-head-input-group {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.per-head-input {
    width: 60px;
    height: 28px;
    font-size: 0.75rem;
    padding: 0 0.5rem;
}

.btn-reset {
    width: 28px;
    height: 28px;
    border: 1px solid #6b7280;
    background: white;
    color: #6b7280;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.btn-reset:hover {
    background: #6b7280;
    color: white;
}

.enhanced-controls {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}

.per-head-toggle {
    margin-top: 0.25rem;
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.toggle-switch input[type="checkbox"] {
    position: relative;
    width: 40px;
    height: 20px;
    appearance: none;
    background: #e5e7eb;
    border-radius: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.toggle-switch input[type="checkbox"]:checked {
    background: #6366f1;
}

.toggle-switch input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch input[type="checkbox"]:checked::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
    margin: 0;
}

/* Ingredients Section Styles */
.ingredients-section {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    background: #f9fafb;
}

.ingredient-selector {
    display: grid;
    grid-template-columns: 2fr 1fr auto;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.ingredient-selector select,
.ingredient-selector input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
}

.ingredient-selector button {
    padding: 0.5rem 1rem;
    background: #6366f1;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background 0.3s ease;
}

.ingredient-selector button:hover {
    background: #5b21b6;
}

.selected-ingredients {
    min-height: 60px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 0.5rem;
    background: white;
}

.ingredient-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: #f3f4f6;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.ingredient-item:last-child {
    margin-bottom: 0;
}

.ingredient-name {
    font-weight: 600;
    color: #374151;
    flex: 1;
}

.ingredient-quantity {
    font-size: 0.875rem;
    color: #6b7280;
    margin-right: 0.5rem;
}

.btn-remove-ingredient {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: background 0.3s ease;
}

.btn-remove-ingredient:hover {
    background: #dc2626;
}

.no-ingredients {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 1rem;
    margin: 0;
}

/* Usage Records Styles */
.usage-record {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}

.usage-record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.usage-record-title {
    font-weight: 600;
    color: #374151;
}

.usage-record-date {
    font-size: 0.75rem;
    color: #6b7280;
}

.usage-record-details {
    font-size: 0.875rem;
    color: #6b7280;
}

.usage-record-quantity {
    font-weight: 600;
    color: #059669;
}

/* Quantity Input Styles for POS Cart */
.quantity-input {
    width: 50px;
    height: 32px;
    text-align: center;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0 0.25rem;
    background: white;
    color: #374151;
    transition: all 0.3s ease;
}

.quantity-input:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
    background: #f8fafc;
}

.quantity-input:hover {
    border-color: #9ca3af;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.quantity-btn:active {
    transform: scale(0.95);
}

.quantity-btn.remove-btn {
    background: #ef4444;
    color: white;
    border-color: #dc2626;
    margin-left: 0.25rem;
}

.quantity-btn.remove-btn:hover {
    background: #dc2626;
    border-color: #b91c1c;
}

/* Recent Usage Styles */
.recent-usage {
    max-width: 200px;
}

.usage-item {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    margin-bottom: 0.25rem;
    padding: 0.25rem;
    background: #f9fafb;
    border-radius: 4px;
    font-size: 0.75rem;
}

.usage-quantity {
    font-weight: 600;
    color: #ef4444;
}

.usage-order {
    color: #6366f1;
    font-weight: 500;
}

.usage-date {
    color: #6b7280;
    font-size: 0.7rem;
}

.no-usage {
    color: #9ca3af;
    font-style: italic;
    font-size: 0.75rem;
}

.btn-link {
    background: none;
    border: none;
    color: #6366f1;
    cursor: pointer;
    font-size: 0.75rem;
    text-decoration: underline;
    padding: 0;
    margin-top: 0.25rem;
}

.btn-link:hover {
    color: #5b21b6;
}

/* Inventory Usage History Table Styles */
.usage-history-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.usage-history-actions select {
    min-width: 150px;
}

.inventory-usage-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.inventory-usage-table th {
    background: #f8fafc;
    color: #374151;
    font-weight: 600;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.875rem;
}

.inventory-usage-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: top;
}

.inventory-usage-table tr:hover {
    background: #f9fafb;
}

.usage-datetime {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.usage-date {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.usage-time {
    font-size: 0.75rem;
    color: #6b7280;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.item-name {
    font-weight: 600;
    color: #374151;
}

.item-id {
    font-size: 0.75rem;
    color: #6b7280;
    font-family: monospace;
}

.quantity-used {
    font-weight: 600;
    color: #ef4444;
    font-size: 0.875rem;
}

.quantity-used.positive {
    color: #059669;
}

.menu-item {
    color: #374151;
    font-weight: 500;
}

.order-number {
    color: #6366f1;
    font-weight: 500;
    font-family: monospace;
}

.usage-type {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.usage-type.order_fulfillment {
    background: #dbeafe;
    color: #1d4ed8;
}

.usage-type.manual {
    background: #f3f4f6;
    color: #374151;
}

.usage-type.order_deletion_restore {
    background: #dcfce7;
    color: #166534;
}

.usage-type.waste {
    background: #fef3c7;
    color: #92400e;
}

.usage-type.expired {
    background: #fee2e2;
    color: #dc2626;
}

.no-data {
    text-align: center;
    padding: 2rem;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
}

.empty-state i {
    font-size: 2rem;
    color: #d1d5db;
}

.empty-state p {
    font-weight: 600;
    margin: 0;
}

.empty-state small {
    font-size: 0.75rem;
    color: #9ca3af;
}

/* Detail Modal Styles */
.detail-group {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.detail-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-group h4 {
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #6b7280;
    flex: 1;
}

.detail-value {
    font-weight: 600;
    color: #374151;
    text-align: right;
    flex: 1;
}

.detail-value.positive {
    color: #059669;
}

.detail-value.negative {
    color: #ef4444;
}

/* Cash Balance States */
.cash-not-set {
    border-left: 4px solid #f59e0b !important;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
}

.cash-not-set .stat-content h3 {
    color: #92400e !important;
}

.cash-not-set .stat-content p {
    color: #78350f !important;
}

/* Print Bill Checkbox Styles */
.print-bill-option {
    margin: 1rem 0;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-container:hover .checkmark {
    border-color: #6366f1;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: #6366f1;
    border-color: #6366f1;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label {
    color: #374151;
    font-weight: 500;
}

.per-head-info {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Scrollable Cart Items */
.scrollable-cart {
    overflow-y: auto;
    max-height: 300px;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    margin-bottom: 1rem;
}

.scrollable-cart::-webkit-scrollbar {
    width: 4px;
}

.scrollable-cart::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.scrollable-cart::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.scrollable-cart::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.touch-cart-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.compact-totals {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.compact-totals .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

.compact-totals .total-row:last-child {
    border-top: 1px solid #e2e8f0;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    font-weight: bold;
    font-size: 1rem;
}

/* Simplified Cart Styles */
.simplified-cart {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Touch Cart Item Enhancements */
.touch-cart-items .cart-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
}

.touch-cart-items .cart-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
}

.touch-cart-items .cart-item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.touch-cart-items .cart-item-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.touch-cart-items .cart-item-price {
    font-size: 0.8rem;
    color: #059669;
    font-weight: 500;
}

.touch-cart-items .cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.touch-cart-items .quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: #f8fafc;
    border-radius: 4px;
    padding: 0.25rem;
}

.touch-cart-items .quantity-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.touch-cart-items .quantity-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.touch-cart-items .quantity-display {
    min-width: 30px;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 600;
    color: #1f2937;
}

.touch-cart-items .remove-item-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #ef4444;
    background: white;
    color: #ef4444;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.touch-cart-items .remove-item-btn:hover {
    background: #ef4444;
    color: white;
}

/* Modern Totals Section */
.totals-section {
    margin: 1rem 1.5rem;
}

.modern-breakdown {
    padding: 1.5rem;
}

.total-breakdown .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
    font-size: 0.875rem;
}

.total-breakdown .total-row:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1rem;
    color: #1f2937;
}

.grand-total {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    padding: 1.5rem;
    margin: 1rem 1.5rem;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.total-label {
    font-size: 1.125rem;
    font-weight: 600;
}

.total-amount {
    font-size: 1.5rem;
    font-weight: 700;
}

/* Modern Payment Section */
.payment-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 1.5rem;
}

.payment-option {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
}

.payment-option:hover {
    border-color: #6366f1;
    transform: translateY(-2px);
}

.payment-option.active {
    border-color: #6366f1;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
}

.payment-icon {
    width: 48px;
    height: 48px;
    background: rgba(99, 102, 241, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #6366f1;
    flex-shrink: 0;
}

.payment-option.active .payment-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.payment-label {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.payment-desc {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Modern Action Buttons */
.action-section {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-top: 1px solid #e5e7eb;
}

.action-btn {
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.secondary-btn {
    background: #f3f4f6;
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.secondary-btn:hover {
    background: #e5e7eb;
    color: #374151;
    transform: translateY(-2px);
}

.primary-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

.action-btn i {
    font-size: 1.125rem;
}

.cart-header {
    margin-bottom: 0.5rem;
}

.cart-header h3 {
    margin: 0 0 1rem 0;
    color: #333;
}

.service-toggle {
    display: flex;
    gap: 0.5rem;
}

.service-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border: 2px solid #ddd;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.service-btn:hover {
    border-color: #007bff;
}

.service-btn.active {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

.service-btn i {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.customer-count-section.touch-friendly {
    background: white;
    border-radius: 6px;
    padding: 0.75rem;
    margin: 0.5rem 1rem;
    border: 1px solid #e5e7eb;
}

.customer-count-header {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
}

.customer-count-header i {
    font-size: 1rem;
    color: #6366f1;
}

.touch-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.count-btn {
    width: 32px;
    height: 32px;
    border: 2px solid #6366f1;
    background: white;
    color: #6366f1;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.count-btn:hover {
    background: #6366f1;
    color: white;
}

.count-input {
    width: 60px;
    height: 32px;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 600;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    background: #f9fafb;
}

/* Reports Integration Styles */
.reports-integration-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.integration-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.integration-header h4 {
    margin: 0;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.integration-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.integration-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.integration-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.integration-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.integration-icon i {
    font-size: 1.5rem;
}

.integration-content h5 {
    margin: 0 0 1rem 0;
    color: #333;
    font-weight: 600;
}

.integration-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.integration-stats span {
    font-size: 0.9rem;
    color: #666;
}

/* Staff Delete Confirmation Styles */
.delete-confirmation-modal {
    max-width: 600px;
    width: 90vw;
}

.delete-header {
    background: #fee2e2;
    color: #dc2626;
    border-bottom: 2px solid #fecaca;
}

.delete-warning {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #fef2f2;
    border-radius: 8px;
    border: 1px solid #fecaca;
}

.warning-icon {
    flex-shrink: 0;
}

.warning-icon i {
    font-size: 3rem;
    color: #dc2626;
}

.warning-content h4 {
    margin: 0 0 1rem 0;
    color: #dc2626;
    font-weight: 600;
}

.staff-info {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.staff-info p {
    margin: 0.5rem 0;
    color: #374151;
}

.deletion-consequences {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #fffbeb;
    border-radius: 8px;
    border: 1px solid #fbbf24;
}

.deletion-consequences h5 {
    margin: 0 0 1rem 0;
    color: #92400e;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.deletion-consequences ul {
    margin: 0;
    padding-left: 1.5rem;
    color: #78350f;
}

.deletion-consequences li {
    margin-bottom: 0.5rem;
}

.backup-option {
    margin-bottom: 1.5rem;
}

.confirmation-input {
    margin-bottom: 1rem;
}

.confirmation-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.confirmation-input input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
}

.confirmation-input input:focus {
    outline: none;
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* System Reset Modal Styles */
.system-reset-modal {
    max-width: 700px;
    width: 90vw;
}

.reset-header {
    background: #fef3c7;
    color: #92400e;
    border-bottom: 2px solid #fbbf24;
}

.reset-warning {
    margin-bottom: 2rem;
}

.warning-box {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #fef2f2;
    border-radius: 8px;
    border: 1px solid #fecaca;
}

.warning-box i {
    font-size: 2rem;
    color: #dc2626;
}

.warning-box p {
    margin: 0;
    color: #dc2626;
    font-weight: 500;
}

.reset-options {
    margin-bottom: 2rem;
}

.reset-options h4 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-weight: 600;
}

.reset-option-card {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.reset-option-card:hover {
    border-color: #f59e0b;
    background: #fffbeb;
}

.option-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.option-header input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #f59e0b;
}

.option-header label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    margin: 0;
}

.option-header i {
    color: #f59e0b;
    font-size: 1.2rem;
}

.option-description {
    color: #6b7280;
    font-size: 0.9rem;
    margin-left: 2.25rem;
}

.backup-options {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f0f9ff;
    border-radius: 8px;
    border: 1px solid #0ea5e9;
}

.backup-options h4 {
    margin: 0 0 1rem 0;
    color: #0369a1;
    font-weight: 600;
}

.confirmation-section {
    margin-bottom: 1rem;
}

.confirmation-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.confirmation-section input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
}

.confirmation-section input:focus {
    outline: none;
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.btn.enabled {
    opacity: 1;
    cursor: pointer;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* AI Assistant Styles */
.ai-chat-button {
    position: fixed;
    bottom: 100px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.ai-chat-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.ai-chat-widget {
    position: fixed;
    bottom: 170px;
    right: 20px;
    width: 400px;
    height: 600px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ai-chat-widget.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

.ai-chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.ai-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ade80;
}

.ai-status-indicator.offline {
    background: #f87171;
}

.ai-chat-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background 0.2s;
}

.ai-chat-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-message {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.user-message {
    flex-direction: row-reverse;
}

.ai-avatar, .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.ai-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-avatar {
    background: #e5e7eb;
    color: #374151;
}

.ai-message-content, .user-message-content {
    max-width: 280px;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.ai-message-content {
    background: #f3f4f6;
    color: #374151;
}

.user-message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai-message-content p, .user-message-content p {
    margin: 0 0 0.5rem 0;
}

.ai-message-content p:last-child, .user-message-content p:last-child {
    margin-bottom: 0;
}

.typing-indicator .ai-message-content {
    padding: 1rem;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #9ca3af;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.chat-data-list, .chat-data-object {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.chat-data-item, .chat-data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.chat-data-item:last-child, .chat-data-row:last-child {
    border-bottom: none;
}

.chat-data-item strong, .data-key {
    font-weight: 600;
    color: #374151;
}

.chat-data-item span, .data-value {
    color: #6b7280;
    font-size: 0.85rem;
}

.chat-data-item small {
    display: block;
    color: #9ca3af;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.ai-chat-input {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 0.5rem;
}

.ai-chat-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.9rem;
    outline: none;
}

.ai-chat-input input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ai-chat-input button {
    padding: 0.75rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-chat-input button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-quick-actions {
    padding: 0.75rem 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.ai-quick-btn {
    padding: 0.5rem 0.75rem;
    background: #f3f4f6;
    color: #374151;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.ai-quick-btn:hover {
    background: #e5e7eb;
    transform: translateY(-1px);
}

.ai-quick-btn i {
    font-size: 0.75rem;
}

/* AI Insights Panel */
.ai-insights-panel {
    margin: 2rem 0;
}

.ai-insights-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-left: 4px solid #667eea;
}

.ai-insights-card .card-header {
    background: transparent;
    border-bottom: 1px solid #e2e8f0;
}

.ai-insights-card .card-header h3 {
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-insights-card .card-header h3 i {
    color: #667eea;
}

.ai-controls {
    display: flex;
    gap: 0.5rem;
}

.ai-insights-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    color: #6b7280;
    font-style: italic;
}

.ai-loading i {
    color: #667eea;
    font-size: 1.2rem;
}

.ai-no-insights {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    color: #6b7280;
    text-align: center;
}

.ai-no-insights i {
    font-size: 2rem;
    color: #10b981;
}

.ai-insight-item {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ai-insight-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #6b7280;
}

.ai-insight-item.positive::before {
    background: #10b981;
}

.ai-insight-item.warning::before {
    background: #f59e0b;
}

.ai-insight-item.info::before {
    background: #3b82f6;
}

.ai-insight-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.insight-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
    background: #f3f4f6;
    color: #6b7280;
}

.ai-insight-item.positive .insight-icon {
    background: #dcfce7;
    color: #10b981;
}

.ai-insight-item.warning .insight-icon {
    background: #fef3c7;
    color: #f59e0b;
}

.ai-insight-item.info .insight-icon {
    background: #dbeafe;
    color: #3b82f6;
}

.insight-title {
    flex: 1;
}

.insight-title h5 {
    margin: 0 0 0.25rem 0;
    color: #1f2937;
    font-weight: 600;
    font-size: 1rem;
}

.insight-type {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.insight-priority {
    flex-shrink: 0;
}

.priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.priority-badge.high {
    background: #fee2e2;
    color: #dc2626;
}

.priority-badge.medium {
    background: #fef3c7;
    color: #d97706;
}

.priority-badge.low {
    background: #dbeafe;
    color: #2563eb;
}

.insight-content {
    color: #374151;
    line-height: 1.5;
}

.insight-content p {
    margin: 0 0 0.75rem 0;
}

.insight-action {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 3px solid #667eea;
    font-size: 0.9rem;
}

.insight-action strong {
    color: #1f2937;
}

.insight-data-list {
    margin-top: 0.75rem;
    background: #f8fafc;
    border-radius: 8px;
    padding: 0.75rem;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.85rem;
}

.data-item:last-child {
    border-bottom: none;
}

.item-name {
    font-weight: 500;
    color: #374151;
}

.item-value {
    color: #6b7280;
}

.data-more {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.insight-data-object {
    margin-top: 0.75rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 6px;
    font-size: 0.85rem;
}

.data-key {
    font-weight: 500;
    color: #374151;
}

.data-value {
    color: #6b7280;
    font-weight: 600;
}

/* AI Settings */
.ai-settings-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-left: 4px solid #667eea;
}

.ai-settings-card h4 {
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.ai-settings-card h4 i {
    color: #667eea;
}

.ai-status-info {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid #e5e7eb;
}

.ai-status-info h5 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    font-weight: 600;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.status-item:last-child {
    border-bottom: none;
}

.status-value {
    font-weight: 600;
}

.status-value.online {
    color: #10b981;
}

.status-value.offline {
    color: #ef4444;
}

/* Responsive Design for AI Components */
@media (max-width: 768px) {
    .ai-chat-widget {
        width: calc(100vw - 40px);
        height: calc(100vh - 200px);
        right: 20px;
        bottom: 100px;
    }

    .ai-chat-button {
        bottom: 20px;
        right: 20px;
    }

    .insight-data-object {
        grid-template-columns: 1fr;
    }

    .insight-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .ai-controls {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* AI Modal Styles */
.ai-analysis-modal, .ai-predictions-modal {
    max-width: 800px;
    width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
}

.ai-analysis-content, .predictions-content {
    padding: 1rem 0;
}

.analysis-insights {
    margin-bottom: 2rem;
}

.analysis-insights h4 {
    margin: 0 0 1rem 0;
    color: #1f2937;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.analysis-insight-item {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb;
    border-left: 4px solid #6b7280;
}

.analysis-insight-item.positive {
    border-left-color: #10b981;
}

.analysis-insight-item.warning {
    border-left-color: #f59e0b;
}

.analysis-insight-item.info {
    border-left-color: #3b82f6;
}

.analysis-insight-item .insight-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.analysis-insight-item .insight-header i {
    font-size: 1.2rem;
    color: #6b7280;
}

.analysis-insight-item.positive .insight-header i {
    color: #10b981;
}

.analysis-insight-item.warning .insight-header i {
    color: #f59e0b;
}

.analysis-insight-item.info .insight-header i {
    color: #3b82f6;
}

.analysis-insight-item .insight-header h5 {
    margin: 0;
    flex: 1;
    color: #1f2937;
    font-weight: 600;
}

.analysis-insight-item p {
    margin: 0 0 0.75rem 0;
    color: #374151;
    line-height: 1.5;
}

.analysis-insight-item .insight-action {
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 3px solid #667eea;
    font-size: 0.9rem;
    margin-top: 0.75rem;
}

.no-insights {
    text-align: center;
    padding: 3rem 2rem;
    color: #6b7280;
}

.no-insights i {
    font-size: 3rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.no-insights h4 {
    margin: 0 0 0.5rem 0;
    color: #1f2937;
}

.analysis-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.prediction-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e5e7eb;
}

.prediction-section h4 {
    margin: 0 0 1rem 0;
    color: #1f2937;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.prediction-section h4 i {
    color: #667eea;
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.prediction-item:last-child {
    border-bottom: none;
}

.prediction-value {
    font-weight: 600;
    color: #1f2937;
}

.prediction-confidence {
    font-size: 0.85rem;
    color: #6b7280;
    font-style: italic;
    margin-top: 0.5rem;
}

.inventory-predictions {
    max-height: 200px;
    overflow-y: auto;
}

.predictions-note {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.predictions-note i {
    color: #0ea5e9;
    font-size: 1.1rem;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.predictions-note p {
    margin: 0;
    color: #0369a1;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* AI Summary Card on Dashboard */
.ai-summary-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-left: 4px solid #667eea;
}

.ai-summary-card .card-header h3 {
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-summary-card .card-header h3 i {
    color: #667eea;
}

.ai-insights-summary {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.insight-summary-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.insight-summary-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.insight-summary-item.positive {
    border-left: 4px solid #10b981;
}

.insight-summary-item.warning {
    border-left: 4px solid #f59e0b;
}

.insight-summary-item.info {
    border-left: 4px solid #3b82f6;
}

.insight-summary-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
    background: #f3f4f6;
    color: #6b7280;
}

.insight-summary-item.positive .insight-summary-icon {
    background: #dcfce7;
    color: #10b981;
}

.insight-summary-item.warning .insight-summary-icon {
    background: #fef3c7;
    color: #f59e0b;
}

.insight-summary-item.info .insight-summary-icon {
    background: #dbeafe;
    color: #3b82f6;
}

.insight-summary-content {
    flex: 1;
}

.insight-summary-content h5 {
    margin: 0 0 0.25rem 0;
    color: #1f2937;
    font-weight: 600;
    font-size: 0.9rem;
}

.insight-summary-content p {
    margin: 0;
    color: #6b7280;
    font-size: 0.8rem;
    line-height: 1.3;
}

.insight-summary-priority {
    flex-shrink: 0;
}

.priority-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6b7280;
}

.priority-dot.high {
    background: #dc2626;
}

.priority-dot.medium {
    background: #d97706;
}

.priority-dot.low {
    background: #2563eb;
}

.ai-summary-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

/* Enhanced AI Assistant Styles */
.persistent-ai-button {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
}

.ai-main-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    position: relative;
}

.ai-main-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.ai-status-dot {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.ai-status-dot.online {
    background: #10b981;
}

.ai-status-dot.offline {
    background: #ef4444;
}

.ai-quick-actions-menu {
    position: absolute;
    top: 70px;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    padding: 0.5rem;
    min-width: 200px;
    z-index: 10001;
}

.ai-quick-actions-menu.hidden {
    display: none;
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #374151;
}

.quick-action-item:hover {
    background: #f3f4f6;
    color: #667eea;
}

.quick-action-item i {
    width: 16px;
    text-align: center;
}

/* Enhanced Chat Widget */
.enhanced-ai-chat-widget {
    position: fixed;
    top: 100px;
    right: 20px;
    width: 450px;
    height: 700px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
}

.enhanced-ai-chat-widget.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

.enhanced-ai-chat-widget .ai-chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-info {
    display: flex;
    flex-direction: column;
}

.ai-title {
    font-weight: 600;
    font-size: 1rem;
}

.ai-subtitle {
    font-size: 0.8rem;
    opacity: 0.9;
}

.ai-status-indicators {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
}

.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.status-indicator.online {
    background: #10b981;
    color: white;
}

.status-indicator.offline {
    background: #ef4444;
    color: white;
}

.status-indicator.active {
    background: #3b82f6;
    color: white;
}

.status-indicator.inactive {
    background: #6b7280;
    color: white;
    opacity: 0.6;
}

.ai-controls {
    display: flex;
    gap: 0.5rem;
}

.ai-control-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.ai-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.ai-automation-status {
    background: #f8fafc;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.status-item span:first-child {
    color: #6b7280;
    font-size: 0.75rem;
}

.status-item span:last-child {
    font-weight: 600;
    color: #1f2937;
}

.error-count {
    color: #dc2626 !important;
}

.enhanced-ai-chat-widget .ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.welcome-message .ai-message-content {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #0ea5e9;
    border-radius: 12px;
    padding: 1.5rem;
}

.welcome-message h4 {
    margin: 0 0 1rem 0;
    color: #0369a1;
}

.command-examples {
    margin-top: 1rem;
}

.command-category {
    margin-bottom: 1rem;
}

.command-category h5 {
    margin: 0 0 0.5rem 0;
    color: #0369a1;
    font-size: 0.9rem;
}

.command-category ul {
    margin: 0;
    padding-left: 1rem;
    list-style: none;
}

.command-category li {
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
    color: #0c4a6e;
    position: relative;
}

.command-category li::before {
    content: '▶';
    position: absolute;
    left: -1rem;
    color: #0ea5e9;
}

.automated-message .ai-avatar {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.automated-message .ai-avatar.automated {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.message-timestamp {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.automated-badge {
    background: #10b981;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.enhanced-chat-data-list,
.enhanced-chat-data-object {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.enhanced-chat-data-item,
.enhanced-chat-data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.enhanced-chat-data-item:last-child,
.enhanced-chat-data-row:last-child {
    border-bottom: none;
}

.data-description {
    display: block;
    color: #9ca3af;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.enhanced-typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
    margin-bottom: 0.5rem;
}

.enhanced-typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #9ca3af;
    animation: enhanced-typing 1.4s infinite ease-in-out;
}

.enhanced-typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.enhanced-typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes enhanced-typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

.typing-text {
    font-size: 0.85rem;
    color: #6b7280;
    font-style: italic;
}

.ai-chat-input-area {
    border-top: 1px solid #e5e7eb;
}

.ai-quick-commands {
    padding: 0.75rem 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    background: #f8fafc;
}

.quick-cmd-btn {
    padding: 0.5rem 0.75rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #374151;
}

.quick-cmd-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.quick-cmd-btn i {
    font-size: 0.75rem;
}

/* Automation Notifications */
.ai-automation-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    z-index: 10002;
    min-width: 400px;
    max-width: 600px;
    border-left: 4px solid #10b981;
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.notification-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-header i {
    margin-right: 0.5rem;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background 0.2s;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.notification-content {
    padding: 1rem;
}

.notification-content h4 {
    margin: 0 0 0.5rem 0;
    color: #1f2937;
}

.notification-content p {
    margin: 0 0 0.5rem 0;
    color: #374151;
}

.notification-details {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e5e7eb;
}

.notification-details small {
    color: #6b7280;
    font-style: italic;
}

/* Automation History Modal */
.automation-history-modal {
    max-width: 800px;
    width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
}

.automation-history-content {
    padding: 1rem 0;
}

.history-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    display: block;
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.history-list h4 {
    margin: 0 0 1rem 0;
    color: #1f2937;
}

.history-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #6b7280;
}

.history-item.success {
    border-left-color: #10b981;
}

.history-item.failed {
    border-left-color: #ef4444;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.action-type {
    font-weight: 600;
    color: #1f2937;
    background: #f3f4f6;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.85rem;
}

.action-time {
    font-size: 0.8rem;
    color: #6b7280;
}

.history-details p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    color: #374151;
}

.history-details .error {
    color: #dc2626;
}

/* Responsive Design for Enhanced AI */
@media (max-width: 768px) {
    .persistent-ai-button {
        top: 10px;
        right: 10px;
    }

    .ai-main-button {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .enhanced-ai-chat-widget {
        width: calc(100vw - 20px);
        height: calc(100vh - 120px);
        top: 80px;
        right: 10px;
    }

    .ai-quick-actions-menu {
        right: -150px;
        min-width: 180px;
    }

    .ai-automation-notification {
        min-width: calc(100vw - 40px);
        left: 20px;
        transform: none;
    }

    .history-stats {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .ai-automation-status {
        flex-direction: column;
        gap: 0.5rem;
    }

    .status-item {
        flex-direction: row;
        justify-content: space-between;
    }

    .ai-quick-commands {
        flex-direction: column;
        gap: 0.25rem;
    }

    .quick-cmd-btn {
        justify-content: center;
    }
}

/* Dark mode support for AI components */
@media (prefers-color-scheme: dark) {
    .enhanced-ai-chat-widget,
    .ai-quick-actions-menu,
    .ai-automation-notification {
        background: #1f2937;
        color: #f9fafb;
    }

    .ai-automation-status {
        background: #374151;
    }

    .enhanced-chat-data-list,
    .enhanced-chat-data-object,
    .quick-cmd-btn {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .welcome-message .ai-message-content {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
        border-color: #3b82f6;
    }

    .history-stats {
        background: #374151;
    }

    .history-item {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
}

/* Enhanced AI Response Styles */
.correction-notice {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 0.75rem;
    margin: 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #92400e;
}

.correction-notice i {
    color: #f59e0b;
    flex-shrink: 0;
}

.step-by-step-explanation {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.75rem 0;
}

.step-by-step-explanation h5 {
    margin: 0 0 0.75rem 0;
    color: #0369a1;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.step-by-step-explanation ol {
    margin: 0;
    padding-left: 1.5rem;
    color: #0c4a6e;
}

.step-by-step-explanation li {
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    line-height: 1.4;
}

.ai-suggestions {
    background: #f0fdf4;
    border: 1px solid #22c55e;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.75rem 0;
}

.ai-suggestions h5 {
    margin: 0 0 0.75rem 0;
    color: #15803d;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-suggestions ul {
    margin: 0;
    padding-left: 1.5rem;
    list-style: none;
    color: #166534;
}

.ai-suggestions li {
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    line-height: 1.4;
    position: relative;
}

.ai-suggestions li::before {
    content: '💡';
    position: absolute;
    left: -1.5rem;
}

.confirmation-buttons {
    display: flex;
    gap: 0.75rem;
    margin: 1rem 0 0.5rem 0;
    justify-content: center;
}

.confirm-btn, .cancel-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.confirm-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.confirm-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.cancel-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.cancel-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.ai-avatar.confirmation {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    animation: pulse-question 2s infinite;
}

@keyframes pulse-question {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 8px rgba(245, 158, 11, 0);
    }
}

.ai-status-badge {
    background: #6b7280;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.ai-status-badge.online {
    background: #10b981;
}

.ai-status-badge.offline {
    background: #ef4444;
}

/* Enhanced typing indicator */
.enhanced-typing-dots {
    display: flex;
    gap: 6px;
    align-items: center;
    margin-bottom: 0.75rem;
}

.enhanced-typing-dots span {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: enhanced-typing-pulse 1.4s infinite ease-in-out;
}

.enhanced-typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.enhanced-typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes enhanced-typing-pulse {
    0%, 60%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.typing-text {
    font-size: 0.9rem;
    color: #667eea;
    font-style: italic;
    font-weight: 500;
}

/* Enhanced data formatting */
.enhanced-chat-data-list,
.enhanced-chat-data-object {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.75rem 0;
}

.enhanced-chat-data-item,
.enhanced-chat-data-row {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.enhanced-chat-data-item:last-child,
.enhanced-chat-data-row:last-child {
    border-bottom: none;
}

.enhanced-chat-data-item strong,
.data-key {
    font-weight: 600;
    color: #1e293b;
    flex-shrink: 0;
    margin-right: 1rem;
}

.data-value {
    color: #475569;
    font-weight: 500;
    text-align: right;
}

.data-description {
    display: block;
    color: #64748b;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-style: italic;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .correction-notice,
    .step-by-step-explanation,
    .ai-suggestions {
        margin: 0.5rem 0;
        padding: 0.75rem;
    }

    .confirmation-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .confirm-btn, .cancel-btn {
        padding: 0.75rem;
        justify-content: center;
    }

    .enhanced-chat-data-item,
    .enhanced-chat-data-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .data-value {
        text-align: left;
    }
}

/* API Configuration Modal */
.api-config-modal {
    max-width: 700px;
    width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
}

.api-config-content {
    padding: 1rem 0;
}

.config-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.config-section h4 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.config-section p {
    margin: 0 0 1.5rem 0;
    color: #64748b;
    font-size: 0.9rem;
}

.api-service-config {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.api-service-config h5 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.input-group input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.test-btn {
    padding: 0.75rem 1.5rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.test-btn:hover {
    background: #5a67d8;
}

.test-btn.success {
    background: #10b981;
}

.test-btn.error {
    background: #ef4444;
}

.test-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.api-service-config small {
    color: #6b7280;
    font-size: 0.8rem;
    font-style: italic;
}

.preference-group {
    margin-bottom: 1rem;
}

.preference-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #374151;
    font-weight: 500;
    font-size: 0.9rem;
}

.preference-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    background: white;
}

.preference-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.preference-group input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.2);
}

.preference-group small {
    display: block;
    margin-top: 0.25rem;
    color: #6b7280;
    font-size: 0.8rem;
}

.config-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.config-actions .btn {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.config-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.config-actions .btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.config-actions .btn-outline {
    background: transparent;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.config-actions .btn-outline:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Voice Recognition Button */
.ai-control-btn.listening {
    background: #ef4444;
    animation: pulse-recording 1.5s infinite;
}

@keyframes pulse-recording {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
    }
}

/* Multi-language Support Indicators */
.language-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 2px solid white;
}

.language-indicator.ur {
    background: #10b981;
    color: white;
}

.language-indicator.en {
    background: #3b82f6;
    color: white;
}

.language-indicator.mixed {
    background: linear-gradient(45deg, #10b981 50%, #3b82f6 50%);
    color: white;
}

/* Unrestricted Mode Indicator */
.unrestricted-indicator {
    position: fixed;
    top: 90px;
    right: 20px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 9998;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    animation: glow-unrestricted 2s infinite alternate;
}

@keyframes glow-unrestricted {
    from {
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }
    to {
        box-shadow: 0 4px 20px rgba(245, 158, 11, 0.6);
    }
}

/* Responsive Design for API Config */
@media (max-width: 768px) {
    .api-config-modal {
        width: calc(100vw - 20px);
        margin: 10px;
    }

    .config-section {
        padding: 1rem;
    }

    .input-group {
        flex-direction: column;
    }

    .test-btn {
        width: 100%;
    }

    .config-actions {
        flex-direction: column;
    }

    .config-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* OpenRouter Free Tier Styling */
.api-service-config.free-tier {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #0ea5e9;
    position: relative;
}

.api-service-config.free-tier::before {
    content: '🆓';
    position: absolute;
    top: -10px;
    right: -10px;
    background: #10b981;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.free-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.model-selector {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #e0f2fe;
}

.model-selector label {
    display: block;
    margin-bottom: 0.5rem;
    color: #0c4a6e;
    font-weight: 600;
    font-size: 0.85rem;
}

.model-selector select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #0ea5e9;
    border-radius: 4px;
    background: white;
    color: #0c4a6e;
    font-size: 0.8rem;
}

.model-selector select:focus {
    outline: none;
    border-color: #0284c7;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.api-service-config.free-tier small {
    color: #0c4a6e;
    font-weight: 500;
}

.api-service-config.free-tier small a {
    color: #0ea5e9;
    text-decoration: none;
    font-weight: 600;
}

.api-service-config.free-tier small a:hover {
    color: #0284c7;
    text-decoration: underline;
}

/* Free tier highlight in dropdown */
option[value="openrouter"] {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    color: #0c4a6e;
    font-weight: 600;
}

/* Enhanced Modal Styles for Cash in Hand and Sales Insights */
.modal-content.large-modal {
    max-width: 800px;
    width: 95%;
}

.modal-content.extra-large-modal {
    max-width: 1200px;
    width: 98%;
    max-height: 95vh;
}

/* Print Bill Customization Modal - Extra Large */
.modal-content.extra-large {
    max-width: 1600px;
    width: 95%;
    height: 90vh;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

/* Cash Modal Specific Styles */
.cash-modal .cash-overview {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.cash-modal .balance-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid #e5e7eb;
}

.cash-modal .balance-card.main-balance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.cash-modal .balance-card.inflow {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    border: none;
}

.cash-modal .balance-card.outflow {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
}

.cash-modal .balance-card.net-flow {
    background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
    color: white;
    border: none;
}

.cash-modal .balance-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.cash-modal .balance-content h2,
.cash-modal .balance-content h3 {
    margin: 0;
    font-weight: 600;
}

.cash-modal .balance-content p {
    margin: 0.25rem 0;
    opacity: 0.9;
}

.cash-modal .balance-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.cash-modal .balance-status.healthy {
    background: rgba(34, 197, 94, 0.2);
    color: #15803d;
}

.cash-modal .balance-status.warning {
    background: rgba(251, 191, 36, 0.2);
    color: #d97706;
}

.cash-modal .balance-status.critical {
    background: rgba(239, 68, 68, 0.2);
    color: #dc2626;
}

.cash-modal .balance-status.not-set {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.cash-modal .cash-breakdown {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.cash-modal .breakdown-section h4 {
    margin-bottom: 1rem;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cash-modal .breakdown-grid {
    display: grid;
    gap: 0.75rem;
}

.cash-modal .breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 6px;
    border-left: 4px solid #e5e7eb;
}

.cash-modal .breakdown-item:nth-child(1) { border-left-color: #3b82f6; }
.cash-modal .breakdown-item:nth-child(2) { border-left-color: #10b981; }
.cash-modal .breakdown-item:nth-child(3) { border-left-color: #f59e0b; }
.cash-modal .breakdown-item:nth-child(4) { border-left-color: #8b5cf6; }

.cash-modal .breakdown-amount {
    font-weight: 600;
    color: #111827;
}

.cash-modal .cash-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.cash-modal .recent-transactions h4 {
    margin-bottom: 1rem;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cash-modal .transactions-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.cash-modal .tab-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cash-modal .tab-btn:hover {
    background: #f3f4f6;
}

.cash-modal .tab-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.cash-modal .tab-content {
    display: none;
}

.cash-modal .tab-content.active {
    display: block;
}

/* Sales Modal Specific Styles */
.sales-modal .insights-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.sales-modal .date-range-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sales-modal .date-range-selector label {
    font-weight: 500;
    color: #374151;
}

.sales-modal .custom-date-range {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sales-modal .insights-actions {
    display: flex;
    gap: 0.5rem;
}

.sales-modal .revenue-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.sales-modal .revenue-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid #e5e7eb;
    transition: transform 0.2s, box-shadow 0.2s;
}

.sales-modal .revenue-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sales-modal .revenue-card.total-revenue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.sales-modal .revenue-card.orders-revenue {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    border: none;
}

.sales-modal .revenue-card.udhar-revenue {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
}

.sales-modal .revenue-card.cash-position {
    background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
    color: white;
    border: none;
}

.sales-modal .revenue-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.sales-modal .revenue-content h3 {
    margin: 0;
    font-weight: 600;
    font-size: 1.5rem;
}

.sales-modal .revenue-content p {
    margin: 0.25rem 0;
    opacity: 0.9;
}

.sales-modal .revenue-content small {
    opacity: 0.8;
    font-size: 0.8rem;
}

.sales-modal .insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.sales-modal .insight-panel {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.sales-modal .insight-panel:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sales-modal .panel-header {
    background: #f9fafb;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sales-modal .panel-header h4 {
    margin: 0;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.sales-modal .panel-content {
    padding: 1rem;
}

.sales-modal .chart-container {
    min-height: 150px;
}

.sales-modal .revenue-breakdown,
.sales-modal .cash-flow-summary {
    display: grid;
    gap: 0.75rem;
}

.sales-modal .breakdown-item,
.sales-modal .cash-flow-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 6px;
    border-left: 4px solid #e5e7eb;
}

.sales-modal .breakdown-item:nth-child(1) { border-left-color: #3b82f6; }
.sales-modal .breakdown-item:nth-child(2) { border-left-color: #10b981; }

.sales-modal .cash-flow-item.total {
    background: #f3f4f6;
    border-left-color: #6b7280;
    font-weight: 600;
}

.sales-modal .breakdown-percentage,
.sales-modal .flow-amount {
    font-weight: 600;
}

.sales-modal .flow-amount.positive {
    color: #059669;
}

.sales-modal .flow-amount.negative {
    color: #dc2626;
}

.sales-modal .chart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.sales-modal .chart-label {
    min-width: 120px;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.sales-modal .chart-bar {
    flex: 1;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.sales-modal .chart-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.sales-modal .chart-percentage {
    min-width: 50px;
    text-align: right;
    font-weight: 600;
    color: #374151;
}

.sales-modal .top-items-section {
    margin-top: 2rem;
}

.sales-modal .top-items-section h4 {
    margin-bottom: 1rem;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sales-modal .rank-badge {
    background: #3b82f6;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.sales-modal .percentage-badge {
    background: #10b981;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.sales-modal .no-data {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
    font-style: italic;
}

/* Analytics Actions in Reports Header */
.analytics-actions {
    display: flex;
    gap: 0.5rem;
}

.analytics-actions .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive Design for Enhanced Modals */
@media (max-width: 768px) {
    .modal-content.large-modal,
    .modal-content.extra-large-modal {
        width: 95%;
        max-width: none;
        max-height: 90vh;
    }

    .cash-modal .cash-overview {
        grid-template-columns: 1fr;
    }

    .cash-modal .cash-breakdown {
        grid-template-columns: 1fr;
    }

    .cash-modal .cash-actions {
        flex-direction: column;
    }

    .sales-modal .insights-grid {
        grid-template-columns: 1fr;
    }

    .sales-modal .insights-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .sales-modal .revenue-overview {
        grid-template-columns: 1fr;
    }

    .analytics-actions {
        flex-direction: column;
        width: 100%;
    }

    .analytics-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Enhanced Cash Modal Transaction Management */
.cash-modal .transactions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.cash-modal .tab-actions {
    display: flex;
    gap: 0.5rem;
}

.cash-modal .transaction-actions {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.25rem;
}

.cash-modal .btn-icon {
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.8rem;
}

.cash-modal .btn-icon.btn-edit {
    color: #3b82f6;
}

.cash-modal .btn-icon.btn-edit:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
}

.cash-modal .btn-icon.btn-delete {
    color: #ef4444;
}

.cash-modal .btn-icon.btn-delete:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.cash-modal .reference-badge,
.cash-modal .category-badge {
    background: #f3f4f6;
    color: #374151;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 0.25rem;
}

.cash-modal .reference-badge {
    background: #dbeafe;
    color: #1e40af;
}

.cash-modal .category-badge {
    background: #fef3c7;
    color: #92400e;
}

.cash-modal .no-data,
.cash-modal .error-message {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
    font-style: italic;
}

.cash-modal .error-message {
    color: #ef4444;
}

/* Clickable dashboard card styles */
.stat-card.cash-balance-card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card.cash-balance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.cash-balance-card.cash-healthy {
    border-left: 4px solid #10b981;
}

.stat-card.cash-balance-card.cash-warning {
    border-left: 4px solid #f59e0b;
}

.stat-card.cash-balance-card.cash-critical {
    border-left: 4px solid #ef4444;
}

.stat-card.cash-balance-card.cash-not-set {
    border-left: 4px solid #6b7280;
}

.stat-change.healthy {
    color: #10b981;
}

.stat-change.warning {
    color: #f59e0b;
}

.stat-change.critical {
    color: #ef4444;
}

.stat-change.not-set {
    color: #6b7280;
}

/* Responsive adjustments for transaction management */
@media (max-width: 768px) {
    .cash-modal .transactions-header {
        flex-direction: column;
        align-items: stretch;
    }

    .cash-modal .tab-actions {
        width: 100%;
        justify-content: center;
    }

    .cash-modal .transaction-actions {
        justify-content: center;
    }
}

/* Enhanced Transaction Details Modal */
.transaction-details {
    max-height: 60vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.detail-section h4 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1.1rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-weight: 600;
    color: #6b7280;
    font-size: 0.875rem;
}

.detail-item span {
    color: #111827;
    font-weight: 500;
}

.payment-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.payment-badge.cash {
    background: #dcfce7;
    color: #166534;
}

.payment-badge.card {
    background: #dbeafe;
    color: #1e40af;
}

.payment-badge.udhar {
    background: #fef3c7;
    color: #92400e;
}

.amount-display {
    font-size: 1.1rem;
    font-weight: 700;
    color: #059669;
}

.items-list {
    background: white;
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid #e5e7eb;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.item-row:last-child {
    border-bottom: none;
}

.item-name {
    flex: 1;
    font-weight: 500;
}

.item-quantity {
    color: #6b7280;
    margin: 0 1rem;
}

.item-price {
    font-weight: 600;
    color: #059669;
}

/* Enhanced transaction action buttons */
.cash-modal .btn-icon.btn-view {
    color: #6b7280;
}

.cash-modal .btn-icon.btn-view:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
}

/* Extra large modal for cash management */
.modal-content.extra-large-modal.cash-modal {
    max-width: 1400px;
    width: 98%;
}

/* Improved table responsiveness */
.cash-modal .table-responsive {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
}

.cash-modal .data-table {
    margin-bottom: 0;
}

.cash-modal .data-table th {
    position: sticky;
    top: 0;
    background: #f9fafb;
    z-index: 10;
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
    .modal-content.extra-large-modal.cash-modal {
        width: 95%;
        max-height: 90vh;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .item-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .cash-modal .table-responsive {
        max-height: 300px;
    }
}

/* Withdrawal Modal Styling */
.withdrawal-info {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.available-balance {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.available-balance label {
    font-weight: 600;
    color: #374151;
}

.balance-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: #059669;
}

.withdrawal-warning {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 0.75rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.withdrawal-warning i {
    color: #d97706;
}

.withdrawal-warning span {
    color: #92400e;
    font-size: 0.875rem;
}

/* Enhanced action buttons for withdrawals */
.btn-warning {
    background: #f59e0b;
    color: white;
    border: 1px solid #f59e0b;
}

.btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
}

/* Custom Category Management Styles */
.category-management-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.add-category-section {
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.add-category-section h4 {
    margin-bottom: 1rem;
    color: var(--gray-900);
    font-weight: 600;
}

.existing-categories-section h4 {
    margin-bottom: 1rem;
    color: var(--gray-900);
    font-weight: 600;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.category-card {
    padding: 1rem;
    background: var(--white);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
    transition: all 0.2s;
}

.category-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-card.custom {
    border-left: 4px solid var(--primary-color);
}

.category-card.default {
    border-left: 4px solid var(--gray-400);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.category-icon {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: 0.375rem;
}

.category-info h5 {
    margin: 0;
    font-weight: 600;
    color: var(--gray-900);
}

.category-info small {
    color: var(--gray-600);
}

.category-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.default-badge {
    padding: 0.25rem 0.5rem;
    background: var(--gray-100);
    color: var(--gray-600);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.category-actions .category-actions {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--gray-200);
}

/* POS Category Management Styles */
.category-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
    padding: 0.5rem;
}

.category-actions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    white-space: nowrap;
}

/* Enhanced POS Category Tabs */
.category-tabs.modern-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
}

.tab-btn:hover {
    background: var(--gray-100);
    border-color: var(--primary-color);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tab-btn i {
    font-size: 1rem;
}

.tab-btn span {
    font-weight: 500;
}

/* Custom category colors in tabs */
.tab-btn[data-category^="custom_"] i {
    filter: brightness(0.8);
}

.tab-btn.active[data-category^="custom_"] i {
    filter: brightness(1.2);
}

/* Category badges for different transaction types */
.category-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.category-badge:contains("Withdrawal") {
    background: #fef3c7;
    color: #92400e;
}

.category-badge:contains("Owner") {
    background: #e0e7ff;
    color: #3730a3;
}

.category-badge:contains("Bank") {
    background: #dcfce7;
    color: #166534;
}

.category-badge:contains("Emergency") {
    background: #fee2e2;
    color: #991b1b;
}

/* Enhanced transaction type styling */
.source-badge.withdrawal {
    background: #fef3c7;
    color: #92400e;
}

.source-badge.expense {
    background: #fee2e2;
    color: #991b1b;
}

.source-badge.order {
    background: #dcfce7;
    color: #166534;
}

.source-badge.udhar {
    background: #dbeafe;
    color: #1e40af;
}

/* Print Bill Customization Styles */
.bill-customization-container {
    display: flex;
    flex-direction: column;
    height: 80vh;
    max-height: 800px;
    width: 100%;
}

.customization-tabs {
    display: flex;
    border-bottom: 2px solid var(--gray-200);
    margin-bottom: 20px;
    overflow-x: auto;
}

.customization-tabs .tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    color: var(--gray-700);
    font-size: 14px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.customization-tabs .tab-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.customization-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--gray-50);
}

.customization-content {
    display: flex;
    gap: 30px;
    flex: 1;
    overflow: hidden;
}

.customization-panel {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 15px;
    max-height: calc(80vh - 200px);
}

.preview-panel {
    flex: 1;
    border-left: 2px solid var(--gray-200);
    padding-left: 30px;
    display: flex;
    flex-direction: column;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h4 {
    margin: 0 0 20px 0;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Logo Upload Styles */
.logo-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: var(--gray-50);
    transition: all 0.3s ease;
}

.logo-upload-area:hover {
    border-color: var(--primary-color);
    background: var(--gray-100);
}

.upload-zone {
    cursor: pointer;
    padding: 20px;
}

.logo-preview {
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.logo-preview img {
    max-height: 100px;
    max-width: 200px;
    object-fit: contain;
    border-radius: 4px;
}

.logo-preview i {
    font-size: 48px;
    color: var(--gray-400);
}

.logo-preview p {
    margin: 0;
    color: var(--gray-700);
    font-weight: 500;
}

.logo-preview small {
    color: var(--gray-500);
    font-size: 12px;
}

.logo-controls {
    margin-top: 15px;
}

/* Preview Panel Styles */
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.preview-header h4 {
    margin: 0;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-controls select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    background: var(--white);
    color: var(--gray-700);
}

.bill-preview-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    justify-content: center;
    max-height: calc(80vh - 200px);
}

.bill-preview {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 350px;
    width: 100%;
    height: fit-content;
}

.bill-preview-content {
    font-family: 'Courier New', monospace;
    line-height: 1.4;
}

/* Additional Print Bill Customization Styles */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    margin: 0;
}

.preview-options,
.test-scenarios,
.settings-management {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.preview-options .btn,
.test-scenarios .btn,
.settings-management .btn {
    flex: 1;
    min-width: 120px;
}

.test-scenarios .btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* Form styling improvements */
.form-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--gray-700);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    background: var(--white);
    color: var(--gray-700);
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

textarea.form-control {
    resize: vertical;
    min-height: 60px;
}

/* Print Bill Customization Responsive Design */
@media (max-width: 1400px) {
    .modal-content.extra-large {
        width: 98%;
        height: 85vh;
    }

    .customization-panel {
        max-height: calc(85vh - 220px);
    }

    .bill-preview-container {
        max-height: calc(85vh - 220px);
    }
}

@media (max-width: 1200px) {
    .modal-content.extra-large {
        width: 95%;
        height: 90vh;
    }

    .customization-content {
        flex-direction: column;
        gap: 20px;
    }

    .preview-panel {
        border-left: none;
        border-top: 2px solid var(--gray-200);
        padding-left: 0;
        padding-top: 20px;
        max-height: 400px;
    }

    .customization-panel {
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .modal-content.extra-large {
        width: 98%;
        height: 95vh;
    }

    .customization-tabs {
        flex-wrap: wrap;
    }

    .customization-tabs .tab-btn {
        flex: 1;
        min-width: 120px;
        padding: 10px 15px;
        font-size: 13px;
    }

    .footer-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-actions .btn {
        font-size: 13px;
        padding: 8px 12px;
        min-width: 100px;
    }
}

/* Usage History Folder Layout Styles */
.usage-history-folders {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 600px;
    overflow-y: auto;
    padding: 10px;
    background: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
}

.usage-folder {
    background: var(--white);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.usage-folder:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.usage-folder.manual-usage {
    border-left: 4px solid #f59e0b;
}

.folder-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.folder-header:hover {
    background: var(--gray-50);
}

.folder-icon {
    margin-right: 12px;
    color: var(--primary-color);
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.folder-info {
    flex: 1;
    min-width: 0;
}

.folder-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
}

.order-number {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 16px;
}

.order-date {
    color: var(--gray-600);
    font-size: 14px;
    background: var(--gray-100);
    padding: 2px 8px;
    border-radius: 12px;
}

.folder-summary {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 13px;
    color: var(--gray-600);
}

.ingredient-count {
    color: var(--primary-color);
    font-weight: 500;
}

.order-amount {
    color: var(--success-color);
    font-weight: 600;
}

.order-time {
    color: var(--gray-500);
}

.folder-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.expand-indicator {
    color: var(--gray-400);
    transition: transform 0.3s ease;
}

.folder-content {
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    padding: 0;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 500px;
    }
}

.ingredient-usage-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--gray-200);
    transition: background-color 0.2s ease;
}

.ingredient-usage-item:last-child {
    border-bottom: none;
}

.ingredient-usage-item:hover {
    background: var(--white);
}

.ingredient-usage-item.manual {
    background: #fef3c7;
}

.ingredient-info {
    flex: 1;
    min-width: 0;
}

.ingredient-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 14px;
    margin-bottom: 2px;
}

.ingredient-details {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: var(--gray-600);
}

.menu-item {
    color: var(--primary-color);
    font-weight: 500;
}

.usage-date {
    color: var(--gray-500);
}

.usage-reason {
    color: var(--warning-color);
    font-style: italic;
}

.usage-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    margin: 0 16px;
}

.quantity-used .usage-amount {
    color: var(--error-color);
    font-weight: 600;
    font-size: 14px;
}

.remaining-stock {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.stock-label {
    color: var(--gray-500);
}

.stock-amount {
    color: var(--success-color);
    font-weight: 500;
}

.usage-type .type-badge {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.type-badge.manual {
    background: #fef3c7;
    color: #92400e;
}

.type-badge.order_fulfillment {
    background: #dbeafe;
    color: #1e40af;
}

.type-badge.waste {
    background: #fee2e2;
    color: #dc2626;
}

.usage-actions {
    display: flex;
    gap: 4px;
}

.btn-xs {
    padding: 4px 8px;
    font-size: 11px;
    min-width: auto;
}

/* Order Usage Details Modal */
.order-summary {
    background: var(--gray-50);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--gray-200);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--gray-200);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item .label {
    font-weight: 600;
    color: var(--gray-700);
}

.summary-item .value {
    color: var(--gray-900);
    font-weight: 500;
}

.usage-details-table {
    margin-top: 20px;
}

.ingredient-cell {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.ingredient-name {
    font-weight: 600;
    color: var(--gray-900);
}

.ingredient-id {
    color: var(--gray-500);
    font-size: 11px;
}

.usage-time {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.usage-time .time {
    font-weight: 500;
    color: var(--gray-900);
}

.usage-time .date {
    color: var(--gray-500);
    font-size: 11px;
}

/* Responsive improvements for mobile */
@media (max-width: 768px) {
    .folder-header {
        padding: 12px;
    }

    .folder-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .folder-summary {
        flex-wrap: wrap;
        gap: 8px;
    }

    .ingredient-usage-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
    }

    .usage-details {
        align-items: flex-start;
        margin: 0;
    }
}

@media (max-width: 480px) {
    .preview-options,
    .test-scenarios,
    .settings-management {
        flex-direction: column;
    }

    .preview-options .btn,
    .test-scenarios .btn,
    .settings-management .btn {
        width: 100%;
        min-width: auto;
    }

    .checkbox-group {
        gap: 12px;
    }

    .checkbox-group label {
        font-size: 13px;
    }

    .usage-history-folders {
        max-height: 400px;
        padding: 8px;
    }

    .folder-actions {
        flex-direction: column;
        gap: 4px;
    }
}

/* Enhanced Inventory Purchase Modal Styles */
.existing-inventory-fields {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
    border: 1px solid #e9ecef;
}

.new-inventory-fields {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
    border: 1px solid #e9ecef;
}

/* Time Period Buttons Styles */
.time-period-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.period-btn {
    min-width: 100px;
    transition: all 0.3s ease;
}

.period-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.period-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Full Page Report Modal Styles */
.full-page-report .modal-content {
    max-width: 95vw;
    max-height: 95vh;
    overflow-y: auto;
}

.extra-large-modal {
    width: 95vw;
    max-width: 1400px;
}

.report-period-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.period-badge {
    background: #007bff;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.date-range {
    color: #6c757d;
    font-size: 14px;
}

.report-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.summary-card.revenue {
    border-left-color: #28a745;
}

.summary-card.expenses {
    border-left-color: #dc3545;
}

.summary-card.profit.positive {
    border-left-color: #28a745;
}

.summary-card.profit.negative {
    border-left-color: #dc3545;
}

.summary-card.customers {
    border-left-color: #17a2b8;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 20px;
}

.revenue .card-icon {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.expenses .card-icon {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.profit .card-icon {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.customers .card-icon {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.card-content h4 {
    margin: 0 0 10px 0;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

.card-content .amount {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.card-content small {
    color: #6c757d;
    font-size: 12px;
}

.report-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.report-section h4 {
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
}

.report-table th,
.report-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.report-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.rank-badge {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

/* Inventory Controls Styles */
.inventory-controls {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-section {
    margin-bottom: 15px;
}

.search-input-group {
    position: relative;
    max-width: 400px;
}

.search-input-group i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-input-group input {
    padding-left: 40px;
}

.filter-section {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.category-filter,
.stock-filter {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.category-filter label,
.stock-filter label {
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

/* Category Management Modal Styles */
.category-management-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.category-management-section h4 {
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.category-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.category-info h5 {
    margin: 0 0 5px 0;
    color: #333;
}

.category-info small {
    color: #6c757d;
}

.category-actions {
    display: flex;
    gap: 5px;
}

.category-assignment {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

/* Enhanced Inventory Table Styles */
.inventory-table-content {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.inventory-table-content th,
.inventory-table-content td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.inventory-table-content th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.inventory-item-row.out-of-stock {
    background: rgba(220, 53, 69, 0.05);
}

.inventory-item-row.low-stock {
    background: rgba(255, 193, 7, 0.05);
}

.item-name strong {
    color: #333;
}

.item-name small {
    display: block;
    color: #6c757d;
    font-size: 12px;
}

.category-badge {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.stock-amount.out-of-stock {
    color: #dc3545;
    font-weight: bold;
}

.stock-amount.low-stock {
    color: #ffc107;
    font-weight: bold;
}

.stock-amount.in-stock {
    color: #28a745;
    font-weight: bold;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-badge.in-stock {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-badge.low-stock {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.status-badge.out-of-stock {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.item-actions {
    display: flex;
    gap: 5px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h4 {
    margin-bottom: 10px;
    color: #495057;
}

/* Responsive Design */
@media (max-width: 768px) {
    .time-period-buttons {
        flex-direction: column;
    }

    .period-btn {
        width: 100%;
    }

    .report-summary-cards {
        grid-template-columns: 1fr;
    }

    .filter-section {
        flex-direction: column;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .extra-large-modal {
        width: 95vw;
        margin: 10px;
    }
}

/* ========================================
   ENHANCED REPORTS PAGE STYLES - COMPREHENSIVE
   ======================================== */

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 20px;
    text-align: center;
}

.loading-spinner {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.loading-spinner i {
    animation: spin 1s linear infinite;
}

/* Quick Stats Bar */
.quick-stats-bar {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    overflow-x: auto;
}

.quick-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 150px;
    text-align: center;
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
}

.stat-value.positive {
    color: #4ade80;
}

.stat-value.negative {
    color: #f87171;
}

/* Business Intelligence Section */
.business-intelligence-section {
    margin-bottom: 30px;
    padding: 25px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.business-intelligence-section h2 {
    margin: 0 0 25px 0;
    color: var(--gray-900);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.intelligence-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.intelligence-card {
    background: var(--gray-50);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--gray-200);
}

.intelligence-card h3 {
    margin: 0 0 15px 0;
    color: var(--gray-800);
    font-size: 1.1rem;
}

/* Optimization Suggestions */
.optimization-suggestions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.suggestion-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
}

.suggestion-item.high {
    background: #fef2f2;
    border-left-color: #ef4444;
}

.suggestion-item.medium {
    background: #fffbeb;
    border-left-color: #f59e0b;
}

.suggestion-item.low {
    background: #f0fdf4;
    border-left-color: #10b981;
}

.suggestion-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.suggestion-item.high .suggestion-icon {
    background: #ef4444;
}

.suggestion-item.medium .suggestion-icon {
    background: #f59e0b;
}

.suggestion-item.low .suggestion-icon {
    background: #10b981;
}

.suggestion-content h5 {
    margin: 0 0 8px 0;
    color: var(--gray-900);
    font-size: 14px;
}

.suggestion-content p {
    margin: 0 0 10px 0;
    color: var(--gray-600);
    font-size: 13px;
    line-height: 1.4;
}

.suggestion-impact {
    font-size: 12px;
    color: var(--gray-700);
}

/* Advanced Reports Section */
.advanced-reports-section {
    margin-bottom: 30px;
    padding: 25px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.advanced-reports-section h2 {
    margin: 0 0 25px 0;
    color: var(--gray-900);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.advanced-reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.report-card {
    background: var(--gray-50);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--gray-200);
}

.report-card h3 {
    margin: 0 0 15px 0;
    color: var(--gray-800);
    font-size: 1.1rem;
}

.report-features {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--gray-600);
    font-size: 14px;
}

.feature-item i {
    color: var(--primary-color);
    width: 16px;
}

/* Cost Management Section */
.cost-management-section {
    margin-bottom: 30px;
    padding: 25px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.cost-management-section h2 {
    margin: 0 0 25px 0;
    color: var(--gray-900);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.cost-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tool-card {
    background: var(--gray-50);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--gray-200);
    text-align: center;
}

.tool-card h3 {
    margin: 0 0 15px 0;
    color: var(--gray-800);
    font-size: 1.1rem;
}

/* Export & Backup Section */
.export-backup-section {
    margin-bottom: 30px;
    padding: 25px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.export-backup-section h2 {
    margin: 0 0 25px 0;
    color: var(--gray-900);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.export-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

/* Enhanced Button Styles */
.btn-secondary {
    background-color: var(--gray-500);
    color: white;
    border: 1px solid var(--gray-500);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-600);
    border-color: var(--gray-600);
}

/* Status Badges */
.status-badge.active {
    background-color: #dcfce7;
    color: #16a34a;
}

.status-badge.inactive {
    background-color: #fee2e2;
    color: #dc2626;
}

.status-badge.paid {
    background-color: #dcfce7;
    color: #16a34a;
}

.status-badge.high-risk {
    background-color: #fee2e2;
    color: #dc2626;
}

.status-badge.out-of-stock {
    background-color: #fee2e2;
    color: #dc2626;
}

.status-badge.low-stock {
    background-color: #fef3c7;
    color: #d97706;
}

.status-badge.in-stock {
    background-color: #dcfce7;
    color: #16a34a;
}

/* Performance Rating */
.performance-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating-stars {
    color: #fbbf24;
    font-size: 14px;
}

.rating-text {
    font-size: 12px;
    color: var(--gray-600);
}

/* Responsive Design for Enhanced Reports */
@media (max-width: 768px) {
    .quick-stats-bar {
        flex-direction: column;
        gap: 10px;
    }

    .quick-stat {
        min-width: auto;
        flex-direction: row;
        justify-content: space-between;
        padding: 10px;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
    }

    .intelligence-grid,
    .advanced-reports-grid,
    .cost-tools-grid {
        grid-template-columns: 1fr;
    }

    .export-options {
        flex-direction: column;
    }

    .export-options .btn {
        width: 100%;
    }
}

/* ========================================
   BUSINESS DAY MANAGER STYLES
   ======================================== */

/* Day Transition Button */
.day-transition-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: none;
    color: white;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.day-transition-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
}

/* Day Transition Notification */
.day-transition-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #f59e0b;
    z-index: 10000;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-content {
    padding: 20px;
    display: flex;
    gap: 15px;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
}

.notification-text h4 {
    margin: 0 0 8px 0;
    color: var(--gray-900);
    font-size: 16px;
}

.notification-text p {
    margin: 0 0 4px 0;
    color: var(--gray-600);
    font-size: 14px;
}

.notification-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* Day Transition Modal */
.day-transition-modal {
    max-width: 600px;
    width: 90%;
}

.day-transition-info {
    background: var(--gray-50);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item label {
    font-weight: 600;
    color: var(--gray-700);
}

.current-date {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 16px;
}

.transition-options {
    margin-bottom: 25px;
}

.transition-options h3 {
    margin: 0 0 20px 0;
    color: var(--gray-900);
    font-size: 18px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--gray-200);
}

.option-group {
    margin-bottom: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 10px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.checkbox-label:hover {
    background-color: var(--gray-50);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.warning-message {
    background: #fef3c7;
    border: 1px solid #fcd34d;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    color: #92400e;
}

.warning-message i {
    color: #f59e0b;
    margin-top: 2px;
    flex-shrink: 0;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button Variants */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-warning {
    background-color: #f59e0b;
    color: white;
    border: 1px solid #f59e0b;
}

.btn-warning:hover:not(:disabled) {
    background-color: #d97706;
    border-color: #d97706;
}

/* Responsive Design */
@media (max-width: 768px) {
    .day-transition-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification-content {
        padding: 15px;
        flex-direction: column;
        text-align: center;
    }

    .notification-actions {
        justify-content: center;
    }

    .day-transition-modal {
        width: 95%;
        margin: 20px auto;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .notification-actions {
        flex-direction: column;
    }
}

/* ========================================
   SYSTEM VERIFICATION MODAL STYLES
   ======================================== */

.verification-summary-modal .modal-content {
    max-width: 700px;
    width: 90%;
}

.verification-status {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.verification-status h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
}

.status-excellent {
    background: #dcfce7;
    color: #16a34a;
    border: 2px solid #22c55e;
}

.status-good {
    background: #dbeafe;
    color: #2563eb;
    border: 2px solid #3b82f6;
}

.status-fair {
    background: #fef3c7;
    color: #d97706;
    border: 2px solid #f59e0b;
}

.status-poor {
    background: #fee2e2;
    color: #dc2626;
    border: 2px solid #ef4444;
}

.test-results,
.recommendations {
    margin-bottom: 25px;
}

.test-results h4,
.recommendations h4 {
    margin: 0 0 15px 0;
    color: var(--gray-900);
    font-size: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-200);
}

.test-results ul,
.recommendations ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.test-results li,
.recommendations li {
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 6px;
    font-size: 14px;
}

.test-results li.pass {
    background: #dcfce7;
    color: #16a34a;
    border-left: 4px solid #22c55e;
}

.test-results li.fail {
    background: #fee2e2;
    color: #dc2626;
    border-left: 4px solid #ef4444;
}

.recommendations li {
    background: var(--gray-50);
    color: var(--gray-700);
    border-left: 4px solid var(--primary-color);
}

/* Responsive Design for Verification Modal */
@media (max-width: 768px) {
    .verification-summary-modal .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .verification-status h3 {
        font-size: 18px;
    }

    .test-results li,
    .recommendations li {
        font-size: 13px;
        padding: 6px 10px;
    }
}

/* ========================================
   ENHANCED CASH MANAGEMENT STYLES
   ======================================== */

/* Day-End Expenses Modal */
.day-end-expenses-modal .modal-content {
    max-width: 900px;
    width: 95%;
}

.expense-categories {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 30px;
}

.expense-category {
    background: var(--gray-50);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--gray-200);
}

.expense-category h3 {
    margin: 0 0 20px 0;
    color: var(--gray-900);
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--gray-200);
}

.expense-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.expense-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
    margin: 0;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkbox-label span {
    font-size: 14px;
    color: var(--gray-700);
    font-weight: 500;
}

.amount-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.amount-input input {
    width: 120px;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
    text-align: right;
}

.amount-input span {
    font-size: 12px;
    color: var(--gray-500);
    font-weight: 600;
}

/* Manual Expense Form */
.manual-expense-form {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--gray-200);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group select {
    padding: 10px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Manual Expenses List */
.manual-expenses-list {
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.manual-expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: var(--gray-50);
    border-radius: 6px;
    border: 1px solid var(--gray-200);
}

.expense-desc {
    font-weight: 500;
    color: var(--gray-900);
    flex: 1;
}

.expense-cat {
    font-size: 12px;
    color: var(--gray-600);
    background: var(--gray-200);
    padding: 2px 8px;
    border-radius: 4px;
    margin: 0 10px;
}

.expense-amt {
    font-weight: 600;
    color: var(--gray-900);
    margin-right: 10px;
}

/* Expense Summary */
.expense-summary {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--gray-200);
    margin-top: 20px;
}

.expense-summary h3 {
    margin: 0 0 15px 0;
    color: var(--gray-900);
    font-size: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--gray-200);
}

.expense-summary-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--gray-100);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item .category {
    font-size: 11px;
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    margin-right: 10px;
}

.summary-item .description {
    flex: 1;
    font-size: 14px;
    color: var(--gray-700);
}

.summary-item .amount {
    font-weight: 600;
    color: var(--gray-900);
}

.summary-total {
    padding: 15px 0;
    border-top: 2px solid var(--gray-200);
    text-align: right;
    font-size: 16px;
    color: var(--gray-900);
}

/* No Staff Message */
.no-staff {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px dashed var(--gray-300);
}

/* Cash Transactions Display */
.cash-transactions-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.transaction-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.transaction-item.cash .transaction-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.transaction-item.expense .transaction-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.transaction-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.transaction-description {
    font-weight: 500;
    color: var(--gray-900);
    font-size: 14px;
}

.transaction-meta {
    display: flex;
    gap: 10px;
    font-size: 12px;
}

.transaction-type {
    color: var(--primary-color);
    font-weight: 500;
}

.transaction-category {
    color: var(--gray-600);
}

.transaction-time {
    font-size: 11px;
    color: var(--gray-500);
}

.transaction-amount {
    font-weight: 700;
    font-size: 16px;
}

.transaction-amount.positive {
    color: #10b981;
}

.transaction-amount.negative {
    color: #ef4444;
}

/* No Transactions Message */
.no-transactions,
.error-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray-500);
}

.no-transactions i,
.error-message i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .day-end-expenses-modal .modal-content {
        width: 98%;
        margin: 10px auto;
    }

    .expense-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .amount-input {
        width: 100%;
        justify-content: flex-end;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .manual-expense-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .transaction-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .transaction-details {
        text-align: center;
    }
}

/* ========================================
   ORIGINAL CASH MODAL STYLES
   ======================================== */

/* Original Cash Modal Styles */
.cash-display {
    text-align: center;
    padding: 20px;
    background: var(--gray-50);
    border-radius: 8px;
    margin-bottom: 20px;
}

.cash-display h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 24px;
    font-weight: 700;
}

.cash-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.cash-section {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 20px;
}

.cash-section h4 {
    margin: 0 0 15px 0;
    color: var(--gray-800);
    font-size: 16px;
    text-align: center;
}

.cash-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.cash-form input {
    padding: 10px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
}

.cash-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.cash-form button {
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.recent-transactions {
    margin-top: 25px;
}

.recent-transactions h4 {
    margin: 0 0 15px 0;
    color: var(--gray-800);
    font-size: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-200);
}

.transactions-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    border-radius: 6px;
    background: white;
}

.loading {
    text-align: center;
    padding: 20px;
    color: var(--gray-500);
    font-style: italic;
}

/* Responsive design for original cash modal */
@media (max-width: 768px) {
    .cash-sections {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .cash-section {
        padding: 15px;
    }

    .cash-display h3 {
        font-size: 20px;
    }
}

/* ========================================
   AUTO EXPENSE MANAGEMENT STYLES
   ======================================== */

.auto-expense-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.section-header h2 {
    margin: 0;
    color: var(--gray-900);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.auto-expense-toggle {
    display: flex;
    align-items: center;
    gap: 15px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    font-weight: 600;
    color: var(--gray-700);
}

.auto-expense-status {
    display: flex;
    gap: 30px;
    margin-bottom: 25px;
    padding: 15px;
    background: var(--gray-50);
    border-radius: 8px;
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.status-label {
    font-size: 12px;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-value {
    font-weight: 600;
    font-size: 14px;
}

.status-value.enabled {
    color: #10b981;
}

.status-value.disabled {
    color: #ef4444;
}

.auto-expense-config h3 {
    margin: 0 0 20px 0;
    color: var(--gray-800);
    font-size: 1.2rem;
}

.auto-expense-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 25px;
}

.auto-expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.auto-expense-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.expense-info {
    flex: 1;
}

.expense-name {
    font-weight: 500;
    color: var(--gray-900);
}

.expense-amount {
    display: flex;
    align-items: center;
    gap: 8px;
}

.expense-amount input {
    width: 120px;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    text-align: right;
}

.currency {
    font-size: 12px;
    color: var(--gray-500);
    font-weight: 600;
}

.config-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* ========================================
   ENHANCED STAFF MANAGEMENT STYLES
   ======================================== */

.staff-management-container {
    padding: 20px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--gray-200);
}

.page-header h1 {
    margin: 0;
    color: var(--gray-900);
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.staff-summary {
    margin-bottom: 30px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), #1e40af);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.card-content h3 {
    margin: 0 0 5px 0;
    color: var(--gray-600);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-900);
}

.staff-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.staff-sections h2 {
    margin: 0 0 20px 0;
    color: var(--gray-800);
    font-size: 1.5rem;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--gray-200);
}

.staff-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.staff-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.staff-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.staff-card.inactive {
    opacity: 0.7;
    border-color: var(--gray-300);
}

.staff-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--gray-200);
}

.staff-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981, #059669);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.staff-info {
    flex: 1;
}

.staff-info h3 {
    margin: 0 0 5px 0;
    color: var(--gray-900);
    font-size: 18px;
}

.staff-info .position {
    margin: 0 0 3px 0;
    color: var(--gray-600);
    font-size: 14px;
    font-weight: 500;
}

.staff-info .phone {
    margin: 0;
    color: var(--gray-500);
    font-size: 12px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: #dcfce7;
    color: #16a34a;
}

.status-badge.inactive {
    background: #fee2e2;
    color: #dc2626;
}

.staff-payment,
.staff-balance {
    margin-bottom: 15px;
}

.payment-info,
.balance-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.payment-item,
.balance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.payment-item .label,
.balance-item .label {
    color: var(--gray-600);
    font-weight: 500;
}

.payment-item .value,
.balance-item .value {
    color: var(--gray-900);
    font-weight: 600;
}

.balance.positive {
    color: #10b981;
}

.balance.negative {
    color: #ef4444;
}

.staff-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.no-staff {
    text-align: center;
    padding: 40px;
    color: var(--gray-500);
    font-style: italic;
    background: var(--gray-50);
    border-radius: 8px;
    border: 1px dashed var(--gray-300);
}

/* Staff Form Styles */
.staff-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-section {
    background: var(--gray-50);
    padding: 20px;
    border-radius: 8px;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: var(--gray-800);
    font-size: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-200);
}

.payment-type-selection {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.radio-label:hover {
    background-color: white;
}

.radio-label input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.calculated-display {
    margin-top: 10px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid var(--gray-200);
    font-size: 14px;
    color: var(--gray-600);
    font-style: italic;
}

/* Attendance Modal Styles */
.attendance-modal .modal-content {
    max-width: 700px;
    width: 90%;
}

.attendance-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 25px;
}

.attendance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
}

.attendance-item .staff-name {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 3px;
}

.attendance-item .staff-position {
    color: var(--gray-600);
    font-size: 14px;
    margin-bottom: 3px;
}

.attendance-item .staff-wage {
    color: var(--gray-500);
    font-size: 12px;
}

.status-present {
    color: #10b981;
    font-weight: 600;
    background: #dcfce7;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
}

.attendance-summary {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--gray-200);
}

.attendance-summary h3 {
    margin: 0 0 10px 0;
    color: var(--gray-800);
}

.attendance-summary p {
    margin: 5px 0;
    color: var(--gray-600);
}

/* Advance Modal Styles */
.advance-form {
    background: var(--gray-50);
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.advance-warning {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #fef3c7;
    border: 1px solid #fcd34d;
    border-radius: 6px;
    color: #92400e;
    font-size: 14px;
    margin-top: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .header-actions {
        width: 100%;
        justify-content: stretch;
    }

    .header-actions .btn {
        flex: 1;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .staff-grid {
        grid-template-columns: 1fr;
    }

    .staff-header {
        flex-direction: column;
        text-align: center;
    }

    .staff-actions {
        justify-content: center;
    }

    .auto-expense-status {
        flex-direction: column;
        gap: 15px;
    }

    .auto-expense-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .expense-amount {
        width: 100%;
        justify-content: flex-end;
    }
}
