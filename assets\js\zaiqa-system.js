/**
 * Zaiqa Restaurant Management System - Main Integration
 * Clean Architecture with Accurate Financial Systems
 * Integrates all modules with the original frontend
 */

class ZaiqaRestaurantSystem {
    constructor() {
        this.version = '2.0.0';
        this.initialized = false;
        
        // Core system components
        this.financialEngine = null;
        this.databaseManager = null;
        this.financialCalculations = null;
        
        // Initialize the system
        this.init();
    }

    /**
     * Initialize the complete restaurant system
     */
    async init() {
        try {
            console.log('🏪 Initializing Zaiqa Restaurant System v' + this.version);
            
            // Step 1: Initialize Financial Engine
            console.log('💰 Initializing Financial Engine...');
            this.financialEngine = new ZaiqaFinancialEngine();
            
            // Step 2: Initialize Database Manager
            console.log('🗄️ Initializing Database Manager...');
            this.databaseManager = new ZaiqaDatabaseManager();
            
            // Step 3: Initialize Financial Calculations
            console.log('📊 Initializing Financial Calculations...');
            this.financialCalculations = new ZaiqaFinancialCalculations(
                this.financialEngine, 
                this.databaseManager
            );
            
            // Step 4: Initialize sample data if needed
            await this.initializeSampleData();
            
            // Step 5: Set up frontend integration
            this.setupFrontendIntegration();
            
            this.initialized = true;
            console.log('✅ Zaiqa Restaurant System initialized successfully');
            
            // Notify frontend that system is ready
            this.notifySystemReady();
            
        } catch (error) {
            console.error('❌ System initialization failed:', error);
            throw new Error('Failed to initialize Zaiqa Restaurant System: ' + error.message);
        }
    }

    /**
     * Initialize sample data for testing and demonstration
     */
    async initializeSampleData() {
        try {
            console.log('🎯 Checking for existing data...');
            
            // Check if data already exists
            const existingOrders = this.databaseManager.read('orders');
            const existingMenuItems = this.databaseManager.read('menuItems');
            
            if (existingOrders.length === 0 && existingMenuItems.length === 0) {
                console.log('📝 Initializing sample data...');
                
                // Create sample menu items
                const sampleMenuItems = [
                    {
                        name: 'Chicken Karahi',
                        category: 'Main Course',
                        basePrice: 800,
                        isAvailable: true,
                        description: 'Traditional Pakistani chicken curry'
                    },
                    {
                        name: 'Mutton Biryani',
                        category: 'Main Course',
                        basePrice: 1200,
                        isAvailable: true,
                        description: 'Aromatic basmati rice with tender mutton'
                    },
                    {
                        name: 'Chicken Tikka',
                        category: 'Appetizers',
                        basePrice: 600,
                        isAvailable: true,
                        description: 'Grilled chicken pieces with spices'
                    },
                    {
                        name: 'Naan',
                        category: 'Bread',
                        basePrice: 80,
                        isAvailable: true,
                        description: 'Traditional Pakistani bread'
                    },
                    {
                        name: 'Fresh Lime Juice',
                        category: 'Beverages',
                        basePrice: 150,
                        isAvailable: true,
                        description: 'Freshly squeezed lime juice'
                    }
                ];

                for (const item of sampleMenuItems) {
                    this.databaseManager.create('menuItems', item);
                }

                // Create sample orders
                const sampleOrders = [
                    {
                        orderNumber: 'ORD001',
                        customerName: 'Ahmed Khan',
                        customerCount: 4,
                        serviceType: 'dine_in',
                        items: [
                            { name: 'Chicken Karahi', price: 800, quantity: 2 },
                            { name: 'Naan', price: 80, quantity: 8 },
                            { name: 'Fresh Lime Juice', price: 150, quantity: 4 }
                        ],
                        totalAmount: 2840,
                        paymentMethod: 'cash',
                        status: 'completed'
                    },
                    {
                        orderNumber: 'ORD002',
                        customerName: 'Fatima Ali',
                        customerCount: 2,
                        serviceType: 'takeaway',
                        items: [
                            { name: 'Mutton Biryani', price: 1200, quantity: 1 },
                            { name: 'Chicken Tikka', price: 600, quantity: 1 }
                        ],
                        totalAmount: 1800,
                        paymentMethod: 'card',
                        status: 'completed'
                    }
                ];

                for (const order of sampleOrders) {
                    this.databaseManager.create('orders', order);
                }

                // Create sample expenses
                const sampleExpenses = [
                    {
                        description: 'Grocery Shopping',
                        amount: 5000,
                        category: 'Food Supplies',
                        date: this.financialEngine.getCurrentBusinessDate()
                    },
                    {
                        description: 'Electricity Bill',
                        amount: 8000,
                        category: 'Utilities',
                        date: this.financialEngine.getCurrentBusinessDate()
                    }
                ];

                for (const expense of sampleExpenses) {
                    this.databaseManager.create('expenses', expense);
                }

                console.log('✅ Sample data initialized successfully');
            } else {
                console.log('✅ Existing data found, skipping sample data initialization');
            }
            
        } catch (error) {
            console.error('❌ Sample data initialization failed:', error);
        }
    }

    /**
     * Set up integration with the original frontend
     */
    setupFrontendIntegration() {
        try {
            console.log('🔗 Setting up frontend integration...');
            
            // Make system available globally for frontend access
            window.zaiqaSystem = this;
            
            // Create convenient access methods for the frontend
            window.getFinancialData = (type, ...args) => {
                return this.getFinancialData(type, ...args);
            };
            
            window.saveData = (table, data) => {
                return this.saveData(table, data);
            };
            
            window.getData = (table, filters) => {
                return this.getData(table, filters);
            };
            
            window.updateData = (table, id, updates) => {
                return this.updateData(table, id, updates);
            };
            
            window.deleteData = (table, id) => {
                return this.deleteData(table, id);
            };
            
            console.log('✅ Frontend integration setup complete');
            
        } catch (error) {
            console.error('❌ Frontend integration setup failed:', error);
        }
    }

    /**
     * Get financial data for frontend consumption
     */
    getFinancialData(type, ...args) {
        try {
            switch (type) {
                case 'todayMetrics':
                    return this.financialCalculations.calculateTodayMetrics();
                    
                case 'periodMetrics':
                    return this.financialCalculations.calculatePeriodMetrics(args[0], args[1]);
                    
                case 'revenue':
                    return this.financialCalculations.calculateRevenue(args[0], args[1]);
                    
                case 'expenses':
                    return this.financialCalculations.calculateExpenses(args[0], args[1]);
                    
                case 'profitLoss':
                    return this.financialCalculations.calculateProfitLoss(args[0], args[1]);
                    
                case 'cashFlow':
                    return this.financialCalculations.calculateCashFlow(args[0]);
                    
                case 'topItems':
                    return this.financialCalculations.calculateTopItems(args[0], args[1], args[2]);
                    
                case 'udharSummary':
                    return this.financialCalculations.calculateUdharSummary();
                    
                case 'inventoryValue':
                    return this.financialCalculations.calculateInventoryValue();
                    
                default:
                    throw new Error(`Unknown financial data type: ${type}`);
            }
            
        } catch (error) {
            console.error(`❌ Failed to get financial data (${type}):`, error);
            return null;
        }
    }

    /**
     * Save data to database
     */
    saveData(table, data) {
        try {
            return this.databaseManager.create(table, data);
        } catch (error) {
            console.error(`❌ Failed to save data to ${table}:`, error);
            throw error;
        }
    }

    /**
     * Get data from database
     */
    getData(table, filters = {}) {
        try {
            return this.databaseManager.read(table, filters);
        } catch (error) {
            console.error(`❌ Failed to get data from ${table}:`, error);
            return [];
        }
    }

    /**
     * Update data in database
     */
    updateData(table, id, updates) {
        try {
            return this.databaseManager.update(table, id, updates);
        } catch (error) {
            console.error(`❌ Failed to update data in ${table}:`, error);
            throw error;
        }
    }

    /**
     * Delete data from database
     */
    deleteData(table, id) {
        try {
            return this.databaseManager.delete(table, id);
        } catch (error) {
            console.error(`❌ Failed to delete data from ${table}:`, error);
            throw error;
        }
    }

    /**
     * Notify frontend that system is ready
     */
    notifySystemReady() {
        try {
            // Dispatch custom event
            const event = new CustomEvent('zaiqaSystemReady', {
                detail: {
                    version: this.version,
                    timestamp: new Date().toISOString()
                }
            });
            
            document.dispatchEvent(event);
            
            // Also call global callback if it exists
            if (typeof window.onZaiqaSystemReady === 'function') {
                window.onZaiqaSystemReady(this);
            }
            
        } catch (error) {
            console.error('❌ Failed to notify system ready:', error);
        }
    }

    /**
     * Get system status and health check
     */
    getSystemStatus() {
        return {
            version: this.version,
            initialized: this.initialized,
            components: {
                financialEngine: this.financialEngine ? this.financialEngine.getStatus() : null,
                databaseManager: this.databaseManager ? this.databaseManager.getStatus() : null,
                financialCalculations: this.financialCalculations ? this.financialCalculations.getStatus() : null
            },
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Check if system is properly initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize the system when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('🚀 Starting Zaiqa Restaurant System...');
        window.zaiqaRestaurantSystem = new ZaiqaRestaurantSystem();
    } catch (error) {
        console.error('❌ Failed to start Zaiqa Restaurant System:', error);
    }
});

// Export for global use
window.ZaiqaRestaurantSystem = ZaiqaRestaurantSystem;
