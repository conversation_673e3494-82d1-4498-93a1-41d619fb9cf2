/**
 * Modern Modal Manager
 * Handles modal creation, display, and management
 */

class ModalManager {
    constructor() {
        this.activeModals = new Map();
        this.modalCounter = 0;
        this.setupEventListeners();
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeTopModal();
            }
        });

        // Handle click outside modal
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target.dataset.modalId);
            }
        });
    }

    /**
     * Create a new modal
     */
    create(options = {}) {
        const modalId = `modal_${++this.modalCounter}`;
        
        const modal = new Modal(modalId, {
            title: options.title || 'Modal',
            content: options.content || '',
            size: options.size || 'medium',
            closable: options.closable !== false,
            backdrop: options.backdrop !== false,
            onShow: options.onShow,
            onHide: options.onHide,
            onDestroy: options.onDestroy
        });

        this.activeModals.set(modalId, modal);
        return modal;
    }

    /**
     * Close specific modal
     */
    closeModal(modalId) {
        const modal = this.activeModals.get(modalId);
        if (modal) {
            modal.hide();
        }
    }

    /**
     * Close top modal (most recent)
     */
    closeTopModal() {
        const modalIds = Array.from(this.activeModals.keys());
        if (modalIds.length > 0) {
            const topModalId = modalIds[modalIds.length - 1];
            this.closeModal(topModalId);
        }
    }

    /**
     * Close all modals
     */
    closeAll() {
        this.activeModals.forEach(modal => modal.hide());
    }

    /**
     * Remove modal from active list
     */
    removeModal(modalId) {
        this.activeModals.delete(modalId);
    }

    /**
     * Get active modal count
     */
    getActiveCount() {
        return this.activeModals.size;
    }
}

/**
 * Individual Modal Class
 */
class Modal {
    constructor(id, options) {
        this.id = id;
        this.options = options;
        this.element = null;
        this.isVisible = false;
        
        this.createElement();
    }

    /**
     * Create modal DOM element
     */
    createElement() {
        const sizeClasses = {
            small: 'max-w-md',
            medium: 'max-w-2xl',
            large: 'max-w-4xl',
            xlarge: 'max-w-6xl',
            full: 'max-w-full mx-4'
        };

        const sizeClass = sizeClasses[this.options.size] || sizeClasses.medium;

        this.element = document.createElement('div');
        this.element.className = 'modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 opacity-0 transition-opacity duration-300';
        this.element.dataset.modalId = this.id;
        
        this.element.innerHTML = `
            <div class="modal-content bg-white rounded-xl shadow-2xl ${sizeClass} w-full max-h-[90vh] overflow-hidden transform scale-95 transition-transform duration-300">
                ${this.options.title ? `
                    <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-900">${this.options.title}</h3>
                        ${this.options.closable ? `
                            <button class="modal-close-btn text-gray-400 hover:text-gray-600 transition-colors" onclick="modernApp.modalManager.closeModal('${this.id}')">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        ` : ''}
                    </div>
                ` : ''}
                
                <div class="modal-body p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                    ${this.options.content}
                </div>
            </div>
        `;

        // Add to container
        const container = document.getElementById('modalContainer') || document.body;
        container.appendChild(this.element);
    }

    /**
     * Show modal
     */
    show() {
        if (this.isVisible) return;

        // Trigger onShow callback
        if (this.options.onShow) {
            this.options.onShow(this);
        }

        // Add body class to prevent scrolling
        document.body.classList.add('modal-open');

        // Show modal with animation
        requestAnimationFrame(() => {
            this.element.classList.remove('opacity-0');
            this.element.classList.add('opacity-100');
            
            const content = this.element.querySelector('.modal-content');
            content.classList.remove('scale-95');
            content.classList.add('scale-100');
        });

        this.isVisible = true;
    }

    /**
     * Hide modal
     */
    hide() {
        if (!this.isVisible) return;

        // Trigger onHide callback
        if (this.options.onHide) {
            this.options.onHide(this);
        }

        // Hide modal with animation
        this.element.classList.remove('opacity-100');
        this.element.classList.add('opacity-0');
        
        const content = this.element.querySelector('.modal-content');
        content.classList.remove('scale-100');
        content.classList.add('scale-95');

        // Remove after animation
        setTimeout(() => {
            this.destroy();
        }, 300);

        this.isVisible = false;
    }

    /**
     * Destroy modal
     */
    destroy() {
        // Trigger onDestroy callback
        if (this.options.onDestroy) {
            this.options.onDestroy(this);
        }

        // Remove from DOM
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }

        // Remove from manager
        if (window.modernApp && window.modernApp.modalManager) {
            window.modernApp.modalManager.removeModal(this.id);
        }

        // Remove body class if no more modals
        if (window.modernApp && window.modernApp.modalManager && 
            window.modernApp.modalManager.getActiveCount() === 0) {
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Update modal content
     */
    updateContent(newContent) {
        const bodyElement = this.element.querySelector('.modal-body');
        if (bodyElement) {
            bodyElement.innerHTML = newContent;
        }
    }

    /**
     * Update modal title
     */
    updateTitle(newTitle) {
        const titleElement = this.element.querySelector('.modal-header h3');
        if (titleElement) {
            titleElement.textContent = newTitle;
        }
    }

    /**
     * Get modal element
     */
    getElement() {
        return this.element;
    }

    /**
     * Check if modal is visible
     */
    isShown() {
        return this.isVisible;
    }
}

/**
 * Utility functions for common modal types
 */
class ModalUtils {
    /**
     * Show confirmation dialog
     */
    static confirm(message, title = 'Confirm Action') {
        return new Promise((resolve) => {
            const modal = window.modernApp.modalManager.create({
                title: title,
                size: 'small',
                content: `
                    <div class="text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                        </div>
                        <p class="text-gray-900 mb-6">${message}</p>
                        <div class="flex justify-center space-x-3">
                            <button onclick="modernApp.modalManager.closeModal('${modal.id}'); window.modalConfirmResolve(false)" 
                                    class="btn btn-secondary">
                                Cancel
                            </button>
                            <button onclick="modernApp.modalManager.closeModal('${modal.id}'); window.modalConfirmResolve(true)" 
                                    class="btn btn-error">
                                Confirm
                            </button>
                        </div>
                    </div>
                `,
                closable: false
            });

            // Store resolve function globally (temporary)
            window.modalConfirmResolve = resolve;

            modal.show();
        });
    }

    /**
     * Show alert dialog
     */
    static alert(message, title = 'Alert', type = 'info') {
        const iconClasses = {
            info: 'fas fa-info-circle text-blue-600',
            success: 'fas fa-check-circle text-green-600',
            warning: 'fas fa-exclamation-triangle text-yellow-600',
            error: 'fas fa-times-circle text-red-600'
        };

        const bgClasses = {
            info: 'bg-blue-100',
            success: 'bg-green-100',
            warning: 'bg-yellow-100',
            error: 'bg-red-100'
        };

        return new Promise((resolve) => {
            const modal = window.modernApp.modalManager.create({
                title: title,
                size: 'small',
                content: `
                    <div class="text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full ${bgClasses[type]} mb-4">
                            <i class="${iconClasses[type]} text-xl"></i>
                        </div>
                        <p class="text-gray-900 mb-6">${message}</p>
                        <button onclick="modernApp.modalManager.closeModal('${modal.id}'); window.modalAlertResolve()" 
                                class="btn btn-primary">
                            OK
                        </button>
                    </div>
                `,
                closable: true
            });

            // Store resolve function globally (temporary)
            window.modalAlertResolve = resolve;

            modal.show();
        });
    }

    /**
     * Show loading modal
     */
    static loading(message = 'Loading...') {
        const modal = window.modernApp.modalManager.create({
            title: '',
            size: 'small',
            content: `
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-12 w-12 border-4 border-orange-500 border-t-transparent mx-auto mb-4"></div>
                    <p class="text-gray-600">${message}</p>
                </div>
            `,
            closable: false,
            backdrop: false
        });

        modal.show();
        return modal;
    }
}

// Add CSS for modal-open class
const modalStyles = document.createElement('style');
modalStyles.textContent = `
    .modal-open {
        overflow: hidden;
    }
    
    .modal-overlay {
        backdrop-filter: blur(4px);
    }
    
    .modal-content {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
    
    @media (max-width: 640px) {
        .modal-content {
            margin: 1rem;
            max-height: calc(100vh - 2rem);
        }
    }
`;
document.head.appendChild(modalStyles);

// Export for global use
window.ModalManager = ModalManager;
window.Modal = Modal;
window.ModalUtils = ModalUtils;
