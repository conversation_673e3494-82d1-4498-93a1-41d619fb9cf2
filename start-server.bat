@echo off
echo ========================================
echo    Zaiqa Restaurant Management System
echo ========================================
echo.
echo Starting XAMPP Apache Server...
echo.

REM Check if XAMPP is installed
if not exist "C:\xampp\apache\bin\httpd.exe" (
    echo ERROR: XAMPP not found at C:\xampp\
    echo Please install XAMPP first from: https://www.apachefriends.org/
    echo.
    pause
    exit /b 1
)

REM Start Apache
echo Starting Apache server...
"C:\xampp\apache\bin\httpd.exe" -k start

REM Wait a moment for server to start
timeout /t 3 /nobreak >nul

REM Open the application in browser
echo Opening Zaiqa Restaurant Management System...
start http://localhost/zaiqa

echo.
echo ========================================
echo Server started successfully!
echo.
echo Application URL: http://localhost/zaiqa
echo.
echo Demo Login Credentials:
echo - Admin:   admin / admin123
echo - Waiter:  waiter / waiter123
echo - Kitchen: kitchen / kitchen123
echo.
echo To stop the server, close this window or
echo use XAMPP Control Panel
echo ========================================
echo.
pause
