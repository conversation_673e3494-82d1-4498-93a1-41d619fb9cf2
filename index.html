<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zaiqa Al-Hayat Restaurant Management System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/sales-insights.css">
    <link rel="stylesheet" href="assets/css/cash-in-hand.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Main Application -->
    <div id="mainApp" class="main-app">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-utensils"></i>
                    <span>Zaiqa Al-Hayat</span>
                </div>
                <button id="sidebarToggle" class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="user-details">
                    <span class="user-name">Zaiqa Al-Hayat</span>
                    <span class="user-role">Management System</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#" data-page="dashboard" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a></li>
                    <li><a href="#" data-page="tables" class="nav-link">
                        <i class="fas fa-table"></i>
                        <span>Tables</span>
                    </a></li>

                    <li><a href="#" data-page="menu" class="nav-link">
                        <i class="fas fa-utensils"></i>
                        <span>Menu</span>
                    </a></li>
                    <li><a href="#" data-page="inventory" class="nav-link">
                        <i class="fas fa-boxes"></i>
                        <span>Inventory</span>
                    </a></li>
                    <li><a href="#" data-page="equipment" class="nav-link">
                        <i class="fas fa-tools"></i>
                        <span>Equipment</span>
                    </a></li>
                    <li><a href="#" data-page="billing" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <span>Billing</span>
                    </a></li>
                    <li><a href="#" data-page="udhars" class="nav-link">
                        <i class="fas fa-user-clock"></i>
                        <span>Udhars</span>
                    </a></li>
                    <li><a href="#" data-page="khata" class="nav-link">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>Khata</span>
                    </a></li>
                    <li><a href="#" data-page="staff" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Staff</span>
                    </a></li>
                    <li><a href="#" data-page="expenses" class="nav-link">
                        <i class="fas fa-receipt"></i>
                        <span>Expenses</span>
                    </a></li>
                    <li><a href="#" data-page="reports" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </a></li>
                    <li><a href="#" data-page="settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="restaurant-info">
                    <p class="restaurant-name">Zaiqa Al-Hayat</p>
                    <p class="restaurant-tagline">Management System</p>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h2 id="pageTitle">Dashboard</h2>
                    <p id="pageSubtitle">Welcome to your restaurant management system</p>
                </div>
                
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <i class="fas fa-wifi text-success"></i>
                            <span>Online</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <span id="currentTime"></span>
                        </div>
                        <div class="stat-item">
                            <button id="themeToggle" class="theme-toggle" onclick="toggleThemeSimple()" title="Toggle Dark Mode">
                                <i class="fas fa-moon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Dashboard Page -->
                <div id="dashboardPage" class="page active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="todayRevenue">PKR 0</h3>
                                <p>Today's Revenue</p>
                                <span class="stat-change neutral">Real-time</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="todayOrders">0</h3>
                                <p>Orders Today</p>
                                <span class="stat-change neutral" id="activeOrders">0 active</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-table"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="tableStatus">20/20</h3>
                                <p>Available Tables</p>
                                <span class="stat-change positive">All available</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-trending-up"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="avgOrderValue">PKR 0</h3>
                                <p>Avg Order Value</p>
                                <span class="stat-change neutral">Calculated</span>
                            </div>
                        </div>

                        <div class="stat-card cash-balance-card">
                            <div class="stat-icon">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="cashBalance">PKR 0</h3>
                                <p>Cash in Hand</p>
                                <span class="stat-change neutral">Revenue - Expenses</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent Orders</h3>
                                <button class="btn btn-sm" onclick="app.navigateToPage('billing')">View All</button>
                            </div>
                            <div class="card-content">
                                <div id="recentOrdersList" class="order-list">
                                    <!-- Real orders will be loaded here -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3><i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Alert</h3>
                                <button class="btn btn-sm" data-page="inventory">Manage Inventory</button>
                            </div>
                            <div class="card-content">
                                <div id="lowStockAlerts" class="inventory-alerts">
                                    <!-- Low stock alerts will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-grid-extended">
                        <!-- To-Do List Card -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3><i class="fas fa-tasks"></i> Daily Tasks</h3>
                                <button class="btn btn-sm" onclick="app.showAddTaskModal()">Add Task</button>
                            </div>
                            <div class="card-content">
                                <div id="todoList" class="todo-list">
                                    <!-- Tasks will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Shopping List Card -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3><i class="fas fa-shopping-list"></i> Purchase List</h3>
                                <button class="btn btn-sm" onclick="app.showAddPurchaseModal()">Add Item</button>
                            </div>
                            <div class="card-content">
                                <div id="purchaseList" class="purchase-list">
                                    <!-- Purchase items will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <h3>Quick Actions</h3>
                        <div class="actions-grid">
                            <button class="action-btn" data-page="orders">
                                <i class="fas fa-plus"></i>
                                <span>New Order</span>
                            </button>
                            <button class="action-btn" data-page="tables">
                                <i class="fas fa-table"></i>
                                <span>View Tables</span>
                            </button>
                            <button class="action-btn" onclick="app.showPOSSystem()">
                                <i class="fas fa-cash-register"></i>
                                <span>Open POS</span>
                            </button>
                            <button class="action-btn" onclick="app.showCalculator()">
                                <i class="fas fa-calculator"></i>
                                <span>Calculator</span>
                            </button>
                            <button class="action-btn" onclick="app.showMorningOpeningModal()">
                                <i class="fas fa-sun"></i>
                                <span>Morning Balance</span>
                            </button>
                            <button class="action-btn" onclick="app.showEveningClosingModal()">
                                <i class="fas fa-moon"></i>
                                <span>Evening Balance</span>
                            </button>
                            <button class="action-btn" data-page="billing">
                                <i class="fas fa-receipt"></i>
                                <span>Generate Bill</span>
                            </button>
                            <button class="action-btn" data-page="inventory">
                                <i class="fas fa-boxes"></i>
                                <span>Check Inventory</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Other pages will be loaded here -->
                <div id="tablesPage" class="page"></div>

                <div id="menuPage" class="page"></div>
                <div id="inventoryPage" class="page"></div>
                <div id="equipmentPage" class="page"></div>
                <div id="billingPage" class="page"></div>
                <div id="udharsPage" class="page"></div>
                <div id="khataPage" class="page"></div>
                <div id="staffPage" class="page"></div>
                <div id="expensesPage" class="page"></div>
                <div id="reportsPage" class="page"></div>
                <div id="settingsPage" class="page"></div>
            </div>
        </main>
    </div>

    <!-- Floating Action Buttons -->
    <div class="floating-actions">
        <button class="fab fab-calculator" onclick="safeAppCall('showCalculator')" title="Calculator">
            <i class="fas fa-calculator"></i>
        </button>
        <button class="fab fab-pos" onclick="safeAppCall('showPOSSystem')" title="POS System">
            <i class="fas fa-cash-register"></i>
        </button>
    </div>

    <!-- Scripts -->
    <script src="assets/js/app.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/ai-assistant.js"></script>
</body>
</html>
