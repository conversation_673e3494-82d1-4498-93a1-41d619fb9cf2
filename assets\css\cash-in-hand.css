/* Cash in Hand Management Styles */

.cash-management-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Cash Balance Overview */
.cash-balance-overview {
    display: grid;
    grid-template-columns: 2fr 3fr;
    gap: 20px;
    margin-bottom: 30px;
}

.balance-card {
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.main-balance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    flex-direction: column;
    text-align: center;
}

.balance-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    margin-bottom: 20px;
}

.balance-content h2 {
    font-size: 48px;
    font-weight: 700;
    margin: 0 0 8px 0;
}

.balance-content p {
    font-size: 18px;
    margin: 0 0 8px 0;
    opacity: 0.9;
}

.balance-content small {
    font-size: 14px;
    opacity: 0.8;
}

.balance-content small.healthy { color: #10b981; }
.balance-content small.warning { color: #f59e0b; }
.balance-content small.critical { color: #ef4444; }
.balance-content small.not-set { color: #6b7280; }

.balance-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.balance-breakdown {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.breakdown-item {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.breakdown-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin: 0 auto 16px auto;
    color: white;
}

.inflow .breakdown-icon { background: #10b981; }
.outflow .breakdown-icon { background: #ef4444; }
.net-flow .breakdown-icon { background: #3b82f6; }

.breakdown-content h3 {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #111827;
}

.breakdown-content h3.positive { color: #10b981; }
.breakdown-content h3.negative { color: #ef4444; }

.breakdown-content p {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 4px 0;
    font-weight: 500;
}

.breakdown-content small {
    font-size: 12px;
    color: #9ca3af;
}

/* Cash Flow Tabs */
.cash-flow-tabs {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-navigation {
    display: flex;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    flex: 1;
    padding: 16px 20px;
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.tab-btn.active {
    background: white;
    color: #111827;
    border-bottom: 3px solid #3b82f6;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* Cash Flow Section */
.cash-flow-section {
    max-width: 100%;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.section-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-actions select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

/* Inflow Categories */
.inflow-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.category-card {
    background: #f9fafb;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #3b82f6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin: 0 auto 16px auto;
}

.category-content h4 {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
}

.category-content p {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 4px 0;
    font-weight: 500;
}

.category-content small {
    font-size: 12px;
    color: #9ca3af;
}

/* Transactions Table */
.transactions-table {
    margin-top: 30px;
}

.transactions-table h4 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
}

.table-responsive {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.data-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    color: #111827;
    font-size: 14px;
}

.data-table tr:hover {
    background: #f9fafb;
}

.source-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.source-badge.order {
    background: #dbeafe;
    color: #1e40af;
}

.source-badge.udhar {
    background: #d1fae5;
    color: #065f46;
}

.source-badge.other {
    background: #fef3c7;
    color: #92400e;
}

.amount-cell {
    text-align: right;
    font-weight: 600;
}

.amount-cell.positive {
    color: #10b981;
}

.amount-cell.negative {
    color: #ef4444;
}

.amount {
    font-family: 'Courier New', monospace;
}

.no-data {
    text-align: center;
    padding: 40px 20px;
}

.empty-state {
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 16px;
    margin: 0 0 8px 0;
}

.empty-state small {
    font-size: 14px;
    opacity: 0.7;
}

/* Reconciliation Modal */
.reconciliation-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.reconciliation-section h4 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
}

.calculated-balance {
    background: #f9fafb;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.balance-amount {
    font-size: 32px;
    font-weight: 700;
    color: #111827;
    text-align: center;
    margin-bottom: 20px;
}

.balance-breakdown {
    space-y: 8px;
}

.breakdown-line {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    font-size: 14px;
}

.breakdown-line.total {
    font-weight: 600;
    font-size: 16px;
    color: #111827;
}

.difference-display {
    margin: 20px 0;
    padding: 16px;
    border-radius: 8px;
    text-align: center;
}

.difference-amount {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.difference-amount.balanced {
    color: #10b981;
    background: #d1fae5;
}

.difference-amount.surplus {
    color: #059669;
    background: #ecfdf5;
}

.difference-amount.shortage {
    color: #dc2626;
    background: #fef2f2;
}

.difference-text {
    font-size: 14px;
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .cash-balance-overview {
        grid-template-columns: 1fr;
    }
    
    .balance-breakdown {
        grid-template-columns: 1fr;
    }
    
    .reconciliation-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .cash-management-container {
        padding: 15px;
    }
    
    .tab-navigation {
        flex-direction: column;
    }
    
    .tab-btn {
        text-align: left;
        justify-content: flex-start;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .inflow-categories {
        grid-template-columns: 1fr;
    }
    
    .balance-card {
        padding: 20px;
    }
    
    .balance-content h2 {
        font-size: 36px;
    }
}

/* Transaction Details Modal */
.transaction-details {
    padding: 20px 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #374151;
    flex: 1;
}

.detail-value {
    color: #111827;
    text-align: right;
    flex: 1;
}

/* Chart Placeholder */
.chart-placeholder {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
    background: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
}

.chart-placeholder i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.chart-placeholder p {
    font-size: 18px;
    margin: 0 0 8px 0;
    font-weight: 500;
}

.chart-placeholder small {
    font-size: 14px;
    opacity: 0.7;
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.analytics-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.analytics-card h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
}

/* Daily Summary */
.daily-summary {
    space-y: 12px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.summary-item.total {
    border-top: 1px solid #e5e7eb;
    margin-top: 12px;
    padding-top: 12px;
    font-weight: 600;
}

.summary-label {
    color: #6b7280;
    font-size: 14px;
}

.summary-value {
    font-weight: 600;
    color: #111827;
}

.summary-value.positive {
    color: #10b981;
}

.summary-value.negative {
    color: #ef4444;
}

/* Health Indicators */
.health-indicators {
    padding: 16px;
    border-radius: 8px;
}

.indicator {
    display: flex;
    align-items: center;
    gap: 12px;
}

.indicator.healthy {
    background: #d1fae5;
    color: #065f46;
}

.indicator.warning {
    background: #fef3c7;
    color: #92400e;
}

.indicator.critical {
    background: #fef2f2;
    color: #991b1b;
}

.indicator.not-set {
    background: #f3f4f6;
    color: #6b7280;
}

.indicator-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: rgba(255, 255, 255, 0.3);
}

.indicator-content h5 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
}

.indicator-content p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
}

@media (max-width: 1024px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }
}
