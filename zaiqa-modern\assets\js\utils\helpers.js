/**
 * Helper Utilities for Modern Zaiqa Restaurant System
 */

class Helpers {
    /**
     * Format currency
     */
    static formatCurrency(amount, currency = 'PKR') {
        const num = parseFloat(amount) || 0;
        return `${currency} ${num.toLocaleString()}`;
    }

    /**
     * Format date
     */
    static formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        
        return new Date(date).toLocaleDateString('en-US', { ...defaultOptions, ...options });
    }

    /**
     * Format time
     */
    static formatTime(date, options = {}) {
        const defaultOptions = {
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return new Date(date).toLocaleTimeString('en-US', { ...defaultOptions, ...options });
    }

    /**
     * Format date and time
     */
    static formatDateTime(date) {
        return `${this.formatDate(date)} ${this.formatTime(date)}`;
    }

    /**
     * Generate unique ID
     */
    static generateId(prefix = '') {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${prefix}${timestamp}_${random}`;
    }

    /**
     * Debounce function
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function
     */
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Deep clone object
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    /**
     * Validate email
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate phone number
     */
    static isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }

    /**
     * Sanitize string
     */
    static sanitizeString(str) {
        return str.replace(/[<>]/g, '').trim();
    }

    /**
     * Calculate percentage
     */
    static calculatePercentage(value, total) {
        if (total === 0) return 0;
        return Math.round((value / total) * 100 * 100) / 100;
    }

    /**
     * Get relative time
     */
    static getRelativeTime(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
        
        return this.formatDate(date);
    }

    /**
     * Generate sample data for testing
     */
    static generateSampleData() {
        return {
            menuItems: [
                {
                    id: this.generateId('menu_'),
                    name: 'Chicken Karahi',
                    category: 'main_course',
                    price: 800,
                    description: 'Traditional Pakistani chicken curry',
                    isAvailable: true,
                    ingredients: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: this.generateId('menu_'),
                    name: 'Mutton Biryani',
                    category: 'main_course',
                    price: 1200,
                    description: 'Aromatic basmati rice with tender mutton',
                    isAvailable: true,
                    ingredients: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: this.generateId('menu_'),
                    name: 'Chicken Tikka',
                    category: 'appetizers',
                    price: 600,
                    description: 'Grilled chicken pieces with spices',
                    isAvailable: true,
                    ingredients: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: this.generateId('menu_'),
                    name: 'Fresh Lime Juice',
                    category: 'beverages',
                    price: 150,
                    description: 'Freshly squeezed lime juice',
                    isAvailable: true,
                    ingredients: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: this.generateId('menu_'),
                    name: 'Naan',
                    category: 'bread',
                    price: 80,
                    description: 'Traditional Pakistani bread',
                    isAvailable: true,
                    ingredients: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: this.generateId('menu_'),
                    name: 'Gulab Jamun',
                    category: 'desserts',
                    price: 200,
                    description: 'Sweet milk dumplings in syrup',
                    isAvailable: true,
                    ingredients: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ],
            
            orders: [
                {
                    id: this.generateId('order_'),
                    orderNumber: 'ORD20241210001',
                    customerName: 'Ahmed Khan',
                    customerCount: 4,
                    serviceType: 'dine_in',
                    paymentMethod: 'cash',
                    items: [
                        { id: 'menu_1', name: 'Chicken Karahi', price: 800, quantity: 2 },
                        { id: 'menu_2', name: 'Naan', price: 80, quantity: 8 },
                        { id: 'menu_3', name: 'Fresh Lime Juice', price: 150, quantity: 4 }
                    ],
                    totalAmount: 2840,
                    perHeadCharges: 100,
                    additionalCharges: 0,
                    discount: 0,
                    status: 'completed',
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
                },
                {
                    id: this.generateId('order_'),
                    orderNumber: 'ORD20241210002',
                    customerName: 'Fatima Ali',
                    customerCount: 2,
                    serviceType: 'takeaway',
                    paymentMethod: 'card',
                    items: [
                        { id: 'menu_2', name: 'Mutton Biryani', price: 1200, quantity: 1 },
                        { id: 'menu_3', name: 'Chicken Tikka', price: 600, quantity: 1 }
                    ],
                    totalAmount: 1800,
                    perHeadCharges: 0,
                    additionalCharges: 0,
                    discount: 0,
                    status: 'completed',
                    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
                }
            ],
            
            expenses: [
                {
                    id: this.generateId('expense_'),
                    description: 'Grocery Shopping',
                    amount: 5000,
                    category: 'food_supplies',
                    date: new Date().toISOString().split('T')[0],
                    paymentMethod: 'cash',
                    supplier: 'Metro Cash & Carry',
                    notes: 'Monthly grocery supplies',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: this.generateId('expense_'),
                    description: 'Electricity Bill',
                    amount: 8000,
                    category: 'utilities',
                    date: new Date().toISOString().split('T')[0],
                    paymentMethod: 'online',
                    supplier: 'LESCO',
                    notes: 'Monthly electricity bill',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ],
            
            cashRegister: [
                {
                    id: this.generateId('cash_'),
                    date: new Date().toISOString().split('T')[0],
                    openingBalance: 10000,
                    closingBalance: 15000,
                    totalSales: 0,
                    totalExpenses: 0,
                    notes: 'Daily cash register',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ]
        };
    }

    /**
     * Initialize sample data if storage is empty
     */
    static async initializeSampleData(dataManager) {
        try {
            // Check if data already exists
            const orders = dataManager.get('orders');
            const menuItems = dataManager.get('menuItems');
            
            if (orders.length === 0 && menuItems.length === 0) {
                console.log('🎯 Initializing sample data...');
                
                const sampleData = this.generateSampleData();
                
                // Add sample data
                for (const menuItem of sampleData.menuItems) {
                    await dataManager.add('menuItems', menuItem);
                }
                
                for (const order of sampleData.orders) {
                    await dataManager.add('orders', order);
                }
                
                for (const expense of sampleData.expenses) {
                    await dataManager.add('expenses', expense);
                }
                
                for (const cashEntry of sampleData.cashRegister) {
                    await dataManager.add('cashRegister', cashEntry);
                }
                
                console.log('✅ Sample data initialized successfully');
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('❌ Sample data initialization failed:', error);
            return false;
        }
    }

    /**
     * Export data as CSV
     */
    static exportToCSV(data, filename) {
        if (!data || data.length === 0) return;
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') 
                        ? `"${value}"` 
                        : value;
                }).join(',')
            )
        ].join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * Print element
     */
    static printElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Print</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .no-print { display: none; }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
    }
}

// Export for global use
window.Helpers = Helpers;
