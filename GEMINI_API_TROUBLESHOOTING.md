# 🔧 Gemini API Troubleshooting Guide

## Your API Key: `AIzaSyCFryKebYzTAoABS2dF47HONaiovZM8CSU`

## Quick Diagnosis Steps

### 1. **Test Your API Key Immediately**
Open the test file I created: `gemini-test.html` in your browser
- It will auto-test your API key
- Shows detailed logs of what's happening
- Provides specific error guidance

### 2. **Common Issues & Solutions**

#### **Issue: API Key Invalid (400 Error)**
**Symptoms**: "API key not valid" or 400 status code
**Solutions**:
1. **Verify Key Format**: Should start with `<PERSON>za` (✅ yours does)
2. **Check for Extra Spaces**: Copy key again without trailing spaces
3. **Regenerate Key**: Create new key at [ai.google.dev](https://ai.google.dev)

#### **Issue: API Not Enabled (403 Error)**
**Symptoms**: "Generative Language API has not been used" or 403 status
**Solutions**:
1. **Enable API**: Go to [Google Cloud Console](https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com)
2. **Select Project**: Make sure you're in the right Google Cloud project
3. **Click Enable**: Enable the "Generative Language API"

#### **Issue: Billing Required (403 Error)**
**Symptoms**: "Billing account required" or quota exceeded
**Solutions**:
1. **Set Up Billing**: Add billing account in Google Cloud Console
2. **Check Quotas**: View usage at [Google Cloud Quotas](https://console.cloud.google.com/iam-admin/quotas)
3. **Free Tier**: Gemini has free tier limits (60 requests/minute)

#### **Issue: Rate Limiting (429 Error)**
**Symptoms**: "Resource exhausted" or 429 status
**Solutions**:
1. **Wait**: Rate limits reset after 1 minute
2. **Reduce Frequency**: Space out your requests
3. **Upgrade**: Consider paid tier for higher limits

### 3. **Manual Testing Commands**

Open browser console (F12) and run:

```javascript
// Test 1: Basic API connectivity
fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyCFryKebYzTAoABS2dF47HONaiovZM8CSU', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        contents: [{ parts: [{ text: "Hello" }] }],
        generationConfig: { maxOutputTokens: 10 }
    })
}).then(r => r.json()).then(console.log).catch(console.error);

// Test 2: Using the AI Assistant's test function
zaiqaAI.testGeminiManual('AIzaSyCFryKebYzTAoABS2dF47HONaiovZM8CSU');
```

### 4. **Step-by-Step Setup Verification**

#### **Step 1: Verify Google Cloud Project**
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Check which project is selected (top dropdown)
3. Ensure it's the same project where you created the API key

#### **Step 2: Enable Required APIs**
1. Go to [APIs & Services](https://console.cloud.google.com/apis/dashboard)
2. Click "Enable APIs and Services"
3. Search for "Generative Language API"
4. Click and enable it

#### **Step 3: Check API Key Restrictions**
1. Go to [Credentials](https://console.cloud.google.com/apis/credentials)
2. Find your API key
3. Click "Edit"
4. Check "API restrictions" - should include "Generative Language API"
5. Check "Website restrictions" - should allow your domain or be unrestricted

#### **Step 4: Verify Billing**
1. Go to [Billing](https://console.cloud.google.com/billing)
2. Ensure billing account is linked to your project
3. Check if you have available credits or payment method

### 5. **Expected API Response Format**

**Successful Response**:
```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "Hello! How can I help you today?"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ]
}
```

**Error Response**:
```json
{
  "error": {
    "code": 403,
    "message": "Generative Language API has not been used in project...",
    "status": "PERMISSION_DENIED"
  }
}
```

### 6. **Testing URLs**

- **API Endpoint**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent`
- **Test Page**: Open `gemini-test.html` in your browser
- **Google AI Studio**: [aistudio.google.com](https://aistudio.google.com) (test API key here)

### 7. **Alternative Testing Method**

Use Google AI Studio to verify your key:
1. Go to [Google AI Studio](https://aistudio.google.com)
2. Sign in with the same Google account
3. Try creating a prompt
4. If it works there, the issue is in our implementation

### 8. **Debug Information to Collect**

If the issue persists, collect this information:

1. **Browser Console Logs**: Open F12 → Console tab
2. **Network Tab**: Check the actual HTTP request/response
3. **Error Messages**: Exact error text from API
4. **Google Cloud Project ID**: From Cloud Console
5. **API Key Permissions**: Screenshot of key restrictions

### 9. **Contact Information**

If you need help:
1. **Google AI Support**: [ai.google.dev/support](https://ai.google.dev/support)
2. **Stack Overflow**: Tag questions with `google-gemini-api`
3. **Google Cloud Support**: If you have a paid plan

## Quick Fix Commands

Run these in browser console for immediate testing:

```javascript
// Quick test
fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyCFryKebYzTAoABS2dF47HONaiovZM8CSU', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({contents: [{parts: [{text: "Hi"}]}]})
}).then(r => {
  console.log('Status:', r.status);
  return r.text();
}).then(text => {
  console.log('Response:', text);
  try {
    const json = JSON.parse(text);
    if (json.candidates) {
      console.log('✅ SUCCESS:', json.candidates[0].content.parts[0].text);
    } else {
      console.log('❌ ERROR:', json);
    }
  } catch(e) {
    console.log('❌ PARSE ERROR:', text);
  }
});
```

---

**Next Steps**: 
1. Open `gemini-test.html` to see detailed test results
2. Check Google Cloud Console for API enablement
3. Verify billing setup if needed
4. Run manual tests in browser console
