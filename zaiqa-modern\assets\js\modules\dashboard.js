/**
 * Dashboard Module - Modern Analytics Dashboard
 * Provides comprehensive overview of restaurant operations
 */

console.log('📊 Loading DashboardModule class...');

class DashboardModule {
    constructor(app) {
        this.app = app;
        this.charts = {};
        this.refreshInterval = null;
    }

    /**
     * Initialize dashboard module
     */
    async init() {
        console.log('📊 Dashboard module initialized');
    }

    /**
     * Render dashboard content
     */
    async render() {
        const today = this.app.financialEngine.getCurrentBusinessDate();
        const todayRange = this.app.financialEngine.getBusinessDateRange(today);
        
        // Get today's analytics
        const analytics = this.app.financialEngine.calculateAnalytics(todayRange);
        const cashFlow = this.app.financialEngine.calculateCashFlow(today);
        
        return `
            <div class="dashboard-container">
                <!-- Page Header -->
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
                        <p class="text-gray-600 mt-1">Welcome back! Here's what's happening today.</p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="modernApp.openPOS()" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus mr-2"></i>New Order
                        </button>
                        <button onclick="modernApp.modules.dashboard.openCashRegister()" class="btn btn-secondary">
                            <i class="fas fa-cash-register mr-2"></i>Cash Register
                        </button>
                    </div>
                </div>

                <!-- Key Metrics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    ${this.generateMetricCard('Today\'s Revenue', `PKR ${analytics.totalRevenue.toLocaleString()}`, 'fas fa-dollar-sign', 'bg-gradient-to-r from-green-500 to-green-600', analytics.totalRevenue >= 0 ? '+' : '', 'vs yesterday')}
                    
                    ${this.generateMetricCard('Total Orders', analytics.totalOrders.toString(), 'fas fa-shopping-cart', 'bg-gradient-to-r from-blue-500 to-blue-600', '', 'orders today')}
                    
                    ${this.generateMetricCard('Cash in Hand', `PKR ${cashFlow.closingBalance.toLocaleString()}`, 'fas fa-wallet', 'bg-gradient-to-r from-purple-500 to-purple-600', '', 'current balance')}
                    
                    ${this.generateMetricCard('Net Profit', `PKR ${analytics.grossProfit.toLocaleString()}`, 'fas fa-chart-line', analytics.grossProfit >= 0 ? 'bg-gradient-to-r from-green-500 to-green-600' : 'bg-gradient-to-r from-red-500 to-red-600', analytics.grossProfit >= 0 ? '+' : '', `${analytics.profitMargin.toFixed(1)}% margin`)}
                </div>

                <!-- Charts and Analytics -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Revenue Chart -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold">Revenue Trend (7 Days)</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Top Items -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold">Top Selling Items</h3>
                        </div>
                        <div class="card-body">
                            ${this.generateTopItemsList(analytics.topItems)}
                        </div>
                    </div>
                </div>

                <!-- Recent Activity and Quick Stats -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Recent Orders -->
                    <div class="lg:col-span-2">
                        <div class="card">
                            <div class="card-header">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold">Recent Orders</h3>
                                    <button onclick="modernApp.navigateTo('orders')" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
                                        View All <i class="fas fa-arrow-right ml-1"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                ${this.generateRecentOrdersList()}
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold">Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="space-y-3">
                                <button onclick="modernApp.openPOS()" class="w-full btn btn-primary">
                                    <i class="fas fa-plus mr-2"></i>New Order
                                </button>
                                <button onclick="modernApp.navigateTo('inventory')" class="w-full btn btn-secondary">
                                    <i class="fas fa-boxes mr-2"></i>Check Inventory
                                </button>
                                <button onclick="modernApp.modules.dashboard.addExpense()" class="w-full btn btn-secondary">
                                    <i class="fas fa-receipt mr-2"></i>Add Expense
                                </button>
                                <button onclick="modernApp.navigateTo('reports')" class="w-full btn btn-secondary">
                                    <i class="fas fa-chart-bar mr-2"></i>View Reports
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts and Notifications -->
                ${this.generateAlertsSection()}
            </div>
        `;
    }

    /**
     * Generate metric card HTML
     */
    generateMetricCard(title, value, icon, bgClass, prefix = '', subtitle = '') {
        return `
            <div class="card hover:shadow-xl transition-all duration-300">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 ${bgClass} rounded-lg flex items-center justify-center text-white">
                                <i class="${icon} text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-sm font-medium text-gray-600">${title}</p>
                            <p class="text-2xl font-bold text-gray-900">${prefix}${value}</p>
                            ${subtitle ? `<p class="text-xs text-gray-500 mt-1">${subtitle}</p>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate top items list
     */
    generateTopItemsList(topItems) {
        if (!topItems || topItems.length === 0) {
            return '<p class="text-gray-500 text-center py-4">No sales data available</p>';
        }

        return `
            <div class="space-y-3">
                ${topItems.slice(0, 5).map((item, index) => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                                ${index + 1}
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">${item.name}</p>
                                <p class="text-sm text-gray-500">${item.quantity} sold</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">PKR ${item.revenue.toLocaleString()}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate recent orders list
     */
    generateRecentOrdersList() {
        const orders = this.app.dataManager.get('orders')
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (orders.length === 0) {
            return '<p class="text-gray-500 text-center py-4">No recent orders</p>';
        }

        return `
            <div class="space-y-3">
                ${orders.map(order => `
                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">${order.orderNumber}</p>
                                <p class="text-sm text-gray-500">${order.customerName}</p>
                                <p class="text-xs text-gray-400">${new Date(order.createdAt).toLocaleTimeString()}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">PKR ${order.totalAmount.toLocaleString()}</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ${order.status}
                            </span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate alerts section
     */
    generateAlertsSection() {
        const inventory = this.app.dataManager.get('inventory');
        const lowStockItems = inventory.filter(item => item.quantity <= item.lowStockThreshold);

        if (lowStockItems.length === 0) {
            return '';
        }

        return `
            <div class="mt-8">
                <div class="card border-l-4 border-l-yellow-500">
                    <div class="card-header bg-yellow-50">
                        <h3 class="text-lg font-semibold text-yellow-800">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Low Stock Alert
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-yellow-700 mb-3">${lowStockItems.length} items are running low on stock:</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            ${lowStockItems.slice(0, 6).map(item => `
                                <div class="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                                    <p class="font-medium text-yellow-900">${item.name}</p>
                                    <p class="text-sm text-yellow-700">${item.quantity} ${item.unit} remaining</p>
                                </div>
                            `).join('')}
                        </div>
                        ${lowStockItems.length > 6 ? `
                            <button onclick="modernApp.navigateTo('inventory')" class="mt-3 text-yellow-700 hover:text-yellow-800 text-sm font-medium">
                                View all ${lowStockItems.length} items <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Called when page loads
     */
    async onPageLoad() {
        // Initialize charts
        await this.initializeCharts();
        
        // Start auto-refresh
        this.startAutoRefresh();
    }

    /**
     * Initialize charts
     */
    async initializeCharts() {
        try {
            // Revenue chart
            const revenueCtx = document.getElementById('revenueChart');
            if (revenueCtx) {
                this.charts.revenue = new Chart(revenueCtx, {
                    type: 'line',
                    data: await this.getRevenueChartData(),
                    options: this.getChartOptions('Revenue (PKR)')
                });
            }
        } catch (error) {
            console.error('❌ Chart initialization failed:', error);
        }
    }

    /**
     * Get revenue chart data
     */
    async getRevenueChartData() {
        const last7Days = [];
        const today = new Date();
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            
            const dateRange = this.app.financialEngine.getBusinessDateRange(dateStr);
            const revenue = this.app.financialEngine.calculateRevenue(dateRange);
            
            last7Days.push({
                date: dateStr,
                revenue: revenue.totalRevenue
            });
        }
        
        return {
            labels: last7Days.map(day => new Date(day.date).toLocaleDateString()),
            datasets: [{
                label: 'Revenue',
                data: last7Days.map(day => day.revenue),
                borderColor: 'rgb(249, 115, 22)',
                backgroundColor: 'rgba(249, 115, 22, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };
    }

    /**
     * Get chart options
     */
    getChartOptions(title) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        };
    }

    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        // Refresh every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.refreshCharts();
        }, 30000);
    }

    /**
     * Refresh charts
     */
    async refreshCharts() {
        try {
            if (this.charts.revenue) {
                this.charts.revenue.data = await this.getRevenueChartData();
                this.charts.revenue.update();
            }
        } catch (error) {
            console.error('❌ Chart refresh failed:', error);
        }
    }

    /**
     * Open cash register modal
     */
    async openCashRegister() {
        const today = this.app.financialEngine.getCurrentBusinessDate();
        const cashFlow = this.app.financialEngine.calculateCashFlow(today);
        
        const modal = this.app.modalManager.create({
            title: 'Cash Register Management',
            size: 'large',
            content: `
                <div class="cash-register-modal">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="font-semibold">Current Balance</h4>
                            </div>
                            <div class="card-body">
                                <div class="text-3xl font-bold text-green-600 mb-2">
                                    PKR ${cashFlow.closingBalance.toLocaleString()}
                                </div>
                                <p class="text-gray-600">Cash in Hand</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h4 class="font-semibold">Today's Summary</h4>
                            </div>
                            <div class="card-body">
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span>Opening Balance:</span>
                                        <span>PKR ${cashFlow.openingBalance.toLocaleString()}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Revenue:</span>
                                        <span class="text-green-600">+PKR ${cashFlow.revenue.toLocaleString()}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Expenses:</span>
                                        <span class="text-red-600">-PKR ${cashFlow.expenses.toLocaleString()}</span>
                                    </div>
                                    <hr>
                                    <div class="flex justify-between font-semibold">
                                        <span>Expected Closing:</span>
                                        <span>PKR ${cashFlow.expectedClosing.toLocaleString()}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <form id="updateCashForm" onsubmit="modernApp.modules.dashboard.updateCashBalance(event)">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label class="form-label">Opening Balance</label>
                                    <input type="number" name="openingBalance" class="form-control" 
                                           value="${cashFlow.openingBalance}" step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Closing Balance</label>
                                    <input type="number" name="closingBalance" class="form-control" 
                                           value="${cashFlow.closingBalance}" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control" rows="3" placeholder="Any additional notes..."></textarea>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" onclick="modernApp.modalManager.close()" class="btn btn-secondary">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    Update Cash Register
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `
        });
        
        modal.show();
    }

    /**
     * Update cash balance
     */
    async updateCashBalance(event) {
        event.preventDefault();
        
        try {
            const formData = new FormData(event.target);
            const today = this.app.financialEngine.getCurrentBusinessDate();
            
            const cashData = {
                date: today,
                openingBalance: parseFloat(formData.get('openingBalance')),
                closingBalance: parseFloat(formData.get('closingBalance')),
                notes: formData.get('notes'),
                updatedAt: new Date().toISOString()
            };
            
            // Find existing entry or create new one
            const cashRegister = this.app.dataManager.get('cashRegister');
            const existingIndex = cashRegister.findIndex(entry => entry.date === today);
            
            if (existingIndex >= 0) {
                await this.app.dataManager.update('cashRegister', cashRegister[existingIndex].id, cashData);
            } else {
                await this.app.dataManager.add('cashRegister', cashData);
            }
            
            this.app.modalManager.close();
            this.app.showToast('Cash register updated successfully', 'success');
            
            // Refresh dashboard
            await this.app.refreshCurrentPage();
            
        } catch (error) {
            console.error('❌ Cash register update failed:', error);
            this.app.showToast('Failed to update cash register', 'error');
        }
    }

    /**
     * Add expense
     */
    async addExpense() {
        // Navigate to expenses page or open quick add modal
        await this.app.navigateTo('expenses');
    }

    /**
     * Cleanup when leaving page
     */
    onPageLeave() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.destroy) {
                chart.destroy();
            }
        });
        this.charts = {};
    }
}

// Export for global use
console.log('✅ DashboardModule class defined, exporting to window...');
window.DashboardModule = DashboardModule;
console.log('✅ DashboardModule exported successfully');
