/**
 * Modern POS Module - Point of Sale System
 * Handles order creation, menu selection, and payment processing
 */

console.log('🛒 Loading POSModule class...');

class POSModule {
    constructor(app) {
        this.app = app;
        this.currentOrder = {
            items: [],
            customerCount: 1,
            serviceType: 'dine_in',
            paymentMethod: 'cash',
            perHeadCharges: 100, // Default per head charge
            extraCharges: [], // Array of extra charges
            discount: 0,
            udharDetails: {
                customerName: '',
                udharAmount: 0,
                phone: '',
                notes: ''
            },
            printBill: true
        };
        this.selectedCategory = 'all';
        this.searchQuery = '';
        this.keyboardShortcuts = new Map();
        this.quickActions = new Map();
        this.initializeQuickActions();
    }

    /**
     * Initialize POS module
     */
    async init() {
        console.log('🛒 POS module initialized');
        this.setupKeyboardShortcuts();
    }

    /**
     * Initialize quick actions for different item types
     */
    initializeQuickActions() {
        // Bread items
        this.quickActions.set('bread', [
            { label: '+5', quantity: 5 },
            { label: '+10', quantity: 10 },
            { label: '+20', quantity: 20 }
        ]);

        // Beverages
        this.quickActions.set('beverages', [
            { label: '+2', quantity: 2 },
            { label: '+4', quantity: 4 },
            { label: '+6', quantity: 6 }
        ]);

        // Main course
        this.quickActions.set('main_course', [
            { label: '+2', quantity: 2 },
            { label: '+3', quantity: 3 },
            { label: '+5', quantity: 5 }
        ]);

        // Default for other items
        this.quickActions.set('default', [
            { label: '+2', quantity: 2 },
            { label: '+5', quantity: 5 },
            { label: '+10', quantity: 10 }
        ]);
    }

    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        // Default shortcuts for popular items
        this.keyboardShortcuts.set('1', 'Chicken Karahi');
        this.keyboardShortcuts.set('2', 'Mutton Biryani');
        this.keyboardShortcuts.set('3', 'Chicken Tikka');
        this.keyboardShortcuts.set('4', 'Naan');
        this.keyboardShortcuts.set('5', 'Fresh Lime Juice');
    }

    /**
     * Render POS interface
     */
    async render() {
        const menuItems = this.app.dataManager.get('menuItems');
        const categories = this.getUniqueCategories(menuItems);

        return `
            <div class="pos-container">
                <!-- POS Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Point of Sale</h1>
                        <p class="text-gray-600 mt-1">Create new orders and process payments</p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="modernApp.modules.pos.clearOrder()" class="btn btn-secondary">
                            <i class="fas fa-trash mr-2"></i>Clear Order
                        </button>
                        <button onclick="modernApp.modules.pos.openModal()" class="btn btn-primary">
                            <i class="fas fa-expand mr-2"></i>Full Screen POS
                        </button>
                    </div>
                </div>

                <!-- POS Layout -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Menu Items (Left Side) -->
                    <div class="lg:col-span-2">
                        <!-- Search Bar -->
                        <div class="mb-4">
                            <div class="relative">
                                <input type="text"
                                       placeholder="Search menu items..."
                                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                       value="${this.searchQuery}"
                                       onchange="modernApp.modules.pos.updateSearch(this.value)"
                                       oninput="modernApp.modules.pos.updateSearch(this.value)">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Category Filter -->
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2">
                                <button onclick="modernApp.modules.pos.selectCategory('all')"
                                        class="category-btn ${this.selectedCategory === 'all' ? 'active' : ''}"
                                        data-category="all">
                                    All Items
                                </button>
                                ${categories.map(category => `
                                    <button onclick="modernApp.modules.pos.selectCategory('${category}')"
                                            class="category-btn ${this.selectedCategory === category ? 'active' : ''}"
                                            data-category="${category}">
                                        ${category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')}
                                    </button>
                                `).join('')}
                            </div>
                        </div>

                        <!-- Menu Items Grid -->
                        <div class="menu-items-grid">
                            ${this.generateMenuItemsGrid(menuItems)}
                        </div>
                    </div>

                    <!-- Order Summary (Right Side) -->
                    <div class="order-summary-panel">
                        ${this.generateOrderSummary()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate menu items grid
     */
    generateMenuItemsGrid(menuItems) {
        let filteredItems = menuItems;

        // Apply category filter
        if (this.selectedCategory !== 'all') {
            filteredItems = filteredItems.filter(item => item.category === this.selectedCategory);
        }

        // Apply search filter
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filteredItems = filteredItems.filter(item =>
                item.name.toLowerCase().includes(query) ||
                (item.description && item.description.toLowerCase().includes(query))
            );
        }

        if (filteredItems.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3>No items found</h3>
                    <p>${this.searchQuery ? 'No items match your search' : 'No menu items available in this category'}</p>
                </div>
            `;
        }

        return `
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                ${filteredItems.map(item => {
                    const shortcut = this.getShortcutForItem(item.name);
                    return `
                        <div class="menu-item-card ${!item.isAvailable ? 'unavailable' : ''}"
                             onclick="modernApp.modules.pos.addToOrder('${item.id}')">
                            ${shortcut ? `<div class="shortcut-badge">${shortcut}</div>` : ''}
                            <div class="menu-item-image">
                                <i class="fas fa-utensils text-2xl text-gray-400"></i>
                            </div>
                            <div class="menu-item-info">
                                <h4 class="menu-item-name">${item.name}</h4>
                                <p class="menu-item-price">PKR ${item.price.toLocaleString()}</p>
                                ${item.description ? `<p class="menu-item-description">${item.description}</p>` : ''}
                            </div>
                            ${!item.isAvailable ? '<div class="unavailable-overlay">Out of Stock</div>' : ''}
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    /**
     * Generate order summary panel
     */
    generateOrderSummary() {
        const subtotal = this.calculateSubtotal();
        const total = this.calculateTotal();

        return `
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold">Current Order</h3>
                </div>
                <div class="card-body">
                    <!-- Service Type Tabs -->
                    <div class="mb-4">
                        <div class="service-type-tabs">
                            <button onclick="modernApp.modules.pos.updateServiceType('dine_in')"
                                    class="service-tab ${this.currentOrder.serviceType === 'dine_in' ? 'active' : ''}">
                                <i class="fas fa-utensils mr-2"></i>Dine In
                            </button>
                            <button onclick="modernApp.modules.pos.updateServiceType('takeaway')"
                                    class="service-tab ${this.currentOrder.serviceType === 'takeaway' ? 'active' : ''}">
                                <i class="fas fa-shopping-bag mr-2"></i>Take Away
                            </button>
                        </div>
                    </div>

                    <!-- Customer Count (Only for Dine In) -->
                    ${this.currentOrder.serviceType === 'dine_in' ? `
                        <div class="mb-4">
                            <label class="form-label">Customer Count</label>
                            <input type="number" id="customerCount" class="form-control"
                                   value="${this.currentOrder.customerCount}"
                                   onchange="modernApp.modules.pos.updateCustomerCount(this.value)"
                                   min="1" max="20">
                        </div>
                    ` : ''}

                    <!-- Order Items -->
                    <div class="order-items mb-4">
                        <h4 class="font-semibold mb-2">Order Items</h4>
                        ${this.generateOrderItems()}
                    </div>

                    <!-- Per Head Charges (Only for Dine In) -->
                    ${this.currentOrder.serviceType === 'dine_in' ? `
                        <div class="per-head-charges mb-4">
                            <h4 class="font-semibold mb-2">Per Head Charges</h4>
                            <div class="flex justify-between items-center">
                                <label class="text-sm">Rate per person:</label>
                                <input type="number" class="form-control w-24 text-right"
                                       value="${this.currentOrder.perHeadCharges}"
                                       onchange="modernApp.modules.pos.updatePerHeadCharges(this.value)"
                                       min="0" step="0.01">
                            </div>
                        </div>
                    ` : ''}

                    <!-- Extra Charges -->
                    <div class="extra-charges mb-4">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-semibold">Extra Charges</h4>
                            <button onclick="modernApp.modules.pos.addExtraCharge()" class="btn btn-sm btn-secondary">
                                <i class="fas fa-plus mr-1"></i>Add
                            </button>
                        </div>
                        ${this.generateExtraCharges()}
                    </div>

                    <!-- Discount -->
                    <div class="discount mb-4">
                        <div class="flex justify-between items-center">
                            <label class="text-sm">Discount:</label>
                            <input type="number" class="form-control w-24 text-right"
                                   value="${this.currentOrder.discount}"
                                   onchange="modernApp.modules.pos.updateDiscount(this.value)"
                                   min="0" step="0.01">
                        </div>
                    </div>

                    <!-- Order Total -->
                    <div class="order-total mb-4">
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span>Items Subtotal:</span>
                                <span>PKR ${subtotal.toLocaleString()}</span>
                            </div>
                            ${this.currentOrder.serviceType === 'dine_in' ? `
                                <div class="flex justify-between">
                                    <span>Per Head (${this.currentOrder.customerCount} × ${this.currentOrder.perHeadCharges}):</span>
                                    <span>PKR ${(this.currentOrder.customerCount * this.currentOrder.perHeadCharges).toLocaleString()}</span>
                                </div>
                            ` : ''}
                            ${this.currentOrder.extraCharges.length > 0 ? `
                                <div class="flex justify-between">
                                    <span>Extra Charges:</span>
                                    <span>PKR ${this.calculateExtraChargesTotal().toLocaleString()}</span>
                                </div>
                            ` : ''}
                            ${this.currentOrder.discount > 0 ? `
                                <div class="flex justify-between">
                                    <span>Discount:</span>
                                    <span class="text-red-600">-PKR ${this.currentOrder.discount.toLocaleString()}</span>
                                </div>
                            ` : ''}
                            <hr class="my-2">
                            <div class="flex justify-between font-bold text-lg">
                                <span>Total:</span>
                                <span class="text-orange-600">PKR ${total.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method Tiles -->
                    <div class="payment-method mb-4">
                        <label class="form-label mb-2">Payment Method</label>
                        <div class="payment-tiles">
                            ${this.generatePaymentTiles()}
                        </div>
                    </div>

                    <!-- Udhar Details (Only when Udhar is selected) -->
                    ${this.currentOrder.paymentMethod === 'udhar' ? this.generateUdharContainer() : ''}

                    <!-- Print Bill Option -->
                    <div class="print-option mb-4">
                        <label class="flex items-center">
                            <input type="checkbox"
                                   ${this.currentOrder.printBill ? 'checked' : ''}
                                   onchange="modernApp.modules.pos.updatePrintBill(this.checked)"
                                   class="mr-2">
                            <span class="text-sm">Print Bill</span>
                        </label>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-2">
                        <button onclick="modernApp.modules.pos.processOrder()"
                                class="w-full btn btn-primary btn-lg"
                                ${this.currentOrder.items.length === 0 ? 'disabled' : ''}>
                            <i class="fas fa-check mr-2"></i>Process Order
                        </button>
                        <button onclick="modernApp.modules.pos.saveAsDraft()"
                                class="w-full btn btn-secondary"
                                ${this.currentOrder.items.length === 0 ? 'disabled' : ''}>
                            <i class="fas fa-save mr-2"></i>Save as Draft
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate order items list with quick actions
     */
    generateOrderItems() {
        if (this.currentOrder.items.length === 0) {
            return '<p class="text-gray-500 text-center py-4">No items added</p>';
        }

        return `
            <div class="space-y-3">
                ${this.currentOrder.items.map((item, index) => {
                    const menuItem = this.app.dataManager.get('menuItems').find(m => m.id === item.id);
                    const quickActions = this.getQuickActionsForItem(menuItem);

                    return `
                        <div class="order-item-card">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <p class="font-medium">${item.name}</p>
                                    <p class="text-sm text-gray-600">PKR ${item.price.toLocaleString()} each</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="modernApp.modules.pos.decreaseQuantity(${index})"
                                            class="btn-action btn-edit">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <span class="w-8 text-center font-medium">${item.quantity}</span>
                                    <button onclick="modernApp.modules.pos.increaseQuantity(${index})"
                                            class="btn-action btn-edit">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <button onclick="modernApp.modules.pos.removeItem(${index})"
                                            class="btn-action btn-delete ml-2">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            ${quickActions.length > 0 ? `
                                <div class="quick-actions mt-2">
                                    <div class="flex space-x-2">
                                        ${quickActions.map(action => `
                                            <button onclick="modernApp.modules.pos.addQuickQuantity(${index}, ${action.quantity})"
                                                    class="quick-action-btn">
                                                ${action.label}
                                            </button>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    /**
     * Generate extra charges list
     */
    generateExtraCharges() {
        if (this.currentOrder.extraCharges.length === 0) {
            return '<p class="text-gray-500 text-center py-2 text-sm">No extra charges</p>';
        }

        return `
            <div class="space-y-2">
                ${this.currentOrder.extraCharges.map((charge, index) => `
                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div class="flex-1">
                            <p class="font-medium text-sm">${charge.name}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm">PKR ${charge.amount.toLocaleString()}</span>
                            <button onclick="modernApp.modules.pos.removeExtraCharge(${index})"
                                    class="btn-action btn-delete">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate payment method tiles
     */
    generatePaymentTiles() {
        const methods = [
            { id: 'cash', label: 'Cash', icon: 'fas fa-money-bill-wave' },
            { id: 'card', label: 'Card', icon: 'fas fa-credit-card' },
            { id: 'online', label: 'Online', icon: 'fas fa-mobile-alt' },
            { id: 'udhar', label: 'Udhar', icon: 'fas fa-handshake' }
        ];

        return `
            <div class="grid grid-cols-2 gap-2">
                ${methods.map(method => `
                    <button onclick="modernApp.modules.pos.updatePaymentMethod('${method.id}')"
                            class="payment-tile ${this.currentOrder.paymentMethod === method.id ? 'active' : ''}">
                        <i class="${method.icon} text-lg mb-1"></i>
                        <span class="text-sm">${method.label}</span>
                    </button>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate Udhar details container
     */
    generateUdharContainer() {
        return `
            <div class="udhar-container mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold mb-3 text-blue-900">Udhar Details</h4>
                <div class="space-y-3">
                    <div class="form-group">
                        <label class="form-label text-sm">Customer Name *</label>
                        <input type="text"
                               value="${this.currentOrder.udharDetails.customerName}"
                               onchange="modernApp.modules.pos.updateUdharDetails('customerName', this.value)"
                               class="form-control"
                               placeholder="Enter customer name">
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="form-group">
                            <label class="form-label text-sm">Udhar Amount</label>
                            <input type="number"
                                   value="${this.currentOrder.udharDetails.udharAmount}"
                                   onchange="modernApp.modules.pos.updateUdharDetails('udharAmount', this.value)"
                                   class="form-control"
                                   min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label text-sm">Phone Number</label>
                            <input type="tel"
                                   value="${this.currentOrder.udharDetails.phone}"
                                   onchange="modernApp.modules.pos.updateUdharDetails('phone', this.value)"
                                   class="form-control"
                                   placeholder="03xxxxxxxxx">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label text-sm">Notes (Optional)</label>
                        <textarea
                                value="${this.currentOrder.udharDetails.notes}"
                                onchange="modernApp.modules.pos.updateUdharDetails('notes', this.value)"
                                class="form-control"
                                rows="2"
                                placeholder="Additional notes..."></textarea>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get unique categories from menu items
     */
    getUniqueCategories(menuItems) {
        const categories = [...new Set(menuItems.map(item => item.category))];
        return categories.filter(category => category && category.trim() !== '');
    }

    /**
     * Get shortcut key for item
     */
    getShortcutForItem(itemName) {
        for (const [key, name] of this.keyboardShortcuts.entries()) {
            if (name === itemName) {
                return key;
            }
        }
        return null;
    }

    /**
     * Get quick actions for item based on category
     */
    getQuickActionsForItem(menuItem) {
        if (!menuItem) return [];

        const category = menuItem.category;
        return this.quickActions.get(category) || this.quickActions.get('default') || [];
    }

    /**
     * Calculate extra charges total
     */
    calculateExtraChargesTotal() {
        return this.currentOrder.extraCharges.reduce((total, charge) => {
            return total + (parseFloat(charge.amount) || 0);
        }, 0);
    }

    /**
     * Update search query
     */
    updateSearch(query) {
        this.searchQuery = query;
        this.refreshMenuItems();
    }

    /**
     * Refresh menu items display
     */
    refreshMenuItems() {
        const menuItemsContainer = document.querySelector('.menu-items-grid');
        if (menuItemsContainer) {
            const menuItems = this.app.dataManager.get('menuItems');
            menuItemsContainer.innerHTML = this.generateMenuItemsGrid(menuItems);
        }
    }

    /**
     * Select category
     */
    selectCategory(category) {
        this.selectedCategory = category;
        this.app.refreshCurrentPage();
    }

    /**
     * Add item to order
     */
    addToOrder(itemId) {
        const menuItems = this.app.dataManager.get('menuItems');
        const menuItem = menuItems.find(item => item.id === itemId);
        
        if (!menuItem || !menuItem.isAvailable) {
            this.app.showToast('Item is not available', 'warning');
            return;
        }

        // Check if item already exists in order
        const existingItemIndex = this.currentOrder.items.findIndex(item => item.id === itemId);
        
        if (existingItemIndex >= 0) {
            this.currentOrder.items[existingItemIndex].quantity += 1;
        } else {
            this.currentOrder.items.push({
                id: menuItem.id,
                name: menuItem.name,
                price: menuItem.price,
                quantity: 1
            });
        }

        this.refreshOrderSummary();
        this.app.showToast(`${menuItem.name} added to order`, 'success');
    }

    /**
     * Remove item from order
     */
    removeItem(index) {
        const item = this.currentOrder.items[index];
        this.currentOrder.items.splice(index, 1);
        this.refreshOrderSummary();
        this.app.showToast(`${item.name} removed from order`, 'info');
    }

    /**
     * Increase item quantity
     */
    increaseQuantity(index) {
        this.currentOrder.items[index].quantity += 1;
        this.refreshOrderSummary();
    }

    /**
     * Decrease item quantity
     */
    decreaseQuantity(index) {
        if (this.currentOrder.items[index].quantity > 1) {
            this.currentOrder.items[index].quantity -= 1;
            this.refreshOrderSummary();
        }
    }

    /**
     * Add quick quantity to item
     */
    addQuickQuantity(itemIndex, quantity) {
        this.currentOrder.items[itemIndex].quantity += quantity;
        this.refreshOrderSummary();
        this.app.showToast(`Added ${quantity} more ${this.currentOrder.items[itemIndex].name}`, 'success');
    }

    /**
     * Add extra charge
     */
    addExtraCharge() {
        const name = prompt('Enter charge name:');
        if (!name) return;

        const amount = parseFloat(prompt('Enter charge amount:'));
        if (isNaN(amount) || amount <= 0) {
            this.app.showToast('Invalid amount', 'error');
            return;
        }

        this.currentOrder.extraCharges.push({
            name: name.trim(),
            amount: amount
        });

        this.refreshOrderSummary();
        this.app.showToast(`Added extra charge: ${name}`, 'success');
    }

    /**
     * Remove extra charge
     */
    removeExtraCharge(index) {
        const charge = this.currentOrder.extraCharges[index];
        this.currentOrder.extraCharges.splice(index, 1);
        this.refreshOrderSummary();
        this.app.showToast(`Removed charge: ${charge.name}`, 'info');
    }

    /**
     * Update Udhar details
     */
    updateUdharDetails(field, value) {
        this.currentOrder.udharDetails[field] = value;
    }

    /**
     * Update print bill option
     */
    updatePrintBill(checked) {
        this.currentOrder.printBill = checked;
    }

    /**
     * Update customer count
     */
    updateCustomerCount(count) {
        this.currentOrder.customerCount = parseInt(count) || 1;
        this.refreshOrderSummary();
    }

    /**
     * Update service type
     */
    updateServiceType(type) {
        this.currentOrder.serviceType = type;

        // Reset customer count for takeaway
        if (type === 'takeaway') {
            this.currentOrder.customerCount = 1;
            this.currentOrder.perHeadCharges = 0;
        } else if (type === 'dine_in' && this.currentOrder.perHeadCharges === 0) {
            this.currentOrder.perHeadCharges = 100; // Default per head charge
        }

        this.refreshOrderSummary();
    }

    /**
     * Update payment method
     */
    updatePaymentMethod(method) {
        this.currentOrder.paymentMethod = method;
    }

    /**
     * Update per head charges
     */
    updatePerHeadCharges(amount) {
        this.currentOrder.perHeadCharges = parseFloat(amount) || 0;
        this.refreshOrderSummary();
    }

    /**
     * Update additional charges
     */
    updateAdditionalCharges(amount) {
        this.currentOrder.additionalCharges = parseFloat(amount) || 0;
        this.refreshOrderSummary();
    }

    /**
     * Update discount
     */
    updateDiscount(amount) {
        this.currentOrder.discount = parseFloat(amount) || 0;
        this.refreshOrderSummary();
    }

    /**
     * Calculate subtotal
     */
    calculateSubtotal() {
        return this.currentOrder.items.reduce((total, item) => {
            return total + (item.price * item.quantity);
        }, 0);
    }

    /**
     * Calculate total
     */
    calculateTotal() {
        const subtotal = this.calculateSubtotal();
        const perHeadTotal = this.currentOrder.serviceType === 'dine_in' ?
            (this.currentOrder.customerCount * this.currentOrder.perHeadCharges) : 0;
        const extraChargesTotal = this.calculateExtraChargesTotal();
        const total = subtotal + perHeadTotal + extraChargesTotal - this.currentOrder.discount;
        return Math.max(0, total);
    }

    /**
     * Refresh order summary
     */
    refreshOrderSummary() {
        const summaryPanel = document.querySelector('.order-summary-panel');
        if (summaryPanel) {
            summaryPanel.innerHTML = this.generateOrderSummary();
        }
    }

    /**
     * Process order
     */
    async processOrder() {
        try {
            if (this.currentOrder.items.length === 0) {
                this.app.showToast('Please add items to the order', 'warning');
                return;
            }

            // Validate Udhar details if payment method is Udhar
            if (this.currentOrder.paymentMethod === 'udhar') {
                if (!this.currentOrder.udharDetails.customerName.trim()) {
                    this.app.showToast('Customer name is required for Udhar payment', 'error');
                    return;
                }
            }

            const orderData = {
                orderNumber: this.generateOrderNumber(),
                customerName: this.currentOrder.paymentMethod === 'udhar' ?
                    this.currentOrder.udharDetails.customerName : 'Walk-in Customer',
                customerCount: this.currentOrder.customerCount,
                serviceType: this.currentOrder.serviceType,
                paymentMethod: this.currentOrder.paymentMethod,
                items: this.currentOrder.items,
                totalAmount: this.calculateTotal(),
                perHeadCharges: this.currentOrder.serviceType === 'dine_in' ? this.currentOrder.perHeadCharges : 0,
                extraCharges: this.currentOrder.extraCharges,
                discount: this.currentOrder.discount,
                udharDetails: this.currentOrder.paymentMethod === 'udhar' ? this.currentOrder.udharDetails : null,
                status: 'completed',
                createdAt: new Date().toISOString()
            };

            // Save order
            await this.app.dataManager.add('orders', orderData);

            // Print bill if requested
            if (this.currentOrder.printBill) {
                this.printBill(orderData);
            }

            // Clear current order
            this.clearOrder();

            this.app.showToast('Order processed successfully!', 'success');

        } catch (error) {
            console.error('❌ Order processing failed:', error);
            this.app.showToast('Failed to process order', 'error');
        }
    }

    /**
     * Save as draft
     */
    async saveAsDraft() {
        // Implementation for saving draft orders
        this.app.showToast('Order saved as draft', 'info');
    }

    /**
     * Clear order
     */
    clearOrder() {
        this.currentOrder = {
            items: [],
            customerCount: 1,
            serviceType: 'dine_in',
            paymentMethod: 'cash',
            perHeadCharges: 100,
            extraCharges: [],
            discount: 0,
            udharDetails: {
                customerName: '',
                udharAmount: 0,
                phone: '',
                notes: ''
            },
            printBill: true
        };
        this.refreshOrderSummary();
    }

    /**
     * Generate order number
     */
    generateOrderNumber() {
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0].replace(/-/g, '');
        const timeStr = now.getTime().toString().slice(-4);
        return `ORD${dateStr}${timeStr}`;
    }

    /**
     * Print bill
     */
    printBill(orderData) {
        const billContent = this.generateBillContent(orderData);

        // Create a new window for printing
        const printWindow = window.open('', '_blank', 'width=300,height=600');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Bill - ${orderData.orderNumber}</title>
                    <style>
                        body {
                            font-family: 'Courier New', monospace;
                            font-size: 12px;
                            line-height: 1.4;
                            margin: 0;
                            padding: 10px;
                            width: 280px;
                        }
                        .header {
                            text-align: center;
                            border-bottom: 2px solid #000;
                            padding-bottom: 10px;
                            margin-bottom: 10px;
                        }
                        .restaurant-name {
                            font-size: 18px;
                            font-weight: bold;
                            margin-bottom: 5px;
                        }
                        .order-info {
                            margin-bottom: 10px;
                            border-bottom: 1px dashed #000;
                            padding-bottom: 10px;
                        }
                        .items-table {
                            width: 100%;
                            margin-bottom: 10px;
                        }
                        .items-table th,
                        .items-table td {
                            text-align: left;
                            padding: 2px 0;
                        }
                        .items-table th {
                            border-bottom: 1px solid #000;
                        }
                        .total-section {
                            border-top: 1px solid #000;
                            padding-top: 10px;
                            margin-top: 10px;
                        }
                        .total-line {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 3px;
                        }
                        .grand-total {
                            font-weight: bold;
                            font-size: 14px;
                            border-top: 1px solid #000;
                            padding-top: 5px;
                            margin-top: 5px;
                        }
                        .footer {
                            text-align: center;
                            margin-top: 15px;
                            border-top: 1px dashed #000;
                            padding-top: 10px;
                            font-size: 10px;
                        }
                        @media print {
                            body { margin: 0; }
                        }
                    </style>
                </head>
                <body>
                    ${billContent}
                </body>
            </html>
        `);

        printWindow.document.close();

        // Wait for content to load then print
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    }

    /**
     * Generate bill content
     */
    generateBillContent(orderData) {
        const itemsSubtotal = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const perHeadTotal = orderData.serviceType === 'dine_in' ?
            (orderData.customerCount * orderData.perHeadCharges) : 0;
        const extraChargesTotal = orderData.extraCharges ?
            orderData.extraCharges.reduce((sum, charge) => sum + charge.amount, 0) : 0;

        return `
            <div class="header">
                <div class="restaurant-name">ZAIQA RESTAURANT</div>
                <div>Delicious Pakistani Cuisine</div>
                <div>Phone: +92-XXX-XXXXXXX</div>
            </div>

            <div class="order-info">
                <div><strong>Order #:</strong> ${orderData.orderNumber}</div>
                <div><strong>Date:</strong> ${new Date(orderData.createdAt).toLocaleDateString()}</div>
                <div><strong>Time:</strong> ${new Date(orderData.createdAt).toLocaleTimeString()}</div>
                <div><strong>Service:</strong> ${orderData.serviceType.replace('_', ' ').toUpperCase()}</div>
                <div><strong>Payment:</strong> ${orderData.paymentMethod.toUpperCase()}</div>
                ${orderData.customerName !== 'Walk-in Customer' ? `<div><strong>Customer:</strong> ${orderData.customerName}</div>` : ''}
                ${orderData.serviceType === 'dine_in' ? `<div><strong>Customers:</strong> ${orderData.customerCount}</div>` : ''}
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Qty</th>
                        <th>Rate</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    ${orderData.items.map(item => `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.quantity}</td>
                            <td>${item.price}</td>
                            <td>${(item.price * item.quantity).toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="total-section">
                <div class="total-line">
                    <span>Items Subtotal:</span>
                    <span>PKR ${itemsSubtotal.toLocaleString()}</span>
                </div>

                ${perHeadTotal > 0 ? `
                    <div class="total-line">
                        <span>Per Head (${orderData.customerCount} × ${orderData.perHeadCharges}):</span>
                        <span>PKR ${perHeadTotal.toLocaleString()}</span>
                    </div>
                ` : ''}

                ${extraChargesTotal > 0 ? `
                    ${orderData.extraCharges.map(charge => `
                        <div class="total-line">
                            <span>${charge.name}:</span>
                            <span>PKR ${charge.amount.toLocaleString()}</span>
                        </div>
                    `).join('')}
                ` : ''}

                ${orderData.discount > 0 ? `
                    <div class="total-line">
                        <span>Discount:</span>
                        <span>-PKR ${orderData.discount.toLocaleString()}</span>
                    </div>
                ` : ''}

                <div class="total-line grand-total">
                    <span>TOTAL:</span>
                    <span>PKR ${orderData.totalAmount.toLocaleString()}</span>
                </div>
            </div>

            <div class="footer">
                <div>Thank you for dining with us!</div>
                <div>Visit us again soon</div>
                <div>---</div>
                <div>Powered by Zaiqa POS System</div>
            </div>
        `;
    }

    /**
     * Open POS modal (optimized size)
     */
    async openModal() {
        const modal = this.app.modalManager.create({
            title: 'Point of Sale System',
            size: 'xlarge', // Changed from 'full' to 'xlarge' for better usability
            content: await this.render()
        });

        modal.show();
    }

    /**
     * Called when page loads
     */
    async onPageLoad() {
        // Add any page-specific initialization
        this.addPOSStyles();
    }

    /**
     * Add enhanced POS-specific styles
     */
    addPOSStyles() {
        if (!document.getElementById('pos-styles')) {
            const styles = document.createElement('style');
            styles.id = 'pos-styles';
            styles.textContent = `
                /* Menu Item Cards */
                .menu-item-card {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 12px;
                    padding: 16px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    position: relative;
                }

                .menu-item-card:hover {
                    border-color: #f97316;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
                }

                .menu-item-card.unavailable {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                .shortcut-badge {
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    background: #f97316;
                    color: white;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    font-weight: bold;
                }

                .menu-item-image {
                    text-align: center;
                    margin-bottom: 12px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f8fafc;
                    border-radius: 8px;
                }

                .menu-item-name {
                    font-weight: 600;
                    color: #1f2937;
                    margin-bottom: 4px;
                    font-size: 14px;
                }

                .menu-item-price {
                    color: #f97316;
                    font-weight: 700;
                    font-size: 16px;
                    margin-bottom: 4px;
                }

                .menu-item-description {
                    color: #6b7280;
                    font-size: 12px;
                    line-height: 1.4;
                }

                .unavailable-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.7);
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 12px;
                }

                /* Category Buttons */
                .category-btn {
                    padding: 8px 16px;
                    border: 1px solid #d1d5db;
                    border-radius: 20px;
                    background: white;
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .category-btn:hover {
                    border-color: #f97316;
                    color: #f97316;
                }

                .category-btn.active {
                    background: #f97316;
                    border-color: #f97316;
                    color: white;
                }

                /* Service Type Tabs */
                .service-type-tabs {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    background: #f3f4f6;
                    padding: 4px;
                    border-radius: 8px;
                }

                .service-tab {
                    padding: 12px 16px;
                    border: none;
                    border-radius: 6px;
                    background: transparent;
                    color: #6b7280;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .service-tab:hover {
                    background: #e5e7eb;
                    color: #374151;
                }

                .service-tab.active {
                    background: white;
                    color: #f97316;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                }

                /* Payment Tiles */
                .payment-tiles {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                }

                .payment-tile {
                    padding: 16px 12px;
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    background: white;
                    color: #6b7280;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    min-height: 60px;
                }

                .payment-tile:hover {
                    border-color: #f97316;
                    color: #f97316;
                    transform: translateY(-1px);
                }

                .payment-tile.active {
                    border-color: #f97316;
                    background: #fff7ed;
                    color: #f97316;
                    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
                }

                /* Order Items */
                .order-item-card {
                    margin-bottom: 8px;
                }

                .quick-actions {
                    display: flex;
                    justify-content: center;
                }

                .quick-action-btn {
                    padding: 4px 8px;
                    margin: 0 2px;
                    border: 1px solid #d1d5db;
                    border-radius: 4px;
                    background: white;
                    color: #6b7280;
                    font-size: 12px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .quick-action-btn:hover {
                    border-color: #f97316;
                    color: #f97316;
                    background: #fff7ed;
                }

                /* Udhar Container */
                .udhar-container {
                    animation: slideDown 0.3s ease-out;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                /* Order Summary Panel */
                .order-summary-panel {
                    position: sticky;
                    top: 20px;
                    height: fit-content;
                    max-height: calc(100vh - 40px);
                    overflow-y: auto;
                }

                /* Responsive Design */
                @media (max-width: 1024px) {
                    .order-summary-panel {
                        position: static;
                        max-height: none;
                    }

                    .payment-tiles {
                        grid-template-columns: 1fr 1fr;
                    }

                    .service-type-tabs {
                        grid-template-columns: 1fr 1fr;
                    }
                }

                @media (max-width: 640px) {
                    .payment-tiles {
                        grid-template-columns: 1fr;
                    }

                    .service-type-tabs {
                        grid-template-columns: 1fr;
                    }

                    .menu-item-card {
                        padding: 12px;
                    }

                    .quick-actions {
                        flex-wrap: wrap;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
    }
}

// Export for global use
console.log('✅ POSModule class defined, exporting to window...');
window.POSModule = POSModule;
console.log('✅ POSModule exported successfully');
