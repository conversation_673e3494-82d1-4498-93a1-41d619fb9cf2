/**
 * Zaiqa Restaurant Financial Calculations - Clean Architecture
 * 100% Accurate Financial Calculations for Restaurant Operations
 * Revenue, Expenses, Profit/Loss, Cash Flow Analysis
 */

class ZaiqaFinancialCalculations {
    constructor(financialEngine, databaseManager) {
        this.financialEngine = financialEngine;
        this.databaseManager = databaseManager;
        this.version = '2.0.0';
        
        // Validate dependencies
        this.validateDependencies();
        
        console.log('📊 Financial Calculations module initialized v' + this.version);
    }

    /**
     * Validate required dependencies
     */
    validateDependencies() {
        if (!this.financialEngine || !this.financialEngine.isInitialized()) {
            throw new Error('Financial Engine is required and must be initialized');
        }
        
        if (!this.databaseManager || !this.databaseManager.isInitialized()) {
            throw new Error('Database Manager is required and must be initialized');
        }
    }

    /**
     * Calculate total revenue for a specific date range
     */
    calculateRevenue(startDate, endDate) {
        try {
            // Get orders within date range
            const orders = this.databaseManager.read('orders', {
                status: 'completed'
            }).filter(order => {
                const orderDate = new Date(order.createdAt);
                const start = new Date(startDate);
                const end = new Date(endDate);
                return orderDate >= start && orderDate <= end;
            });

            // Calculate order revenue
            const orderRevenue = orders.reduce((total, order) => {
                return this.financialEngine.calculate('add', total, order.totalAmount || 0);
            }, 0);

            // Get Udhar payments within date range
            const udhars = this.databaseManager.read('udhars');
            let udharPayments = 0;

            udhars.forEach(udhar => {
                if (udhar.transactions && Array.isArray(udhar.transactions)) {
                    udhar.transactions.forEach(transaction => {
                        if (transaction.type === 'payment') {
                            const transactionDate = new Date(transaction.date);
                            const start = new Date(startDate);
                            const end = new Date(endDate);
                            
                            if (transactionDate >= start && transactionDate <= end) {
                                udharPayments = this.financialEngine.calculate('add', udharPayments, transaction.amount || 0);
                            }
                        }
                    });
                }
            });

            const totalRevenue = this.financialEngine.calculate('add', orderRevenue, udharPayments);

            return {
                orderRevenue: orderRevenue,
                udharPayments: udharPayments,
                totalRevenue: totalRevenue,
                orderCount: orders.length,
                averageOrderValue: orders.length > 0 ? 
                    this.financialEngine.calculate('divide', orderRevenue, orders.length) : 0
            };

        } catch (error) {
            console.error('❌ Revenue calculation failed:', error);
            return {
                orderRevenue: 0,
                udharPayments: 0,
                totalRevenue: 0,
                orderCount: 0,
                averageOrderValue: 0
            };
        }
    }

    /**
     * Calculate total expenses for a specific date range
     */
    calculateExpenses(startDate, endDate) {
        try {
            // Get expenses within date range
            const expenses = this.databaseManager.read('expenses').filter(expense => {
                const expenseDate = new Date(expense.date);
                const start = new Date(startDate);
                const end = new Date(endDate);
                return expenseDate >= start && expenseDate <= end;
            });

            // Group expenses by category
            const expensesByCategory = {};
            let totalExpenses = 0;

            expenses.forEach(expense => {
                const category = expense.category || 'Other';
                const amount = expense.amount || 0;
                
                if (!expensesByCategory[category]) {
                    expensesByCategory[category] = 0;
                }
                
                expensesByCategory[category] = this.financialEngine.calculate('add', 
                    expensesByCategory[category], amount);
                totalExpenses = this.financialEngine.calculate('add', totalExpenses, amount);
            });

            return {
                totalExpenses: totalExpenses,
                expensesByCategory: expensesByCategory,
                expenseCount: expenses.length,
                averageExpense: expenses.length > 0 ? 
                    this.financialEngine.calculate('divide', totalExpenses, expenses.length) : 0
            };

        } catch (error) {
            console.error('❌ Expense calculation failed:', error);
            return {
                totalExpenses: 0,
                expensesByCategory: {},
                expenseCount: 0,
                averageExpense: 0
            };
        }
    }

    /**
     * Calculate profit and loss for a specific date range
     */
    calculateProfitLoss(startDate, endDate) {
        try {
            const revenue = this.calculateRevenue(startDate, endDate);
            const expenses = this.calculateExpenses(startDate, endDate);

            const grossProfit = this.financialEngine.calculate('subtract', 
                revenue.totalRevenue, expenses.totalExpenses);
            
            const profitMargin = revenue.totalRevenue > 0 ? 
                this.financialEngine.calculate('percentage', grossProfit, revenue.totalRevenue) : 0;

            return {
                revenue: revenue,
                expenses: expenses,
                grossProfit: grossProfit,
                profitMargin: profitMargin,
                isProfit: grossProfit >= 0
            };

        } catch (error) {
            console.error('❌ Profit/Loss calculation failed:', error);
            return {
                revenue: { totalRevenue: 0 },
                expenses: { totalExpenses: 0 },
                grossProfit: 0,
                profitMargin: 0,
                isProfit: false
            };
        }
    }

    /**
     * Calculate cash flow for a specific date
     */
    calculateCashFlow(date) {
        try {
            const businessDate = date || this.financialEngine.getCurrentBusinessDate();
            const dateRange = this.financialEngine.getBusinessDateRange(businessDate);

            // Get cash register data
            const cashRegister = this.databaseManager.read('cashRegister', {
                date: businessDate
            });

            const cashEntry = cashRegister.length > 0 ? cashRegister[0] : null;

            // Calculate day's revenue and expenses
            const dayRevenue = this.calculateRevenue(dateRange.start, dateRange.end);
            const dayExpenses = this.calculateExpenses(businessDate, businessDate);

            // Calculate cash flow
            const openingBalance = cashEntry ? cashEntry.morningBalance || 0 : 0;
            const closingBalance = cashEntry ? cashEntry.eveningBalance || 0 : 0;
            
            const expectedClosing = this.financialEngine.calculate('add',
                this.financialEngine.calculate('subtract', openingBalance, dayExpenses.totalExpenses),
                dayRevenue.orderRevenue // Only cash orders, not Udhar
            );

            const cashDifference = this.financialEngine.calculate('subtract', 
                closingBalance, expectedClosing);

            return {
                date: businessDate,
                openingBalance: openingBalance,
                closingBalance: closingBalance,
                expectedClosing: expectedClosing,
                cashDifference: cashDifference,
                dayRevenue: dayRevenue.orderRevenue,
                dayExpenses: dayExpenses.totalExpenses,
                netCashFlow: this.financialEngine.calculate('subtract', 
                    dayRevenue.orderRevenue, dayExpenses.totalExpenses)
            };

        } catch (error) {
            console.error('❌ Cash flow calculation failed:', error);
            return {
                date: date || this.financialEngine.getCurrentBusinessDate(),
                openingBalance: 0,
                closingBalance: 0,
                expectedClosing: 0,
                cashDifference: 0,
                dayRevenue: 0,
                dayExpenses: 0,
                netCashFlow: 0
            };
        }
    }

    /**
     * Calculate today's business metrics
     */
    calculateTodayMetrics() {
        try {
            const businessDate = this.financialEngine.getCurrentBusinessDate();
            const dateRange = this.financialEngine.getBusinessDateRange(businessDate);

            const revenue = this.calculateRevenue(dateRange.start, dateRange.end);
            const expenses = this.calculateExpenses(businessDate, businessDate);
            const profitLoss = this.calculateProfitLoss(dateRange.start, dateRange.end);
            const cashFlow = this.calculateCashFlow(businessDate);

            return {
                businessDate: businessDate,
                revenue: revenue,
                expenses: expenses,
                profitLoss: profitLoss,
                cashFlow: cashFlow,
                calculatedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ Today metrics calculation failed:', error);
            return this.getEmptyMetrics();
        }
    }

    /**
     * Calculate metrics for a specific date range
     */
    calculatePeriodMetrics(startDate, endDate) {
        try {
            const revenue = this.calculateRevenue(startDate, endDate);
            const expenses = this.calculateExpenses(startDate, endDate);
            const profitLoss = this.calculateProfitLoss(startDate, endDate);

            // Calculate daily averages
            const daysDiff = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1;
            
            const dailyAverages = {
                revenue: daysDiff > 0 ? this.financialEngine.calculate('divide', revenue.totalRevenue, daysDiff) : 0,
                expenses: daysDiff > 0 ? this.financialEngine.calculate('divide', expenses.totalExpenses, daysDiff) : 0,
                profit: daysDiff > 0 ? this.financialEngine.calculate('divide', profitLoss.grossProfit, daysDiff) : 0
            };

            return {
                startDate: startDate,
                endDate: endDate,
                dayCount: daysDiff,
                revenue: revenue,
                expenses: expenses,
                profitLoss: profitLoss,
                dailyAverages: dailyAverages,
                calculatedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ Period metrics calculation failed:', error);
            return this.getEmptyPeriodMetrics(startDate, endDate);
        }
    }

    /**
     * Calculate top-selling items
     */
    calculateTopItems(startDate, endDate, limit = 10) {
        try {
            const orders = this.databaseManager.read('orders', {
                status: 'completed'
            }).filter(order => {
                const orderDate = new Date(order.createdAt);
                const start = new Date(startDate);
                const end = new Date(endDate);
                return orderDate >= start && orderDate <= end;
            });

            const itemStats = {};

            orders.forEach(order => {
                if (order.items && Array.isArray(order.items)) {
                    order.items.forEach(item => {
                        const itemName = item.name;
                        if (!itemStats[itemName]) {
                            itemStats[itemName] = {
                                name: itemName,
                                quantity: 0,
                                revenue: 0,
                                orderCount: 0
                            };
                        }

                        itemStats[itemName].quantity = this.financialEngine.calculate('add',
                            itemStats[itemName].quantity, item.quantity || 0);
                        
                        const itemRevenue = this.financialEngine.calculate('multiply',
                            item.price || 0, item.quantity || 0);
                        
                        itemStats[itemName].revenue = this.financialEngine.calculate('add',
                            itemStats[itemName].revenue, itemRevenue);
                        
                        itemStats[itemName].orderCount += 1;
                    });
                }
            });

            // Sort by quantity and limit results
            const topItems = Object.values(itemStats)
                .sort((a, b) => b.quantity - a.quantity)
                .slice(0, limit);

            return topItems;

        } catch (error) {
            console.error('❌ Top items calculation failed:', error);
            return [];
        }
    }

    /**
     * Get empty metrics structure
     */
    getEmptyMetrics() {
        return {
            businessDate: this.financialEngine.getCurrentBusinessDate(),
            revenue: { totalRevenue: 0, orderRevenue: 0, udharPayments: 0, orderCount: 0, averageOrderValue: 0 },
            expenses: { totalExpenses: 0, expensesByCategory: {}, expenseCount: 0, averageExpense: 0 },
            profitLoss: { grossProfit: 0, profitMargin: 0, isProfit: false },
            cashFlow: { openingBalance: 0, closingBalance: 0, netCashFlow: 0 },
            calculatedAt: new Date().toISOString()
        };
    }

    /**
     * Get empty period metrics structure
     */
    getEmptyPeriodMetrics(startDate, endDate) {
        return {
            startDate: startDate,
            endDate: endDate,
            dayCount: 0,
            revenue: { totalRevenue: 0, orderRevenue: 0, udharPayments: 0, orderCount: 0, averageOrderValue: 0 },
            expenses: { totalExpenses: 0, expensesByCategory: {}, expenseCount: 0, averageExpense: 0 },
            profitLoss: { grossProfit: 0, profitMargin: 0, isProfit: false },
            dailyAverages: { revenue: 0, expenses: 0, profit: 0 },
            calculatedAt: new Date().toISOString()
        };
    }

    /**
     * Validate calculation results
     */
    validateResults(results) {
        try {
            // Check for NaN or infinite values
            const checkValue = (value, path = '') => {
                if (typeof value === 'number') {
                    if (!Number.isFinite(value)) {
                        console.warn(`Invalid number at ${path}:`, value);
                        return 0;
                    }
                }
                return value;
            };

            // Recursively validate all numeric values
            const validateObject = (obj, path = '') => {
                if (typeof obj === 'object' && obj !== null) {
                    for (const [key, value] of Object.entries(obj)) {
                        const currentPath = path ? `${path}.${key}` : key;
                        if (typeof value === 'object') {
                            validateObject(value, currentPath);
                        } else {
                            obj[key] = checkValue(value, currentPath);
                        }
                    }
                }
            };

            validateObject(results);
            return results;

        } catch (error) {
            console.error('❌ Results validation failed:', error);
            return results;
        }
    }

    /**
     * Get financial calculations status
     */
    getStatus() {
        return {
            version: this.version,
            financialEngineStatus: this.financialEngine.getStatus(),
            databaseManagerStatus: this.databaseManager.getStatus(),
            timestamp: new Date().toISOString()
        };
    }
}

    /**
     * Calculate Udhar summary
     */
    calculateUdharSummary() {
        try {
            const udhars = this.databaseManager.read('udhars');

            let totalUdharAmount = 0;
            let totalPaidAmount = 0;
            let totalRemainingAmount = 0;
            let activeCustomers = 0;

            udhars.forEach(udhar => {
                totalUdharAmount = this.financialEngine.calculate('add', totalUdharAmount, udhar.totalAmount || 0);
                totalPaidAmount = this.financialEngine.calculate('add', totalPaidAmount, udhar.paidAmount || 0);
                totalRemainingAmount = this.financialEngine.calculate('add', totalRemainingAmount, udhar.remainingAmount || 0);

                if (udhar.remainingAmount > 0) {
                    activeCustomers++;
                }
            });

            return {
                totalUdharAmount: totalUdharAmount,
                totalPaidAmount: totalPaidAmount,
                totalRemainingAmount: totalRemainingAmount,
                activeCustomers: activeCustomers,
                totalCustomers: udhars.length,
                recoveryRate: totalUdharAmount > 0 ?
                    this.financialEngine.calculate('percentage', totalPaidAmount, totalUdharAmount) : 0
            };

        } catch (error) {
            console.error('❌ Udhar summary calculation failed:', error);
            return {
                totalUdharAmount: 0,
                totalPaidAmount: 0,
                totalRemainingAmount: 0,
                activeCustomers: 0,
                totalCustomers: 0,
                recoveryRate: 0
            };
        }
    }

    /**
     * Calculate inventory value
     */
    calculateInventoryValue() {
        try {
            const inventory = this.databaseManager.read('inventory');

            let totalValue = 0;
            let lowStockItems = 0;
            let outOfStockItems = 0;

            inventory.forEach(item => {
                const itemValue = this.financialEngine.calculate('multiply',
                    item.quantity || 0, item.unitPrice || 0);
                totalValue = this.financialEngine.calculate('add', totalValue, itemValue);

                if (item.quantity <= 0) {
                    outOfStockItems++;
                } else if (item.quantity <= (item.lowStockThreshold || 0)) {
                    lowStockItems++;
                }
            });

            return {
                totalValue: totalValue,
                totalItems: inventory.length,
                lowStockItems: lowStockItems,
                outOfStockItems: outOfStockItems,
                inStockItems: inventory.length - outOfStockItems
            };

        } catch (error) {
            console.error('❌ Inventory value calculation failed:', error);
            return {
                totalValue: 0,
                totalItems: 0,
                lowStockItems: 0,
                outOfStockItems: 0,
                inStockItems: 0
            };
        }
    }
}

// Export the financial calculations for global use
window.ZaiqaFinancialCalculations = ZaiqaFinancialCalculations;
