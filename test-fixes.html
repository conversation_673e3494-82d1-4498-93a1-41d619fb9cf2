<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zaiqa System Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #fb923c;
            padding-bottom: 10px;
        }
        .test-button {
            background: #fb923c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #e8831e;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Zaiqa Restaurant System - Fixes Verification</h1>
        
        <div class="test-section">
            <h2>🔄 Fix #1: Order-Inventory Synchronization</h2>
            <p>Tests the deletion of bills/orders and verification of inventory restoration.</p>
            <button class="test-button" onclick="testOrderInventorySync()">Test Order-Inventory Sync</button>
            <button class="test-button" onclick="testBillDeletion()">Test Bill Deletion</button>
            <div id="sync-results" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>🗑️ Fix #2: Usage History Delete Functionality</h2>
            <p>Tests the delete buttons in inventory usage history and stock restoration.</p>
            <button class="test-button" onclick="testUsageHistoryDelete()">Test Usage History Delete</button>
            <button class="test-button" onclick="checkUsageHistoryButtons()">Check Delete Buttons</button>
            <div id="usage-results" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>📂 Fix #3: Custom Category Management</h2>
            <p>Tests custom category creation, editing, and filtering functionality.</p>
            <button class="test-button" onclick="testCustomCategories()">Test Custom Categories</button>
            <button class="test-button" onclick="checkCategoryDropdown()">Check Category Dropdown</button>
            <div id="category-results" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 Fix #4: Custom Period Reports</h2>
            <p>Tests comprehensive custom period report generation with full-page display.</p>
            <button class="test-button" onclick="testCustomPeriodReports()">Test Custom Period Reports</button>
            <button class="test-button" onclick="checkReportSections()">Check Report Sections</button>
            <div id="report-results" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>🔧 System Status</h2>
            <button class="test-button" onclick="checkSystemStatus()">Check Overall System Status</button>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
            <div id="system-results" class="test-results"></div>
        </div>
    </div>

    <script>
        // Test Functions
        function testOrderInventorySync() {
            const results = document.getElementById('sync-results');
            results.innerHTML = 'Testing Order-Inventory Synchronization...\n';
            
            try {
                // Check if functions exist
                if (typeof app !== 'undefined') {
                    const functions = ['deleteBill', 'processOrderDeletion', 'refreshAllDisplaysAfterOrderDeletion'];
                    let allExist = true;
                    
                    functions.forEach(func => {
                        if (typeof app[func] === 'function') {
                            results.innerHTML += `✅ ${func} function exists\n`;
                        } else {
                            results.innerHTML += `❌ ${func} function missing\n`;
                            allExist = false;
                        }
                    });
                    
                    if (allExist) {
                        results.innerHTML += '\n✅ All order-inventory sync functions are present\n';
                        results.innerHTML += 'Navigate to Bills page to test deletion functionality\n';
                    } else {
                        results.innerHTML += '\n❌ Some functions are missing\n';
                    }
                } else {
                    results.innerHTML += '❌ App object not found\n';
                }
            } catch (error) {
                results.innerHTML += `❌ Error: ${error.message}\n`;
            }
        }

        function testBillDeletion() {
            const results = document.getElementById('sync-results');
            results.innerHTML += '\nTesting Bill Deletion Process...\n';
            
            try {
                if (typeof app !== 'undefined' && typeof app.getOrders === 'function') {
                    const orders = app.getOrders();
                    results.innerHTML += `📋 Found ${orders.length} orders in system\n`;
                    
                    if (orders.length > 0) {
                        results.innerHTML += '✅ Orders available for deletion testing\n';
                        results.innerHTML += 'Go to Bills page and try deleting an order to test sync\n';
                    } else {
                        results.innerHTML += '⚠️ No orders found - create some orders first\n';
                    }
                } else {
                    results.innerHTML += '❌ Cannot access orders data\n';
                }
            } catch (error) {
                results.innerHTML += `❌ Error: ${error.message}\n`;
            }
        }

        function testUsageHistoryDelete() {
            const results = document.getElementById('usage-results');
            results.innerHTML = 'Testing Usage History Delete Functionality...\n';
            
            try {
                if (typeof app !== 'undefined') {
                    const functions = ['deleteUsageRecord', 'showUsageRecordDetails', 'generateUsageHistoryTable'];
                    let allExist = true;
                    
                    functions.forEach(func => {
                        if (typeof app[func] === 'function') {
                            results.innerHTML += `✅ ${func} function exists\n`;
                        } else {
                            results.innerHTML += `❌ ${func} function missing\n`;
                            allExist = false;
                        }
                    });
                    
                    if (allExist) {
                        results.innerHTML += '\n✅ All usage history functions are present\n';
                    }
                    
                    // Check usage records
                    if (typeof app.getUsageRecords === 'function') {
                        const records = app.getUsageRecords();
                        results.innerHTML += `📋 Found ${records.length} usage records\n`;
                    }
                } else {
                    results.innerHTML += '❌ App object not found\n';
                }
            } catch (error) {
                results.innerHTML += `❌ Error: ${error.message}\n`;
            }
        }

        function checkUsageHistoryButtons() {
            const results = document.getElementById('usage-results');
            results.innerHTML += '\nChecking Usage History Delete Buttons...\n';
            
            // Navigate to inventory page to check
            if (typeof app !== 'undefined' && typeof app.navigateToPage === 'function') {
                app.navigateToPage('inventory');
                setTimeout(() => {
                    const deleteButtons = document.querySelectorAll('[onclick*="deleteUsageRecord"]');
                    results.innerHTML += `🔍 Found ${deleteButtons.length} delete buttons in usage history\n`;
                    
                    if (deleteButtons.length > 0) {
                        results.innerHTML += '✅ Delete buttons are present in usage history table\n';
                    } else {
                        results.innerHTML += '❌ No delete buttons found in usage history table\n';
                    }
                }, 1000);
            }
        }

        function testCustomCategories() {
            const results = document.getElementById('category-results');
            results.innerHTML = 'Testing Custom Category Management...\n';
            
            try {
                if (typeof app !== 'undefined') {
                    const functions = [
                        'getCustomCategories', 
                        'saveCustomCategories',
                        'showAddCustomCategoryModal',
                        'editCustomCategory',
                        'deleteCustomCategory',
                        'refreshCategoryFilters'
                    ];
                    let allExist = true;
                    
                    functions.forEach(func => {
                        if (typeof app[func] === 'function') {
                            results.innerHTML += `✅ ${func} function exists\n`;
                        } else {
                            results.innerHTML += `❌ ${func} function missing\n`;
                            allExist = false;
                        }
                    });
                    
                    if (allExist) {
                        results.innerHTML += '\n✅ All custom category functions are present\n';
                    }
                    
                    // Check existing custom categories
                    if (typeof app.getCustomCategories === 'function') {
                        const customCategories = app.getCustomCategories();
                        results.innerHTML += `📂 Found ${customCategories.length} custom categories\n`;
                    }
                } else {
                    results.innerHTML += '❌ App object not found\n';
                }
            } catch (error) {
                results.innerHTML += `❌ Error: ${error.message}\n`;
            }
        }

        function checkCategoryDropdown() {
            const results = document.getElementById('category-results');
            results.innerHTML += '\nChecking Category Dropdown...\n';
            
            // Navigate to inventory page
            if (typeof app !== 'undefined' && typeof app.navigateToPage === 'function') {
                app.navigateToPage('inventory');
                setTimeout(() => {
                    const categoryFilter = document.getElementById('inventoryCategoryFilter');
                    if (categoryFilter) {
                        const options = categoryFilter.querySelectorAll('option');
                        const addCustomOption = Array.from(options).find(opt => opt.value === 'add_custom');
                        
                        results.innerHTML += `🔍 Found ${options.length} options in category dropdown\n`;
                        
                        if (addCustomOption) {
                            results.innerHTML += '✅ "Add Custom Category" option found in dropdown\n';
                        } else {
                            results.innerHTML += '❌ "Add Custom Category" option missing from dropdown\n';
                        }
                    } else {
                        results.innerHTML += '❌ Category filter dropdown not found\n';
                    }
                }, 1000);
            }
        }

        function testCustomPeriodReports() {
            const results = document.getElementById('report-results');
            results.innerHTML = 'Testing Custom Period Reports...\n';
            
            try {
                if (typeof app !== 'undefined') {
                    const functions = [
                        'generateCustomReport',
                        'generateComprehensiveCustomReportData',
                        'showCustomPeriodFullPageReport',
                        'generateTopSellingItemsTable',
                        'generateItemProfitAnalysisTable',
                        'generateExpenseAnalysisTable'
                    ];
                    let allExist = true;
                    
                    functions.forEach(func => {
                        if (typeof app[func] === 'function') {
                            results.innerHTML += `✅ ${func} function exists\n`;
                        } else {
                            results.innerHTML += `❌ ${func} function missing\n`;
                            allExist = false;
                        }
                    });
                    
                    if (allExist) {
                        results.innerHTML += '\n✅ All custom report functions are present\n';
                        results.innerHTML += 'Navigate to Reports page to test custom period generation\n';
                    }
                } else {
                    results.innerHTML += '❌ App object not found\n';
                }
            } catch (error) {
                results.innerHTML += `❌ Error: ${error.message}\n`;
            }
        }

        function checkReportSections() {
            const results = document.getElementById('report-results');
            results.innerHTML += '\nChecking Report Section Functions...\n';
            
            const sectionFunctions = [
                'generateKPIsTable',
                'generateMostUsedItemsTable', 
                'generateFinancialHealthTable',
                'generateSalesTrends',
                'generateCategoryBreakdown'
            ];
            
            sectionFunctions.forEach(func => {
                if (typeof app !== 'undefined' && typeof app[func] === 'function') {
                    results.innerHTML += `✅ ${func} function exists\n`;
                } else {
                    results.innerHTML += `❌ ${func} function missing\n`;
                }
            });
        }

        function checkSystemStatus() {
            const results = document.getElementById('system-results');
            results.innerHTML = 'Checking Overall System Status...\n';
            
            try {
                if (typeof app !== 'undefined') {
                    results.innerHTML += '✅ Main app object is available\n';
                    
                    // Check data access
                    const dataFunctions = ['getOrders', 'getInventoryItems', 'getUsageRecords', 'getExpenses'];
                    dataFunctions.forEach(func => {
                        if (typeof app[func] === 'function') {
                            try {
                                const data = app[func]();
                                results.innerHTML += `✅ ${func}: ${Array.isArray(data) ? data.length : 'available'} items\n`;
                            } catch (e) {
                                results.innerHTML += `⚠️ ${func}: function exists but error accessing data\n`;
                            }
                        } else {
                            results.innerHTML += `❌ ${func}: function missing\n`;
                        }
                    });
                    
                    results.innerHTML += '\n📊 System appears to be functional\n';
                } else {
                    results.innerHTML += '❌ Main app object not found - system may not be loaded\n';
                }
            } catch (error) {
                results.innerHTML += `❌ System Error: ${error.message}\n`;
            }
        }

        function runAllTests() {
            const results = document.getElementById('system-results');
            results.innerHTML = 'Running All Tests...\n\n';
            
            testOrderInventorySync();
            setTimeout(() => testUsageHistoryDelete(), 500);
            setTimeout(() => testCustomCategories(), 1000);
            setTimeout(() => testCustomPeriodReports(), 1500);
            setTimeout(() => {
                results.innerHTML += '\n🎉 All tests completed!\n';
                results.innerHTML += 'Check individual sections for detailed results.\n';
            }, 2000);
        }

        // Auto-run system check on load
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
