/**
 * Modern Toast Notification Manager
 * Handles toast notifications with animations and auto-dismiss
 */

console.log('🍞 Loading ToastManager class...');

class ToastManager {
    constructor() {
        this.toasts = new Map();
        this.toastCounter = 0;
        this.container = this.createContainer();
    }

    /**
     * Create toast container
     */
    createContainer() {
        let container = document.getElementById('toastContainer');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full';
            document.body.appendChild(container);
        }
        
        return container;
    }

    /**
     * Show toast notification
     */
    show(message, type = 'info', options = {}) {
        const toastId = `toast_${++this.toastCounter}`;
        
        const toast = new Toast(toastId, {
            message,
            type,
            duration: options.duration || this.getDefaultDuration(type),
            closable: options.closable !== false,
            action: options.action,
            onShow: options.onShow,
            onHide: options.onHide,
            onDestroy: options.onDestroy
        });

        this.toasts.set(toastId, toast);
        this.container.appendChild(toast.element);
        
        // Show with animation
        requestAnimationFrame(() => {
            toast.show();
        });

        return toast;
    }

    /**
     * Get default duration based on type
     */
    getDefaultDuration(type) {
        const durations = {
            success: 4000,
            info: 5000,
            warning: 6000,
            error: 8000
        };
        
        return durations[type] || 5000;
    }

    /**
     * Remove toast from manager
     */
    removeToast(toastId) {
        this.toasts.delete(toastId);
    }

    /**
     * Clear all toasts
     */
    clearAll() {
        this.toasts.forEach(toast => toast.hide());
    }

    /**
     * Success toast shorthand
     */
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    /**
     * Error toast shorthand
     */
    error(message, options = {}) {
        return this.show(message, 'error', options);
    }

    /**
     * Warning toast shorthand
     */
    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    /**
     * Info toast shorthand
     */
    info(message, options = {}) {
        return this.show(message, 'info', options);
    }
}

/**
 * Individual Toast Class
 */
class Toast {
    constructor(id, options) {
        this.id = id;
        this.options = options;
        this.element = null;
        this.isVisible = false;
        this.autoHideTimer = null;
        
        this.createElement();
    }

    /**
     * Create toast DOM element
     */
    createElement() {
        const typeStyles = {
            success: {
                bg: 'bg-green-50 border-green-200',
                icon: 'fas fa-check-circle text-green-500',
                text: 'text-green-800'
            },
            error: {
                bg: 'bg-red-50 border-red-200',
                icon: 'fas fa-times-circle text-red-500',
                text: 'text-red-800'
            },
            warning: {
                bg: 'bg-yellow-50 border-yellow-200',
                icon: 'fas fa-exclamation-triangle text-yellow-500',
                text: 'text-yellow-800'
            },
            info: {
                bg: 'bg-blue-50 border-blue-200',
                icon: 'fas fa-info-circle text-blue-500',
                text: 'text-blue-800'
            }
        };

        const style = typeStyles[this.options.type] || typeStyles.info;

        this.element = document.createElement('div');
        this.element.className = `toast-item ${style.bg} border rounded-lg shadow-lg p-4 transform translate-x-full transition-transform duration-300 ease-out`;
        
        this.element.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="${style.icon} text-lg"></i>
                </div>
                <div class="ml-3 flex-1">
                    <p class="${style.text} text-sm font-medium">${this.options.message}</p>
                    ${this.options.action ? `
                        <div class="mt-2">
                            <button onclick="${this.options.action.onClick}" 
                                    class="text-xs font-medium ${style.text} hover:underline">
                                ${this.options.action.text}
                            </button>
                        </div>
                    ` : ''}
                </div>
                ${this.options.closable ? `
                    <div class="ml-4 flex-shrink-0">
                        <button onclick="this.closest('.toast-item').toastInstance.hide()" 
                                class="${style.text} hover:opacity-75 transition-opacity">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                ` : ''}
            </div>
            
            ${this.options.duration > 0 ? `
                <div class="toast-progress mt-2 h-1 bg-black bg-opacity-10 rounded-full overflow-hidden">
                    <div class="toast-progress-bar h-full bg-current opacity-30 transition-all duration-100 ease-linear" 
                         style="width: 100%; transition-duration: ${this.options.duration}ms;"></div>
                </div>
            ` : ''}
        `;

        // Store reference for event handlers
        this.element.toastInstance = this;
    }

    /**
     * Show toast
     */
    show() {
        if (this.isVisible) return;

        // Trigger onShow callback
        if (this.options.onShow) {
            this.options.onShow(this);
        }

        // Show with slide animation
        this.element.classList.remove('translate-x-full');
        this.element.classList.add('translate-x-0');

        // Start progress bar animation
        if (this.options.duration > 0) {
            const progressBar = this.element.querySelector('.toast-progress-bar');
            if (progressBar) {
                requestAnimationFrame(() => {
                    progressBar.style.width = '0%';
                });
            }

            // Auto-hide after duration
            this.autoHideTimer = setTimeout(() => {
                this.hide();
            }, this.options.duration);
        }

        this.isVisible = true;
    }

    /**
     * Hide toast
     */
    hide() {
        if (!this.isVisible) return;

        // Clear auto-hide timer
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }

        // Trigger onHide callback
        if (this.options.onHide) {
            this.options.onHide(this);
        }

        // Hide with slide animation
        this.element.classList.remove('translate-x-0');
        this.element.classList.add('translate-x-full');

        // Remove after animation
        setTimeout(() => {
            this.destroy();
        }, 300);

        this.isVisible = false;
    }

    /**
     * Destroy toast
     */
    destroy() {
        // Trigger onDestroy callback
        if (this.options.onDestroy) {
            this.options.onDestroy(this);
        }

        // Remove from DOM
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }

        // Remove from manager
        if (window.modernApp && window.modernApp.toastManager) {
            window.modernApp.toastManager.removeToast(this.id);
        }
    }

    /**
     * Update toast message
     */
    updateMessage(newMessage) {
        const messageElement = this.element.querySelector('p');
        if (messageElement) {
            messageElement.textContent = newMessage;
        }
    }

    /**
     * Pause auto-hide (on hover)
     */
    pause() {
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }

        const progressBar = this.element.querySelector('.toast-progress-bar');
        if (progressBar) {
            progressBar.style.animationPlayState = 'paused';
        }
    }

    /**
     * Resume auto-hide
     */
    resume() {
        if (this.options.duration > 0 && this.isVisible) {
            const progressBar = this.element.querySelector('.toast-progress-bar');
            if (progressBar) {
                progressBar.style.animationPlayState = 'running';
            }

            // Calculate remaining time based on progress bar width
            const currentWidth = parseFloat(progressBar.style.width) || 100;
            const remainingTime = (currentWidth / 100) * this.options.duration;

            this.autoHideTimer = setTimeout(() => {
                this.hide();
            }, remainingTime);
        }
    }
}

/**
 * Toast utility functions
 */
class ToastUtils {
    /**
     * Show loading toast
     */
    static loading(message = 'Loading...') {
        return window.modernApp.toastManager.show(
            `<div class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent mr-2"></div>
                ${message}
            </div>`,
            'info',
            { duration: 0, closable: false }
        );
    }

    /**
     * Show progress toast
     */
    static progress(message, progress = 0) {
        return window.modernApp.toastManager.show(
            `<div>
                <div class="mb-2">${message}</div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                         style="width: ${progress}%"></div>
                </div>
            </div>`,
            'info',
            { duration: 0, closable: true }
        );
    }

    /**
     * Show action toast
     */
    static action(message, actionText, actionCallback, type = 'info') {
        return window.modernApp.toastManager.show(message, type, {
            action: {
                text: actionText,
                onClick: actionCallback
            },
            duration: 8000
        });
    }
}

// Add hover pause/resume functionality
document.addEventListener('mouseenter', (e) => {
    if (e.target.closest('.toast-item')) {
        const toast = e.target.closest('.toast-item').toastInstance;
        if (toast) {
            toast.pause();
        }
    }
}, true);

document.addEventListener('mouseleave', (e) => {
    if (e.target.closest('.toast-item')) {
        const toast = e.target.closest('.toast-item').toastInstance;
        if (toast) {
            toast.resume();
        }
    }
}, true);

// Export for global use
console.log('✅ Toast classes defined, exporting to window...');
window.ToastManager = ToastManager;
window.Toast = Toast;
window.ToastUtils = ToastUtils;
console.log('✅ Toast classes exported successfully');
