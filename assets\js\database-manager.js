/**
 * Zaiqa Restaurant Database Manager - Clean Architecture
 * Efficient Data Storage, Retrieval, and Management
 * Built from scratch with data integrity and validation
 */

class ZaiqaDatabaseManager {
    constructor() {
        this.version = '2.0.0';
        this.initialized = false;
        this.storagePrefix = 'zaiqa_';
        this.maxRetries = 3;
        
        // Initialize the database manager
        this.init();
    }

    /**
     * Initialize the database manager
     */
    init() {
        try {
            console.log('🗄️ Initializing Zaiqa Database Manager v' + this.version);
            
            // Validate storage availability
            this.validateStorage();
            
            // Initialize data schemas
            this.initializeSchemas();
            
            // Set up data integrity checks
            this.setupDataIntegrity();
            
            // Initialize indexes for faster queries
            this.initializeIndexes();
            
            this.initialized = true;
            console.log('✅ Database Manager initialized successfully');
            
        } catch (error) {
            console.error('❌ Database Manager initialization failed:', error);
            throw new Error('Failed to initialize Database Manager: ' + error.message);
        }
    }

    /**
     * Validate localStorage availability and functionality
     */
    validateStorage() {
        try {
            const testKey = this.storagePrefix + 'test';
            const testValue = 'test_value_' + Date.now();
            
            localStorage.setItem(testKey, testValue);
            const retrieved = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);
            
            if (retrieved !== testValue) {
                throw new Error('localStorage read/write test failed');
            }
            
        } catch (error) {
            throw new Error('localStorage is not available or functional: ' + error.message);
        }
    }

    /**
     * Initialize data schemas for all entities
     */
    initializeSchemas() {
        this.schemas = {
            orders: {
                id: { type: 'string', required: true },
                orderNumber: { type: 'string', required: true, unique: true },
                customerName: { type: 'string', required: false },
                customerCount: { type: 'number', required: true, min: 1 },
                serviceType: { type: 'string', required: true, enum: ['dine_in', 'takeaway', 'delivery'] },
                items: { type: 'array', required: true },
                totalAmount: { type: 'number', required: true, min: 0 },
                paymentMethod: { type: 'string', required: true },
                status: { type: 'string', required: true, enum: ['pending', 'completed', 'cancelled'] },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            menuItems: {
                id: { type: 'string', required: true },
                name: { type: 'string', required: true },
                category: { type: 'string', required: true },
                basePrice: { type: 'number', required: true, min: 0 },
                isAvailable: { type: 'boolean', required: true },
                description: { type: 'string', required: false },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            expenses: {
                id: { type: 'string', required: true },
                description: { type: 'string', required: true },
                amount: { type: 'number', required: true, min: 0 },
                category: { type: 'string', required: true },
                date: { type: 'string', required: true },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            staff: {
                id: { type: 'string', required: true },
                name: { type: 'string', required: true },
                position: { type: 'string', required: true },
                monthlySalary: { type: 'number', required: true, min: 0 },
                isActive: { type: 'boolean', required: true },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            inventory: {
                id: { type: 'string', required: true },
                name: { type: 'string', required: true },
                quantity: { type: 'number', required: true, min: 0 },
                unit: { type: 'string', required: true },
                lowStockThreshold: { type: 'number', required: true, min: 0 },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            udhars: {
                id: { type: 'string', required: true },
                customerName: { type: 'string', required: true },
                phone: { type: 'string', required: false },
                totalAmount: { type: 'number', required: true },
                paidAmount: { type: 'number', required: true, min: 0 },
                remainingAmount: { type: 'number', required: true },
                transactions: { type: 'array', required: true },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            },
            
            cashRegister: {
                id: { type: 'string', required: true },
                date: { type: 'string', required: true },
                morningBalance: { type: 'number', required: true, min: 0 },
                eveningBalance: { type: 'number', required: true, min: 0 },
                totalSales: { type: 'number', required: true, min: 0 },
                totalExpenses: { type: 'number', required: true, min: 0 },
                createdAt: { type: 'string', required: true },
                updatedAt: { type: 'string', required: true }
            }
        };
    }

    /**
     * Set up data integrity checks
     */
    setupDataIntegrity() {
        this.integrityChecks = {
            validateSchema: (data, schemaName) => {
                const schema = this.schemas[schemaName];
                if (!schema) {
                    throw new Error(`Schema not found: ${schemaName}`);
                }
                
                const errors = [];
                
                for (const [field, rules] of Object.entries(schema)) {
                    const value = data[field];
                    
                    // Check required fields
                    if (rules.required && (value === undefined || value === null || value === '')) {
                        errors.push(`${field} is required`);
                        continue;
                    }
                    
                    // Skip validation if field is not required and empty
                    if (!rules.required && (value === undefined || value === null || value === '')) {
                        continue;
                    }
                    
                    // Type validation
                    if (!this.validateType(value, rules.type)) {
                        errors.push(`${field} must be of type ${rules.type}`);
                        continue;
                    }
                    
                    // Additional validations
                    if (rules.min !== undefined && value < rules.min) {
                        errors.push(`${field} must be at least ${rules.min}`);
                    }
                    
                    if (rules.max !== undefined && value > rules.max) {
                        errors.push(`${field} must not exceed ${rules.max}`);
                    }
                    
                    if (rules.enum && !rules.enum.includes(value)) {
                        errors.push(`${field} must be one of: ${rules.enum.join(', ')}`);
                    }
                }
                
                return {
                    isValid: errors.length === 0,
                    errors: errors
                };
            }
        };
    }

    /**
     * Validate data type
     */
    validateType(value, expectedType) {
        switch (expectedType) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number' && Number.isFinite(value);
            case 'boolean':
                return typeof value === 'boolean';
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return false;
        }
    }

    /**
     * Initialize indexes for faster data retrieval
     */
    initializeIndexes() {
        this.indexes = {
            orders: ['orderNumber', 'createdAt', 'status'],
            menuItems: ['category', 'name'],
            expenses: ['date', 'category'],
            staff: ['isActive'],
            inventory: ['name'],
            udhars: ['customerName'],
            cashRegister: ['date']
        };
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Get storage key for a table
     */
    getStorageKey(tableName) {
        return this.storagePrefix + tableName;
    }

    /**
     * Safe JSON parse with error handling
     */
    safeJsonParse(jsonString, defaultValue = []) {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            console.warn('JSON parse error:', error);
            return defaultValue;
        }
    }

    /**
     * Safe JSON stringify with error handling
     */
    safeJsonStringify(data) {
        try {
            return JSON.stringify(data);
        } catch (error) {
            console.error('JSON stringify error:', error);
            throw new Error('Failed to serialize data');
        }
    }

    /**
     * Create a new record in the specified table
     */
    create(tableName, data) {
        try {
            // Validate schema
            const validation = this.integrityChecks.validateSchema(data, tableName);
            if (!validation.isValid) {
                throw new Error('Validation failed: ' + validation.errors.join(', '));
            }
            
            // Add metadata
            const record = {
                ...data,
                id: data.id || this.generateId(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            // Get existing data
            const storageKey = this.getStorageKey(tableName);
            const existingData = this.safeJsonParse(localStorage.getItem(storageKey), []);
            
            // Check for unique constraints
            if (this.schemas[tableName]) {
                for (const [field, rules] of Object.entries(this.schemas[tableName])) {
                    if (rules.unique && record[field]) {
                        const duplicate = existingData.find(item => item[field] === record[field]);
                        if (duplicate) {
                            throw new Error(`Duplicate value for unique field ${field}: ${record[field]}`);
                        }
                    }
                }
            }
            
            // Add record
            existingData.push(record);
            
            // Save to storage
            localStorage.setItem(storageKey, this.safeJsonStringify(existingData));
            
            console.log(`✅ Created record in ${tableName}:`, record.id);
            return record;
            
        } catch (error) {
            console.error(`❌ Failed to create record in ${tableName}:`, error);
            throw error;
        }
    }

    /**
     * Read records from the specified table
     */
    read(tableName, filters = {}) {
        try {
            const storageKey = this.getStorageKey(tableName);
            const data = this.safeJsonParse(localStorage.getItem(storageKey), []);
            
            // Apply filters
            if (Object.keys(filters).length === 0) {
                return data;
            }
            
            return data.filter(record => {
                return Object.entries(filters).every(([key, value]) => {
                    if (typeof value === 'function') {
                        return value(record[key]);
                    }
                    return record[key] === value;
                });
            });
            
        } catch (error) {
            console.error(`❌ Failed to read from ${tableName}:`, error);
            return [];
        }
    }

    /**
     * Update a record in the specified table
     */
    update(tableName, id, updates) {
        try {
            const storageKey = this.getStorageKey(tableName);
            const data = this.safeJsonParse(localStorage.getItem(storageKey), []);
            
            const recordIndex = data.findIndex(record => record.id === id);
            if (recordIndex === -1) {
                throw new Error(`Record not found with id: ${id}`);
            }
            
            // Merge updates
            const updatedRecord = {
                ...data[recordIndex],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            
            // Validate updated record
            const validation = this.integrityChecks.validateSchema(updatedRecord, tableName);
            if (!validation.isValid) {
                throw new Error('Validation failed: ' + validation.errors.join(', '));
            }
            
            // Update record
            data[recordIndex] = updatedRecord;
            
            // Save to storage
            localStorage.setItem(storageKey, this.safeJsonStringify(data));
            
            console.log(`✅ Updated record in ${tableName}:`, id);
            return updatedRecord;
            
        } catch (error) {
            console.error(`❌ Failed to update record in ${tableName}:`, error);
            throw error;
        }
    }

    /**
     * Delete a record from the specified table
     */
    delete(tableName, id) {
        try {
            const storageKey = this.getStorageKey(tableName);
            const data = this.safeJsonParse(localStorage.getItem(storageKey), []);
            
            const recordIndex = data.findIndex(record => record.id === id);
            if (recordIndex === -1) {
                throw new Error(`Record not found with id: ${id}`);
            }
            
            // Remove record
            const deletedRecord = data.splice(recordIndex, 1)[0];
            
            // Save to storage
            localStorage.setItem(storageKey, this.safeJsonStringify(data));
            
            console.log(`✅ Deleted record from ${tableName}:`, id);
            return deletedRecord;
            
        } catch (error) {
            console.error(`❌ Failed to delete record from ${tableName}:`, error);
            throw error;
        }
    }

    /**
     * Get database statistics
     */
    getStats() {
        const stats = {};
        
        for (const tableName of Object.keys(this.schemas)) {
            const data = this.read(tableName);
            stats[tableName] = {
                count: data.length,
                lastUpdated: data.length > 0 ? 
                    Math.max(...data.map(record => new Date(record.updatedAt).getTime())) : null
            };
        }
        
        return stats;
    }

    /**
     * Check if the database manager is properly initialized
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * Get database manager status
     */
    getStatus() {
        return {
            version: this.version,
            initialized: this.initialized,
            storagePrefix: this.storagePrefix,
            availableSchemas: Object.keys(this.schemas),
            stats: this.getStats(),
            timestamp: new Date().toISOString()
        };
    }
}

// Export the database manager for global use
window.ZaiqaDatabaseManager = ZaiqaDatabaseManager;
