/**
 * Cash Management Integration - Connect Enhanced Features
 * Integrates new cash management with existing system components
 */

class ZaiqaCashIntegration {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize cash management integration
     */
    init() {
        try {
            console.log('🔗 Initializing Cash Management Integration v' + this.version);
            
            // Wait for all systems to be ready
            this.waitForSystems().then(() => {
                this.setupIntegration();
                this.initialized = true;
                console.log('✅ Cash Management Integration completed');
            });
            
        } catch (error) {
            console.error('❌ Cash Management Integration failed:', error);
        }
    }

    /**
     * Wait for all required systems to be ready
     */
    async waitForSystems() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 20;
            
            const checkSystems = () => {
                attempts++;
                
                const systemsReady = 
                    typeof window.app !== 'undefined' &&
                    typeof window.cashManager !== 'undefined' &&
                    typeof window.aiCleanup !== 'undefined';
                
                if (systemsReady) {
                    console.log('✅ All systems ready for integration');
                    resolve();
                } else if (attempts < maxAttempts) {
                    setTimeout(checkSystems, 500);
                } else {
                    console.warn('⚠️ Some systems not ready, proceeding with partial integration');
                    resolve();
                }
            };
            
            checkSystems();
        });
    }

    /**
     * Set up comprehensive integration
     */
    setupIntegration() {
        try {
            // Integrate cash management with existing functions
            this.integrateCashFunctions();
            
            // Fix cash modal display
            this.fixCashModalDisplay();
            
            // Integrate with reports system
            this.integrateWithReports();
            
            // Set up automatic data synchronization
            this.setupDataSynchronization();
            
            // Clean up existing issues
            this.performDataCleanup();
            
        } catch (error) {
            console.error('❌ Failed to setup integration:', error);
        }
    }

    /**
     * Integrate cash management with existing functions
     */
    integrateCashFunctions() {
        try {
            if (!window.app) return;

            // Override ONLY the cash in hand modal function - use original design with enhanced functionality
            if (typeof window.app.showCashInHandModal === 'function') {
                window.app.originalShowCashInHandModal = window.app.showCashInHandModal;

                window.app.showCashInHandModal = () => {
                    // Remove any existing modals first
                    const existingModals = document.querySelectorAll('.modal-overlay');
                    existingModals.forEach(modal => modal.remove());

                    // Show only the original style modal with enhanced functionality
                    this.showOriginalEnhancedCashModal();
                };
            }

            // Disable any other cash modal functions that might create duplicates
            if (window.cashManager && typeof window.cashManager.showEnhancedCashModal === 'function') {
                window.cashManager.originalShowEnhancedCashModal = window.cashManager.showEnhancedCashModal;
                window.cashManager.showEnhancedCashModal = () => {
                    // Redirect to our integrated function
                    window.app.showCashInHandModal();
                };
            }

            console.log('✅ Cash functions integrated - single modal ensured');

        } catch (error) {
            console.error('❌ Failed to integrate cash functions:', error);
        }
    }

    /**
     * Show original enhanced cash modal with simple design
     */
    showOriginalEnhancedCashModal() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Cash in Hand</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>

                    <div class="modal-body">
                        <div class="cash-display">
                            <h3>Current Cash: PKR ${currentCash.toLocaleString()}</h3>
                        </div>

                        <div class="cash-sections">
                            <div class="cash-section">
                                <h4>💰 Cash Receipt</h4>
                                <div class="cash-form">
                                    <input type="number" id="inflowAmount" placeholder="Amount" step="0.01" min="0">
                                    <input type="text" id="inflowDescription" placeholder="Description (e.g., Cash Sale, Additional Revenue)">
                                    <button onclick="window.cashIntegration.addCashReceipt()" class="btn btn-success">
                                        Add Cash Receipt
                                    </button>
                                </div>
                                <small class="form-help">Cash receipts will be added to today's sales and revenue</small>
                            </div>

                            <div class="cash-section">
                                <h4>💸 Outflow</h4>
                                <div class="cash-form">
                                    <input type="number" id="outflowAmount" placeholder="Amount" step="0.01">
                                    <input type="text" id="outflowDescription" placeholder="Description (e.g., Expense, Payment)">
                                    <button onclick="window.cashIntegration.addCashExpense()" class="btn btn-danger">
                                        Add Outflow
                                    </button>
                                </div>
                                <small class="form-help">Outflows will be added to expenses</small>
                            </div>
                        </div>

                        <div class="auto-expense-section">
                            <h4>⚙️ Auto Expenses</h4>
                            <button onclick="window.cashIntegration.deductAutoExpenses()" class="btn btn-warning">
                                <i class="fas fa-cog"></i> Deduct Auto Expenses
                            </button>
                        </div>

                        <div class="recent-transactions">
                            <h4>Recent Transactions</h4>
                            <div id="cashTransactionsList" class="transactions-list">
                                <div class="loading">Loading...</div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Close
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Load transactions after modal is displayed
            setTimeout(() => {
                this.loadCashTransactions();
            }, 100);

        } catch (error) {
            console.error('❌ Failed to show original enhanced cash modal:', error);
        }
    }

    /**
     * Add cash receipt (revenue)
     */
    addCashReceipt() {
        try {
            const amount = parseFloat(document.getElementById('inflowAmount')?.value || 0);
            const description = document.getElementById('inflowDescription')?.value?.trim() || 'Cash Receipt';

            if (amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            // Process cash inflow with proper revenue integration
            const success = this.processCashInflow(amount, description);

            if (success) {
                // Clear form
                document.getElementById('inflowAmount').value = '';
                document.getElementById('inflowDescription').value = '';

                // Show success message
                if (window.app && typeof window.app.showNotification === 'function') {
                    window.app.showNotification(`Cash receipt of PKR ${amount.toLocaleString()} added to revenue`, 'success');
                }

                // Refresh display
                this.refreshCashModal();
                this.loadCashTransactions();

                // Force refresh all financial displays
                this.forceRefreshAllDisplays();
            }

        } catch (error) {
            console.error('❌ Failed to add cash receipt:', error);
            alert('Failed to add cash receipt. Please try again.');
        }
    }

    /**
     * Process cash inflow with proper revenue integration
     */
    processCashInflow(amount, description) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            // 1. Update cash in hand
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const newCash = currentCash + amount;
            localStorage.setItem('currentCashInHand', newCash.toString());
            localStorage.setItem('lastCashUpdate', timestamp);

            // 2. Create revenue order entry
            const revenueOrder = {
                id: 'cash_revenue_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                order_number: 'CASH-' + Date.now(),
                customer_name: 'Cash Receipt',
                customer_count: 1,
                service_type: 'cash_receipt',
                items: [{
                    name: description,
                    price: amount,
                    quantity: 1
                }],
                total_amount: amount,
                payment_method: 'cash',
                status: 'completed',
                created_at: timestamp,
                createdAt: timestamp,
                cash_transaction_type: 'inflow'
            };

            // Add to orders for revenue tracking
            const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
            orders.push(revenueOrder);
            localStorage.setItem('restaurantOrders', JSON.stringify(orders));

            // 3. Create cash transaction record
            const cashTransaction = {
                id: 'cash_in_' + Date.now(),
                amount: amount,
                description: description,
                category: 'Cash Receipt',
                date: today,
                timestamp: timestamp,
                type: 'inflow'
            };

            // Store cash transaction
            const cashTransactions = JSON.parse(localStorage.getItem('cashTransactions') || '[]');
            cashTransactions.push(cashTransaction);
            localStorage.setItem('cashTransactions', JSON.stringify(cashTransactions));

            // 4. Update today's cash register
            this.updateTodayCashRegister(amount, 'inflow');

            console.log(`✅ Cash inflow processed: PKR ${amount} added to revenue and cash`);

            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(`Cash receipt of PKR ${amount.toLocaleString()} added to revenue`, 'success');
            }

            return true;

        } catch (error) {
            console.error('❌ Failed to process cash inflow:', error);
            return false;
        }
    }

    /**
     * Add cash expense
     */
    addCashExpense() {
        try {
            const amount = parseFloat(document.getElementById('outflowAmount')?.value || 0);
            const description = document.getElementById('outflowDescription')?.value?.trim() || 'Cash Expense';

            if (amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            // Process cash outflow with proper expense integration
            const success = this.processCashOutflow(amount, description);

            if (success) {
                // Clear form
                document.getElementById('outflowAmount').value = '';
                document.getElementById('outflowDescription').value = '';

                // Show success message
                if (window.app && typeof window.app.showNotification === 'function') {
                    window.app.showNotification(`Expense of PKR ${amount.toLocaleString()} added and deducted from cash`, 'success');
                }

                // Refresh display
                this.refreshCashModal();
                this.loadCashTransactions();

                // Force refresh all financial displays
                this.forceRefreshAllDisplays();
            }

        } catch (error) {
            console.error('❌ Failed to add cash expense:', error);
            alert('Failed to add cash expense. Please try again.');
        }
    }

    /**
     * Deduct auto expenses from cash
     */
    deductAutoExpenses() {
        try {
            const config = JSON.parse(localStorage.getItem('autoExpenseConfig') || '{}');
            const enabledExpenses = config.autoExpenses ?
                config.autoExpenses.filter(exp => exp.enabled && exp.amount > 0) : [];

            if (enabledExpenses.length === 0) {
                alert('No auto expenses configured');
                return;
            }

            const totalAmount = enabledExpenses.reduce((sum, exp) => sum + exp.amount, 0);
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');

            if (currentCash < totalAmount) {
                alert(`Insufficient cash. Need PKR ${totalAmount.toLocaleString()}, have PKR ${currentCash.toLocaleString()}`);
                return;
            }

            const confirmed = confirm(`Deduct PKR ${totalAmount.toLocaleString()} for ${enabledExpenses.length} auto expenses?`);
            if (!confirmed) return;

            // Process each auto expense
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            enabledExpenses.forEach(expense => {
                const expenseEntry = {
                    id: 'auto_exp_' + expense.id + '_' + Date.now(),
                    description: `${expense.name} (Auto Deduction)`,
                    amount: expense.amount,
                    category: 'Auto Expenses',
                    date: today,
                    created_at: timestamp,
                    type: 'auto_expense_deduction'
                };

                // Add to expenses
                const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                expenses.push(expenseEntry);
                localStorage.setItem('expenses', JSON.stringify(expenses));
            });

            // Deduct from cash
            const newCash = currentCash - totalAmount;
            localStorage.setItem('currentCashInHand', newCash.toString());

            // Add cash transaction
            this.addCashTransaction('outflow', totalAmount, 'Auto Expenses Deduction');

            // Show success message
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(`Auto expenses of PKR ${totalAmount.toLocaleString()} deducted`, 'success');
            }

            // Refresh modal and dashboard
            this.refreshCashModal();
            this.forceRefreshAllDisplays();

        } catch (error) {
            console.error('❌ Failed to deduct auto expenses:', error);
            alert('Failed to deduct auto expenses');
        }
    }

    /**
     * Process cash outflow with proper expense integration
     */
    processCashOutflow(amount, description) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            // 1. Update cash in hand
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const newCash = currentCash - amount;
            localStorage.setItem('currentCashInHand', newCash.toString());
            localStorage.setItem('lastCashUpdate', timestamp);

            // 2. Create expense entry
            const expenseEntry = {
                id: 'cash_expense_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                description: description,
                amount: amount,
                category: 'Cash Deduction',
                date: today,
                created_at: timestamp,
                createdAt: timestamp,
                type: 'cash_outflow'
            };

            // Add to expenses
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            expenses.push(expenseEntry);
            localStorage.setItem('expenses', JSON.stringify(expenses));

            // 3. Create cash transaction record
            const cashTransaction = {
                id: 'cash_out_' + Date.now(),
                amount: -amount,
                description: description,
                category: 'Cash Deduction',
                date: today,
                timestamp: timestamp,
                type: 'outflow'
            };

            // Store cash transaction
            const cashTransactions = JSON.parse(localStorage.getItem('cashTransactions') || '[]');
            cashTransactions.push(cashTransaction);
            localStorage.setItem('cashTransactions', JSON.stringify(cashTransactions));

            // 4. Update today's cash register
            this.updateTodayCashRegister(-amount, 'outflow');

            console.log(`✅ Cash outflow processed: PKR ${amount} added to expenses and deducted from cash`);

            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(`Cash deduction of PKR ${amount.toLocaleString()} added to expenses`, 'info');
            }

            return true;

        } catch (error) {
            console.error('❌ Failed to process cash outflow:', error);
            return false;
        }
    }

    /**
     * Update today's cash register entry
     */
    updateTodayCashRegister(amount, type) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const cashRegister = JSON.parse(localStorage.getItem('cashRegister') || '[]');

            let todayEntry = cashRegister.find(entry => entry.date === today);

            if (!todayEntry) {
                todayEntry = {
                    date: today,
                    morningBalance: 0,
                    eveningBalance: 0,
                    totalSales: 0,
                    totalExpenses: 0,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                cashRegister.push(todayEntry);
            }

            // Update based on transaction type
            if (type === 'inflow') {
                todayEntry.totalSales = (todayEntry.totalSales || 0) + amount;
            } else {
                todayEntry.totalExpenses = (todayEntry.totalExpenses || 0) + Math.abs(amount);
            }

            todayEntry.eveningBalance = (todayEntry.morningBalance || 0) +
                                       (todayEntry.totalSales || 0) -
                                       (todayEntry.totalExpenses || 0);
            todayEntry.updatedAt = new Date().toISOString();

            localStorage.setItem('cashRegister', JSON.stringify(cashRegister));

        } catch (error) {
            console.error('❌ Failed to update cash register:', error);
        }
    }

    /**
     * Force refresh all financial displays
     */
    forceRefreshAllDisplays() {
        try {
            // Refresh dashboard
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                setTimeout(() => {
                    window.app.updateDashboardStats();
                }, 100);
            }

            // Refresh reports page if visible
            const reportsPage = document.getElementById('reportsPage');
            if (reportsPage && reportsPage.style.display !== 'none') {
                if (window.zaiqaReports && typeof window.zaiqaReports.render === 'function') {
                    setTimeout(() => {
                        window.zaiqaReports.render(reportsPage);
                    }, 200);
                }
            }

            // Trigger data sync
            setTimeout(() => {
                this.syncFinancialData();
            }, 300);

        } catch (error) {
            console.error('❌ Failed to force refresh displays:', error);
        }
    }

    /**
     * Refresh original cash modal display
     */
    refreshOriginalCashModal() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const cashDisplayElement = document.querySelector('.cash-display h3');

            if (cashDisplayElement) {
                cashDisplayElement.textContent = `Current Cash: PKR ${currentCash.toLocaleString()}`;
            }

        } catch (error) {
            console.error('❌ Failed to refresh cash modal:', error);
        }
    }

    /**
     * Process cash transaction from modal
     */
    processCashTransaction() {
        try {
            const amount = parseFloat(document.getElementById('cashAmount')?.value || 0);
            const description = document.getElementById('cashDescription')?.value || '';
            const category = document.getElementById('cashCategory')?.value || 'Cash Transaction';

            if (amount === 0) {
                alert('Please enter a valid amount');
                return;
            }

            if (!description.trim()) {
                alert('Please enter a description');
                return;
            }

            // Process through enhanced cash manager
            if (window.cashManager && window.cashManager.isInitialized()) {
                const success = window.cashManager.processEnhancedCashTransaction(amount, description, category);
                
                if (success) {
                    // Clear form
                    document.getElementById('cashAmount').value = '';
                    document.getElementById('cashDescription').value = '';
                    
                    // Refresh display
                    this.refreshCashModal();
                    this.loadCashTransactions();
                }
            }

        } catch (error) {
            console.error('❌ Failed to process cash transaction:', error);
            alert('Failed to process transaction. Please try again.');
        }
    }

    /**
     * Load and display cash transactions
     */
    loadCashTransactions() {
        try {
            const container = document.getElementById('cashTransactionsList');
            if (!container) return;

            if (window.app && typeof window.app.displayCashTransactions === 'function') {
                window.app.displayCashTransactions('cashTransactionsList');
            } else {
                container.innerHTML = '<div class="error">Unable to load transactions</div>';
            }

        } catch (error) {
            console.error('❌ Failed to load cash transactions:', error);
        }
    }

    /**
     * Refresh cash modal display
     */
    refreshCashModal() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const cashAmountElement = document.querySelector('.cash-amount');
            const lastUpdatedElement = document.querySelector('.last-updated');
            
            if (cashAmountElement) {
                cashAmountElement.textContent = `PKR ${currentCash.toLocaleString()}`;
            }
            
            if (lastUpdatedElement) {
                lastUpdatedElement.textContent = `Last updated: ${this.getLastUpdateTime()}`;
            }

        } catch (error) {
            console.error('❌ Failed to refresh cash modal:', error);
        }
    }

    /**
     * Get last update time
     */
    getLastUpdateTime() {
        try {
            const lastUpdate = localStorage.getItem('lastCashUpdate');
            if (lastUpdate) {
                const date = new Date(lastUpdate);
                return date.toLocaleString();
            }
            return 'Never';
        } catch (error) {
            return 'Unknown';
        }
    }

    /**
     * Fix cash modal display issues
     */
    fixCashModalDisplay() {
        try {
            // Add CSS for enhanced cash modal
            const style = document.createElement('style');
            style.textContent = `
                .cash-modal .modal-content {
                    max-width: 800px;
                    width: 90%;
                }
                
                .cash-summary {
                    background: linear-gradient(135deg, #10b981, #059669);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    margin-bottom: 25px;
                    text-align: center;
                }
                
                .current-cash h3 {
                    margin: 0 0 10px 0;
                    font-size: 16px;
                    opacity: 0.9;
                }
                
                .cash-amount {
                    font-size: 32px;
                    font-weight: 700;
                    margin-bottom: 5px;
                }
                
                .last-updated {
                    font-size: 12px;
                    opacity: 0.8;
                }
                
                .cash-actions {
                    margin-bottom: 25px;
                }
                
                .cash-actions h3 {
                    margin: 0 0 15px 0;
                    color: var(--gray-900);
                    font-size: 16px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid var(--gray-200);
                }
                
                .recent-transactions h3 {
                    margin: 0 0 15px 0;
                    color: var(--gray-900);
                    font-size: 16px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid var(--gray-200);
                }
            `;
            document.head.appendChild(style);

        } catch (error) {
            console.error('❌ Failed to fix cash modal display:', error);
        }
    }

    /**
     * Integrate with reports system
     */
    integrateWithReports() {
        try {
            if (window.zaiqaReports && typeof window.zaiqaReports.addCashIntegration !== 'function') {
                window.zaiqaReports.addCashIntegration = function() {
                    // This will be called by reports to get enhanced cash data
                    return {
                        cashTransactions: window.cashManager ? window.cashManager.getRecentCashTransactions(50) : [],
                        currentCash: parseFloat(localStorage.getItem('currentCashInHand') || '0'),
                        lastUpdate: localStorage.getItem('lastCashUpdate')
                    };
                };
            }

        } catch (error) {
            console.error('❌ Failed to integrate with reports:', error);
        }
    }

    /**
     * Set up automatic data synchronization
     */
    setupDataSynchronization() {
        try {
            // Sync data every 30 seconds
            setInterval(() => {
                this.syncFinancialData();
            }, 30000);

        } catch (error) {
            console.error('❌ Failed to setup data synchronization:', error);
        }
    }

    /**
     * Sync financial data across all systems
     */
    syncFinancialData() {
        try {
            // Update dashboard if visible
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                window.app.updateDashboardStats();
            }

            // Update reports if visible
            const reportsPage = document.getElementById('reportsPage');
            if (reportsPage && reportsPage.style.display !== 'none') {
                if (window.zaiqaReports && typeof window.zaiqaReports.refreshData === 'function') {
                    window.zaiqaReports.refreshData();
                }
            }

        } catch (error) {
            console.error('❌ Failed to sync financial data:', error);
        }
    }

    /**
     * Perform data cleanup
     */
    performDataCleanup() {
        try {
            // Clean up phantom expenses
            if (window.aiCleanup && typeof window.aiCleanup.cleanupPhantomExpenses === 'function') {
                window.aiCleanup.cleanupPhantomExpenses();
            }

            // Validate cash in hand
            this.validateCashInHand();

        } catch (error) {
            console.error('❌ Failed to perform data cleanup:', error);
        }
    }

    /**
     * Validate cash in hand calculations
     */
    validateCashInHand() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            
            if (isNaN(currentCash)) {
                localStorage.setItem('currentCashInHand', '0');
                console.log('✅ Reset invalid cash in hand value');
            }

        } catch (error) {
            console.error('❌ Failed to validate cash in hand:', error);
        }
    }

    /**
     * Export cash report
     */
    exportCashReport() {
        try {
            const cashData = {
                currentCash: parseFloat(localStorage.getItem('currentCashInHand') || '0'),
                transactions: window.cashManager ? window.cashManager.getRecentCashTransactions(100) : [],
                exportDate: new Date().toISOString(),
                summary: this.generateCashSummary()
            };

            const blob = new Blob([JSON.stringify(cashData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-cash-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            if (window.cashManager && typeof window.cashManager.showNotification === 'function') {
                window.cashManager.showNotification('Cash report exported successfully', 'success');
            }

        } catch (error) {
            console.error('❌ Failed to export cash report:', error);
        }
    }

    /**
     * Generate cash summary
     */
    generateCashSummary() {
        try {
            const transactions = window.cashManager ? window.cashManager.getRecentCashTransactions(30) : [];
            
            let totalReceipts = 0;
            let totalDeductions = 0;
            
            transactions.forEach(transaction => {
                if (transaction.amount > 0) {
                    totalReceipts += transaction.amount;
                } else {
                    totalDeductions += Math.abs(transaction.amount);
                }
            });

            return {
                totalReceipts,
                totalDeductions,
                netChange: totalReceipts - totalDeductions,
                transactionCount: transactions.length
            };

        } catch (error) {
            console.error('❌ Failed to generate cash summary:', error);
            return { totalReceipts: 0, totalDeductions: 0, netChange: 0, transactionCount: 0 };
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize cash integration - Enhanced original modal only
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.cashIntegration = new ZaiqaCashIntegration();
        console.log('✅ Enhanced original cash modal system initialized');
    }, 2500);
});

// Export for global use
window.ZaiqaCashIntegration = ZaiqaCashIntegration;
