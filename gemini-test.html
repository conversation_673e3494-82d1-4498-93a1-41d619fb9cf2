<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #4285f4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #3367d6;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Gemini API Test Tool</h1>
        <p>Test your Google Gemini API key to diagnose connection issues.</p>
        
        <div class="input-group">
            <label for="apiKey">Gemini API Key:</label>
            <input type="password" id="apiKey" placeholder="Enter your Gemini API key (AIza...)" value="AIzaSyCFryKebYzTAoABS2dF47HONaiovZM8CSU">
        </div>
        
        <div class="input-group">
            <label for="testPrompt">Test Prompt:</label>
            <input type="text" id="testPrompt" placeholder="Enter test prompt" value="Say hello in English">
        </div>
        
        <button onclick="testGeminiAPI()" id="testBtn">Test API</button>
        <button onclick="clearLogs()">Clear Logs</button>
        
        <div id="result" class="result logs" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.style.display = 'block';
            resultDiv.className = `result logs ${type}`;
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function clearLogs() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '';
            resultDiv.style.display = 'none';
        }

        async function testGeminiAPI() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const prompt = document.getElementById('testPrompt').value.trim() || "Say hello";
            const testBtn = document.getElementById('testBtn');
            
            if (!apiKey) {
                log('❌ Please enter an API key', 'error');
                return;
            }
            
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';
            clearLogs();
            
            log('🧪 Starting Gemini API test...');
            log(`📝 API Key: ${apiKey.substring(0, 20)}...`);
            log(`💬 Prompt: "${prompt}"`);
            
            try {
                const requestBody = {
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        maxOutputTokens: 100,
                        temperature: 0.7
                    }
                };
                
                log('📤 Sending request to Gemini API...');
                log(`🔗 URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent`);
                log(`📦 Request body: ${JSON.stringify(requestBody, null, 2)}`);
                
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                log(`📥 Response status: ${response.status} ${response.statusText}`);
                
                const responseText = await response.text();
                log(`📄 Raw response: ${responseText}`);
                
                if (!response.ok) {
                    log('❌ API request failed', 'error');
                    
                    try {
                        const errorData = JSON.parse(responseText);
                        if (errorData.error) {
                            log(`❌ Error code: ${errorData.error.code}`, 'error');
                            log(`❌ Error message: ${errorData.error.message}`, 'error');
                            log(`❌ Error status: ${errorData.error.status}`, 'error');
                            
                            // Provide specific guidance based on error
                            if (errorData.error.code === 400) {
                                log('💡 Suggestion: Check your API key format and ensure it starts with "AIza"', 'info');
                            } else if (errorData.error.code === 403) {
                                log('💡 Suggestion: Enable the Generative Language API in Google Cloud Console', 'info');
                                log('💡 Link: https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com', 'info');
                            } else if (errorData.error.code === 429) {
                                log('💡 Suggestion: You have exceeded the rate limit. Wait a few minutes and try again.', 'info');
                            }
                        }
                    } catch (e) {
                        log('❌ Could not parse error response', 'error');
                    }
                    return;
                }
                
                try {
                    const data = JSON.parse(responseText);
                    log('✅ Successfully parsed response', 'success');
                    
                    if (data.candidates && data.candidates.length > 0) {
                        const generatedText = data.candidates[0].content.parts[0].text;
                        log(`✅ Generated text: "${generatedText}"`, 'success');
                        log('🎉 Gemini API test SUCCESSFUL!', 'success');
                    } else {
                        log('❌ No candidates in response', 'error');
                        log(`📊 Full response: ${JSON.stringify(data, null, 2)}`);
                    }
                } catch (e) {
                    log('❌ Could not parse response as JSON', 'error');
                }
                
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
                log('💡 Check your internet connection and try again', 'info');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = 'Test API';
            }
        }

        // Auto-test on page load if API key is present
        window.onload = function() {
            const apiKey = document.getElementById('apiKey').value;
            if (apiKey && apiKey.startsWith('AIza')) {
                log('🚀 Auto-testing API key on page load...');
                setTimeout(testGeminiAPI, 1000);
            }
        };
    </script>
</body>
</html>
