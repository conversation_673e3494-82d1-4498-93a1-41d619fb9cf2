/**
 * Cash Management Integration - Connect Enhanced Features
 * Integrates new cash management with existing system components
 */

class ZaiqaCashIntegration {
    constructor() {
        this.version = '1.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize cash management integration
     */
    init() {
        try {
            console.log('🔗 Initializing Cash Management Integration v' + this.version);
            
            // Wait for all systems to be ready
            this.waitForSystems().then(() => {
                this.setupIntegration();
                this.initialized = true;
                console.log('✅ Cash Management Integration completed');
            });
            
        } catch (error) {
            console.error('❌ Cash Management Integration failed:', error);
        }
    }

    /**
     * Wait for all required systems to be ready
     */
    async waitForSystems() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 20;
            
            const checkSystems = () => {
                attempts++;
                
                const systemsReady = 
                    typeof window.app !== 'undefined' &&
                    typeof window.cashManager !== 'undefined' &&
                    typeof window.aiCleanup !== 'undefined';
                
                if (systemsReady) {
                    console.log('✅ All systems ready for integration');
                    resolve();
                } else if (attempts < maxAttempts) {
                    setTimeout(checkSystems, 500);
                } else {
                    console.warn('⚠️ Some systems not ready, proceeding with partial integration');
                    resolve();
                }
            };
            
            checkSystems();
        });
    }

    /**
     * Set up comprehensive integration
     */
    setupIntegration() {
        try {
            // Integrate cash management with existing functions
            this.integrateCashFunctions();
            
            // Fix cash modal display
            this.fixCashModalDisplay();
            
            // Integrate with reports system
            this.integrateWithReports();
            
            // Set up automatic data synchronization
            this.setupDataSynchronization();
            
            // Clean up existing issues
            this.performDataCleanup();
            
        } catch (error) {
            console.error('❌ Failed to setup integration:', error);
        }
    }

    /**
     * Integrate cash management with existing functions
     */
    integrateCashFunctions() {
        try {
            if (!window.app) return;

            // Override cash in hand modal function
            if (typeof window.app.showCashInHandModal === 'function') {
                window.app.originalShowCashInHandModal = window.app.showCashInHandModal;
                
                window.app.showCashInHandModal = () => {
                    this.showEnhancedCashModal();
                };
            }

            // Override add cash receipt function
            if (typeof window.app.addCashReceipt === 'function') {
                window.app.originalAddCashReceipt = window.app.addCashReceipt;
                
                window.app.addCashReceipt = (amount, description, category) => {
                    if (window.cashManager && window.cashManager.isInitialized()) {
                        return window.cashManager.processEnhancedCashTransaction(amount, description, category);
                    } else {
                        return window.app.originalAddCashReceipt(amount, description, category);
                    }
                };
            }

            console.log('✅ Cash functions integrated');

        } catch (error) {
            console.error('❌ Failed to integrate cash functions:', error);
        }
    }

    /**
     * Show enhanced cash modal with improved transaction display
     */
    showEnhancedCashModal() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            
            const modal = document.createElement('div');
            modal.className = 'modal-overlay cash-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-wallet"></i> Cash in Hand Management</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="cash-summary">
                            <div class="current-cash">
                                <h3>Current Cash in Hand</h3>
                                <div class="cash-amount">PKR ${currentCash.toLocaleString()}</div>
                                <div class="last-updated">Last updated: ${this.getLastUpdateTime()}</div>
                            </div>
                        </div>
                        
                        <div class="cash-actions">
                            <h3>Add Cash Transaction</h3>
                            <div class="cash-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Amount (PKR):</label>
                                        <input type="number" id="cashAmount" placeholder="Enter amount (+ for receipt, - for deduction)" step="0.01">
                                    </div>
                                    <div class="form-group">
                                        <label>Description:</label>
                                        <input type="text" id="cashDescription" placeholder="Enter description">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Category:</label>
                                        <select id="cashCategory">
                                            <option value="Cash Receipt">Cash Receipt</option>
                                            <option value="Cash Deduction">Cash Deduction</option>
                                            <option value="Owner Withdrawal">Owner Withdrawal</option>
                                            <option value="Expense Payment">Expense Payment</option>
                                            <option value="Revenue Addition">Revenue Addition</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <button onclick="window.cashIntegration.processCashTransaction()" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Add Transaction
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recent-transactions">
                            <h3>Recent Cash Transactions</h3>
                            <div id="cashTransactionsList">
                                <div class="loading">Loading transactions...</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Close
                        </button>
                        <button onclick="window.cashIntegration.exportCashReport()" class="btn btn-info">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Load transactions after modal is displayed
            setTimeout(() => {
                this.loadCashTransactions();
            }, 100);

        } catch (error) {
            console.error('❌ Failed to show enhanced cash modal:', error);
        }
    }

    /**
     * Process cash transaction from modal
     */
    processCashTransaction() {
        try {
            const amount = parseFloat(document.getElementById('cashAmount')?.value || 0);
            const description = document.getElementById('cashDescription')?.value || '';
            const category = document.getElementById('cashCategory')?.value || 'Cash Transaction';

            if (amount === 0) {
                alert('Please enter a valid amount');
                return;
            }

            if (!description.trim()) {
                alert('Please enter a description');
                return;
            }

            // Process through enhanced cash manager
            if (window.cashManager && window.cashManager.isInitialized()) {
                const success = window.cashManager.processEnhancedCashTransaction(amount, description, category);
                
                if (success) {
                    // Clear form
                    document.getElementById('cashAmount').value = '';
                    document.getElementById('cashDescription').value = '';
                    
                    // Refresh display
                    this.refreshCashModal();
                    this.loadCashTransactions();
                }
            }

        } catch (error) {
            console.error('❌ Failed to process cash transaction:', error);
            alert('Failed to process transaction. Please try again.');
        }
    }

    /**
     * Load and display cash transactions
     */
    loadCashTransactions() {
        try {
            const container = document.getElementById('cashTransactionsList');
            if (!container) return;

            if (window.app && typeof window.app.displayCashTransactions === 'function') {
                window.app.displayCashTransactions('cashTransactionsList');
            } else {
                container.innerHTML = '<div class="error">Unable to load transactions</div>';
            }

        } catch (error) {
            console.error('❌ Failed to load cash transactions:', error);
        }
    }

    /**
     * Refresh cash modal display
     */
    refreshCashModal() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const cashAmountElement = document.querySelector('.cash-amount');
            const lastUpdatedElement = document.querySelector('.last-updated');
            
            if (cashAmountElement) {
                cashAmountElement.textContent = `PKR ${currentCash.toLocaleString()}`;
            }
            
            if (lastUpdatedElement) {
                lastUpdatedElement.textContent = `Last updated: ${this.getLastUpdateTime()}`;
            }

        } catch (error) {
            console.error('❌ Failed to refresh cash modal:', error);
        }
    }

    /**
     * Get last update time
     */
    getLastUpdateTime() {
        try {
            const lastUpdate = localStorage.getItem('lastCashUpdate');
            if (lastUpdate) {
                const date = new Date(lastUpdate);
                return date.toLocaleString();
            }
            return 'Never';
        } catch (error) {
            return 'Unknown';
        }
    }

    /**
     * Fix cash modal display issues
     */
    fixCashModalDisplay() {
        try {
            // Add CSS for enhanced cash modal
            const style = document.createElement('style');
            style.textContent = `
                .cash-modal .modal-content {
                    max-width: 800px;
                    width: 90%;
                }
                
                .cash-summary {
                    background: linear-gradient(135deg, #10b981, #059669);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    margin-bottom: 25px;
                    text-align: center;
                }
                
                .current-cash h3 {
                    margin: 0 0 10px 0;
                    font-size: 16px;
                    opacity: 0.9;
                }
                
                .cash-amount {
                    font-size: 32px;
                    font-weight: 700;
                    margin-bottom: 5px;
                }
                
                .last-updated {
                    font-size: 12px;
                    opacity: 0.8;
                }
                
                .cash-actions {
                    margin-bottom: 25px;
                }
                
                .cash-actions h3 {
                    margin: 0 0 15px 0;
                    color: var(--gray-900);
                    font-size: 16px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid var(--gray-200);
                }
                
                .recent-transactions h3 {
                    margin: 0 0 15px 0;
                    color: var(--gray-900);
                    font-size: 16px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid var(--gray-200);
                }
            `;
            document.head.appendChild(style);

        } catch (error) {
            console.error('❌ Failed to fix cash modal display:', error);
        }
    }

    /**
     * Integrate with reports system
     */
    integrateWithReports() {
        try {
            if (window.zaiqaReports && typeof window.zaiqaReports.addCashIntegration !== 'function') {
                window.zaiqaReports.addCashIntegration = function() {
                    // This will be called by reports to get enhanced cash data
                    return {
                        cashTransactions: window.cashManager ? window.cashManager.getRecentCashTransactions(50) : [],
                        currentCash: parseFloat(localStorage.getItem('currentCashInHand') || '0'),
                        lastUpdate: localStorage.getItem('lastCashUpdate')
                    };
                };
            }

        } catch (error) {
            console.error('❌ Failed to integrate with reports:', error);
        }
    }

    /**
     * Set up automatic data synchronization
     */
    setupDataSynchronization() {
        try {
            // Sync data every 30 seconds
            setInterval(() => {
                this.syncFinancialData();
            }, 30000);

        } catch (error) {
            console.error('❌ Failed to setup data synchronization:', error);
        }
    }

    /**
     * Sync financial data across all systems
     */
    syncFinancialData() {
        try {
            // Update dashboard if visible
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                window.app.updateDashboardStats();
            }

            // Update reports if visible
            const reportsPage = document.getElementById('reportsPage');
            if (reportsPage && reportsPage.style.display !== 'none') {
                if (window.zaiqaReports && typeof window.zaiqaReports.refreshData === 'function') {
                    window.zaiqaReports.refreshData();
                }
            }

        } catch (error) {
            console.error('❌ Failed to sync financial data:', error);
        }
    }

    /**
     * Perform data cleanup
     */
    performDataCleanup() {
        try {
            // Clean up phantom expenses
            if (window.aiCleanup && typeof window.aiCleanup.cleanupPhantomExpenses === 'function') {
                window.aiCleanup.cleanupPhantomExpenses();
            }

            // Validate cash in hand
            this.validateCashInHand();

        } catch (error) {
            console.error('❌ Failed to perform data cleanup:', error);
        }
    }

    /**
     * Validate cash in hand calculations
     */
    validateCashInHand() {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            
            if (isNaN(currentCash)) {
                localStorage.setItem('currentCashInHand', '0');
                console.log('✅ Reset invalid cash in hand value');
            }

        } catch (error) {
            console.error('❌ Failed to validate cash in hand:', error);
        }
    }

    /**
     * Export cash report
     */
    exportCashReport() {
        try {
            const cashData = {
                currentCash: parseFloat(localStorage.getItem('currentCashInHand') || '0'),
                transactions: window.cashManager ? window.cashManager.getRecentCashTransactions(100) : [],
                exportDate: new Date().toISOString(),
                summary: this.generateCashSummary()
            };

            const blob = new Blob([JSON.stringify(cashData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-cash-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            if (window.cashManager && typeof window.cashManager.showNotification === 'function') {
                window.cashManager.showNotification('Cash report exported successfully', 'success');
            }

        } catch (error) {
            console.error('❌ Failed to export cash report:', error);
        }
    }

    /**
     * Generate cash summary
     */
    generateCashSummary() {
        try {
            const transactions = window.cashManager ? window.cashManager.getRecentCashTransactions(30) : [];
            
            let totalReceipts = 0;
            let totalDeductions = 0;
            
            transactions.forEach(transaction => {
                if (transaction.amount > 0) {
                    totalReceipts += transaction.amount;
                } else {
                    totalDeductions += Math.abs(transaction.amount);
                }
            });

            return {
                totalReceipts,
                totalDeductions,
                netChange: totalReceipts - totalDeductions,
                transactionCount: transactions.length
            };

        } catch (error) {
            console.error('❌ Failed to generate cash summary:', error);
            return { totalReceipts: 0, totalDeductions: 0, netChange: 0, transactionCount: 0 };
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize cash integration
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.cashIntegration = new ZaiqaCashIntegration();
    }, 2500);
});

// Export for global use
window.ZaiqaCashIntegration = ZaiqaCashIntegration;
