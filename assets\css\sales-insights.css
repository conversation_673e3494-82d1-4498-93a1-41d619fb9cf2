/* Sales Insights Page Styles */

.sales-insights-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.insights-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.date-range-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-range-selector label {
    font-weight: 600;
    color: #374151;
}

.date-range-selector select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
}

.custom-date-range {
    display: flex;
    align-items: center;
    gap: 10px;
}

.custom-date-range input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

/* Revenue Overview Cards */
.revenue-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.revenue-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.revenue-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.revenue-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.total-revenue .revenue-icon { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.orders-revenue .revenue-icon { background: linear-gradient(135deg, #10b981, #047857); }
.udhar-revenue .revenue-icon { background: linear-gradient(135deg, #f59e0b, #d97706); }
.cash-position .revenue-icon { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

.revenue-content h3 {
    font-size: 28px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 4px 0;
}

.revenue-content p {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 8px 0;
    font-weight: 500;
}

.revenue-content small {
    font-size: 12px;
    color: #9ca3af;
}

/* Insights Grid */
.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.insight-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-content {
    padding: 20px;
}

.chart-container {
    min-height: 200px;
}

/* Chart Styles */
.chart-legend {
    margin-bottom: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.legend-item:last-child {
    border-bottom: none;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.legend-label {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

.legend-value {
    font-weight: 600;
    color: #111827;
}

.legend-percentage {
    font-size: 12px;
    color: #6b7280;
}

.simple-bar-chart {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.bar-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.bar-label {
    width: 80px;
    font-size: 12px;
    color: #6b7280;
    text-align: right;
}

.bar-container {
    flex: 1;
    height: 20px;
    background: #f3f4f6;
    border-radius: 10px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.bar-value {
    width: 80px;
    font-size: 12px;
    font-weight: 600;
    color: #111827;
    text-align: right;
}

/* Trend Styles */
.trend-summary {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 700;
}

.trend-description p {
    margin: 0 0 4px 0;
    color: #374151;
}

.trend-description small {
    color: #6b7280;
}

.trend-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.metric {
    text-align: center;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
}

.metric-label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
}

.metric-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #111827;
}

/* Analytics Tables */
.analytics-tables {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.table-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel-actions select {
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 12px;
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.data-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    color: #111827;
    font-size: 14px;
}

.data-table tr:hover {
    background: #f9fafb;
}

/* Cash Flow Analysis */
.cash-flow-summary {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
}

.flow-section {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background: #f9fafb;
}

.flow-section h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #374151;
}

.flow-amount {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 12px;
}

.inflow .flow-amount { color: #10b981; }
.outflow .flow-amount { color: #ef4444; }
.net-position .flow-amount.positive { color: #10b981; }
.net-position .flow-amount.negative { color: #ef4444; }

.flow-breakdown {
    text-align: left;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    padding: 4px 0;
    font-size: 12px;
    color: #6b7280;
}

.position-status {
    margin-top: 8px;
    font-size: 12px;
}

.status-positive { color: #10b981; }
.status-negative { color: #ef4444; }

/* Responsive Design */
@media (max-width: 768px) {
    .insights-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .revenue-overview {
        grid-template-columns: 1fr;
    }
    
    .insights-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-tables {
        grid-template-columns: 1fr;
    }
    
    .cash-flow-summary {
        grid-template-columns: 1fr;
    }
}
