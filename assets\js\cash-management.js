/**
 * Zaiqa Cash Management System - Enhanced Integration
 * Comprehensive cash tracking with revenue and expense integration
 */

class ZaiqaCashManagement {
    constructor() {
        this.version = '2.0.0';
        this.initialized = false;
        
        this.init();
    }

    /**
     * Initialize the cash management system
     */
    init() {
        try {
            console.log('💰 Initializing Enhanced Cash Management v' + this.version);
            
            // Set up cash transaction integration
            this.setupCashIntegration();
            
            // Initialize expense categorization
            this.initializeExpenseCategories();
            
            // Set up automatic expense tracking
            this.setupAutomaticExpenses();
            
            this.initialized = true;
            console.log('✅ Enhanced Cash Management initialized successfully');
            
        } catch (error) {
            console.error('❌ Cash Management initialization failed:', error);
        }
    }

    /**
     * Set up cash transaction integration with revenue/expense tracking
     */
    setupCashIntegration() {
        try {
            // Override the existing cash receipt function
            if (window.app && typeof window.app.addCashReceipt === 'function') {
                window.app.originalAddCashReceipt = window.app.addCashReceipt;
                
                window.app.addCashReceipt = (amount, description, category = 'Cash Transaction') => {
                    return this.processEnhancedCashTransaction(amount, description, category);
                };
            }
            
            console.log('✅ Cash integration setup completed');
            
        } catch (error) {
            console.error('❌ Failed to setup cash integration:', error);
        }
    }

    /**
     * Process enhanced cash transaction with automatic revenue/expense tracking
     */
    processEnhancedCashTransaction(amount, description, category) {
        try {
            const numAmount = parseFloat(amount);
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();
            
            if (isNaN(numAmount) || numAmount === 0) {
                throw new Error('Invalid amount entered');
            }

            // Create cash transaction record
            const cashTransaction = {
                id: 'cash_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                amount: numAmount,
                description: description || 'Cash Transaction',
                category: category,
                date: today,
                timestamp: timestamp,
                type: numAmount > 0 ? 'receipt' : 'deduction'
            };

            // Store cash transaction
            this.storeCashTransaction(cashTransaction);

            // Process based on transaction type
            if (numAmount > 0) {
                // Positive amount - Add to revenue
                this.addToRevenue(cashTransaction);
                this.showNotification(`Cash receipt of PKR ${numAmount.toLocaleString()} added to revenue`, 'success');
            } else {
                // Negative amount - Add to expenses
                this.addToExpenses(cashTransaction);
                this.showNotification(`Cash deduction of PKR ${Math.abs(numAmount).toLocaleString()} added to expenses`, 'info');
            }

            // Update cash in hand
            this.updateCashInHand(numAmount);

            // Refresh displays
            this.refreshFinancialDisplays();

            return true;

        } catch (error) {
            console.error('❌ Failed to process cash transaction:', error);
            this.showNotification('Failed to process cash transaction: ' + error.message, 'error');
            return false;
        }
    }

    /**
     * Store cash transaction in localStorage
     */
    storeCashTransaction(transaction) {
        try {
            const cashTransactions = JSON.parse(localStorage.getItem('cashTransactions') || '[]');
            cashTransactions.push(transaction);
            
            // Keep only last 1000 transactions
            if (cashTransactions.length > 1000) {
                cashTransactions.splice(0, cashTransactions.length - 1000);
            }
            
            localStorage.setItem('cashTransactions', JSON.stringify(cashTransactions));
            
        } catch (error) {
            console.error('❌ Failed to store cash transaction:', error);
        }
    }

    /**
     * Add positive cash transaction to revenue
     */
    addToRevenue(transaction) {
        try {
            // Create revenue entry
            const revenueEntry = {
                id: 'rev_' + transaction.id,
                order_number: 'CASH-' + Date.now(),
                customer_name: 'Cash Receipt',
                customer_count: 1,
                service_type: 'cash_receipt',
                items: [{
                    name: transaction.description,
                    price: transaction.amount,
                    quantity: 1
                }],
                total_amount: transaction.amount,
                payment_method: 'cash',
                status: 'completed',
                created_at: transaction.timestamp,
                cash_transaction_id: transaction.id
            };

            // Add to orders for revenue tracking
            const orders = JSON.parse(localStorage.getItem('restaurantOrders') || '[]');
            orders.push(revenueEntry);
            localStorage.setItem('restaurantOrders', JSON.stringify(orders));

            console.log('✅ Cash receipt added to revenue:', transaction.amount);

        } catch (error) {
            console.error('❌ Failed to add cash receipt to revenue:', error);
        }
    }

    /**
     * Add negative cash transaction to expenses
     */
    addToExpenses(transaction) {
        try {
            // Create expense entry
            const expenseEntry = {
                id: 'exp_' + transaction.id,
                description: transaction.description || 'Cash Deduction',
                amount: Math.abs(transaction.amount),
                category: transaction.category || 'Cash Deduction',
                date: transaction.date,
                created_at: transaction.timestamp,
                cash_transaction_id: transaction.id,
                type: 'cash_deduction'
            };

            // Add to expenses
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            expenses.push(expenseEntry);
            localStorage.setItem('expenses', JSON.stringify(expenses));

            console.log('✅ Cash deduction added to expenses:', Math.abs(transaction.amount));

        } catch (error) {
            console.error('❌ Failed to add cash deduction to expenses:', error);
        }
    }

    /**
     * Update cash in hand balance
     */
    updateCashInHand(amount) {
        try {
            const currentCash = parseFloat(localStorage.getItem('currentCashInHand') || '0');
            const newCash = currentCash + amount;
            
            localStorage.setItem('currentCashInHand', newCash.toString());
            localStorage.setItem('lastCashUpdate', new Date().toISOString());

            // Update cash register for today
            this.updateTodayCashRegister(amount);

            console.log('✅ Cash in hand updated:', newCash);

        } catch (error) {
            console.error('❌ Failed to update cash in hand:', error);
        }
    }

    /**
     * Update today's cash register entry
     */
    updateTodayCashRegister(amount) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const cashRegister = JSON.parse(localStorage.getItem('cashRegister') || '[]');
            
            let todayEntry = cashRegister.find(entry => entry.date === today);
            
            if (!todayEntry) {
                todayEntry = {
                    date: today,
                    morningBalance: 0,
                    eveningBalance: 0,
                    totalSales: 0,
                    totalExpenses: 0,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                cashRegister.push(todayEntry);
            }

            // Update based on transaction type
            if (amount > 0) {
                todayEntry.totalSales = (todayEntry.totalSales || 0) + amount;
            } else {
                todayEntry.totalExpenses = (todayEntry.totalExpenses || 0) + Math.abs(amount);
            }

            todayEntry.eveningBalance = (todayEntry.morningBalance || 0) + 
                                       (todayEntry.totalSales || 0) - 
                                       (todayEntry.totalExpenses || 0);
            todayEntry.updatedAt = new Date().toISOString();

            localStorage.setItem('cashRegister', JSON.stringify(cashRegister));

        } catch (error) {
            console.error('❌ Failed to update cash register:', error);
        }
    }

    /**
     * Initialize expense categorization system
     */
    initializeExpenseCategories() {
        try {
            const defaultCategories = {
                operational: {
                    name: 'Operational Expenses',
                    nameUrdu: 'آپریشنل اخراجات',
                    subcategories: [
                        { id: 'rent', name: 'Shop Rent', nameUrdu: 'دکان کا کرایہ', amount: 0, recurring: true },
                        { id: 'electricity', name: 'Electricity Bill', nameUrdu: 'بجلی بل', amount: 0, recurring: true },
                        { id: 'gas', name: 'Gas Bill', nameUrdu: 'گیس بل', amount: 0, recurring: true },
                        { id: 'water', name: 'Water Bill', nameUrdu: 'پانی بل', amount: 0, recurring: true },
                        { id: 'internet', name: 'Internet Bill', nameUrdu: 'انٹرنیٹ بل', amount: 0, recurring: true },
                        { id: 'maintenance', name: 'Maintenance', nameUrdu: 'مرمت', amount: 0, recurring: false }
                    ]
                },
                staff: {
                    name: 'Staff Salaries',
                    nameUrdu: 'عملے کی تنخواہ',
                    subcategories: []
                },
                manual: {
                    name: 'Manual Expenses',
                    nameUrdu: 'دستی اخراجات',
                    subcategories: [
                        { id: 'supplies', name: 'Supplies', nameUrdu: 'سامان', amount: 0, recurring: false },
                        { id: 'food', name: 'Food Supplies', nameUrdu: 'کھانے کا سامان', amount: 0, recurring: false },
                        { id: 'transport', name: 'Transportation', nameUrdu: 'ٹرانسپورٹ', amount: 0, recurring: false },
                        { id: 'other', name: 'Other', nameUrdu: 'دیگر', amount: 0, recurring: false }
                    ]
                }
            };

            // Load existing categories or set defaults
            const existingCategories = localStorage.getItem('expenseCategories');
            if (!existingCategories) {
                localStorage.setItem('expenseCategories', JSON.stringify(defaultCategories));
            }

            // Load staff for salary categories
            this.loadStaffSalaryCategories();

            console.log('✅ Expense categories initialized');

        } catch (error) {
            console.error('❌ Failed to initialize expense categories:', error);
        }
    }

    /**
     * Load staff members for salary categories
     */
    loadStaffSalaryCategories() {
        try {
            const staff = JSON.parse(localStorage.getItem('staff') || '[]');
            const categories = JSON.parse(localStorage.getItem('expenseCategories') || '{}');
            
            // Clear existing staff subcategories
            categories.staff.subcategories = [];
            
            // Add each staff member as a salary subcategory
            staff.forEach(member => {
                if (member.isActive) {
                    const dailyWage = member.monthlySalary ? (member.monthlySalary / 30) : 0;
                    categories.staff.subcategories.push({
                        id: 'staff_' + member.id,
                        name: member.name + ' (Daily Wage)',
                        nameUrdu: member.name + ' (دہاڑی)',
                        amount: dailyWage,
                        recurring: true,
                        staffId: member.id
                    });
                }
            });

            localStorage.setItem('expenseCategories', JSON.stringify(categories));

        } catch (error) {
            console.error('❌ Failed to load staff salary categories:', error);
        }
    }

    /**
     * Set up automatic expense tracking
     */
    setupAutomaticExpenses() {
        try {
            // Remove any phantom automatic expenses
            this.cleanupPhantomExpenses();
            
            // Set up day-end expense prompts
            this.setupDayEndExpensePrompts();

            console.log('✅ Automatic expense tracking setup completed');

        } catch (error) {
            console.error('❌ Failed to setup automatic expenses:', error);
        }
    }

    /**
     * Clean up phantom expenses
     */
    cleanupPhantomExpenses() {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
            
            // Remove any expenses with "auto expense" or similar phantom entries
            const cleanedExpenses = expenses.filter(expense => {
                const description = (expense.description || '').toLowerCase();
                return !description.includes('auto expense') && 
                       !description.includes('phantom') &&
                       expense.amount > 0;
            });

            if (cleanedExpenses.length !== expenses.length) {
                localStorage.setItem('expenses', JSON.stringify(cleanedExpenses));
                console.log(`✅ Cleaned up ${expenses.length - cleanedExpenses.length} phantom expenses`);
            }

        } catch (error) {
            console.error('❌ Failed to cleanup phantom expenses:', error);
        }
    }

    /**
     * Set up day-end expense prompts
     */
    setupDayEndExpensePrompts() {
        try {
            // Override the business day manager's transition function
            if (window.businessDayManager && typeof window.businessDayManager.executeTransition === 'function') {
                window.businessDayManager.originalExecuteTransition = window.businessDayManager.executeTransition;
                
                window.businessDayManager.executeTransition = () => {
                    this.showDayEndExpensePrompts();
                };
            }

        } catch (error) {
            console.error('❌ Failed to setup day-end expense prompts:', error);
        }
    }

    /**
     * Show day-end expense prompts
     */
    showDayEndExpensePrompts() {
        try {
            const categories = JSON.parse(localStorage.getItem('expenseCategories') || '{}');
            
            const modal = document.createElement('div');
            modal.className = 'modal-overlay day-end-expenses-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-calculator"></i> Day-End Expense Processing</h2>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="expense-categories">
                            ${this.renderOperationalExpenses(categories.operational)}
                            ${this.renderStaffSalaries(categories.staff)}
                            ${this.renderManualExpenseOption()}
                        </div>
                        
                        <div class="expense-summary">
                            <h3>Expense Summary</h3>
                            <div id="expenseSummaryContent">
                                <p>Select expenses above to see summary</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn btn-secondary">
                            Skip Expenses
                        </button>
                        <button onclick="window.cashManager.processDayEndExpenses()" class="btn btn-primary">
                            <i class="fas fa-check"></i> Process Expenses & End Day
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);

        } catch (error) {
            console.error('❌ Failed to show day-end expense prompts:', error);
        }
    }

    /**
     * Refresh financial displays
     */
    refreshFinancialDisplays() {
        try {
            // Refresh dashboard if available
            if (window.app && typeof window.app.updateDashboardStats === 'function') {
                window.app.updateDashboardStats();
            }

            // Refresh reports page if visible
            const reportsPage = document.getElementById('reportsPage');
            if (reportsPage && reportsPage.style.display !== 'none') {
                if (window.zaiqaReports && typeof window.zaiqaReports.refreshReports === 'function') {
                    window.zaiqaReports.refreshReports();
                }
            }

            // Refresh cash in hand modal if open
            const cashModal = document.querySelector('.cash-modal');
            if (cashModal) {
                this.refreshCashModal();
            }

        } catch (error) {
            console.error('❌ Failed to refresh financial displays:', error);
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        try {
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(message, type);
            } else {
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        } catch (error) {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Get recent cash transactions for display
     */
    getRecentCashTransactions(limit = 10) {
        try {
            const transactions = JSON.parse(localStorage.getItem('cashTransactions') || '[]');
            return transactions
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                .slice(0, limit);
        } catch (error) {
            console.error('❌ Failed to get recent cash transactions:', error);
            return [];
        }
    }

    /**
     * Render operational expenses section
     */
    renderOperationalExpenses(operational) {
        return `
            <div class="expense-category">
                <h3>
                    <i class="fas fa-building"></i>
                    ${operational.name} (${operational.nameUrdu})
                </h3>
                <div class="expense-items">
                    ${operational.subcategories.map(item => `
                        <div class="expense-item">
                            <label class="checkbox-label">
                                <input type="checkbox"
                                       id="op_${item.id}"
                                       data-category="operational"
                                       data-amount="${item.amount}"
                                       onchange="window.cashManager.updateExpenseSummary()">
                                <span>${item.name} (${item.nameUrdu})</span>
                            </label>
                            <div class="amount-input">
                                <input type="number"
                                       id="amount_op_${item.id}"
                                       value="${item.amount}"
                                       placeholder="Amount"
                                       onchange="window.cashManager.updateExpenseSummary()">
                                <span>PKR</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Render staff salaries section
     */
    renderStaffSalaries(staff) {
        return `
            <div class="expense-category">
                <h3>
                    <i class="fas fa-users"></i>
                    ${staff.name} (${staff.nameUrdu})
                </h3>
                <div class="expense-items">
                    ${staff.subcategories.length > 0 ? staff.subcategories.map(item => `
                        <div class="expense-item">
                            <label class="checkbox-label">
                                <input type="checkbox"
                                       id="staff_${item.id}"
                                       data-category="staff"
                                       data-amount="${item.amount}"
                                       onchange="window.cashManager.updateExpenseSummary()">
                                <span>${item.name} (${item.nameUrdu})</span>
                            </label>
                            <div class="amount-input">
                                <input type="number"
                                       id="amount_staff_${item.id}"
                                       value="${item.amount}"
                                       placeholder="Daily wage"
                                       onchange="window.cashManager.updateExpenseSummary()">
                                <span>PKR</span>
                            </div>
                        </div>
                    `).join('') : '<p class="no-staff">No active staff members found. Add staff in the Staff Management section.</p>'}
                </div>
            </div>
        `;
    }

    /**
     * Render manual expense option
     */
    renderManualExpenseOption() {
        return `
            <div class="expense-category">
                <h3>
                    <i class="fas fa-edit"></i>
                    Manual Expenses (دستی اخراجات)
                </h3>
                <div class="manual-expense-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>Description:</label>
                            <input type="text" id="manualExpenseDescription" placeholder="Enter expense description">
                        </div>
                        <div class="form-group">
                            <label>Amount (PKR):</label>
                            <input type="number" id="manualExpenseAmount" placeholder="0" onchange="window.cashManager.updateExpenseSummary()">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Category:</label>
                            <select id="manualExpenseCategory">
                                <option value="supplies">Supplies (سامان)</option>
                                <option value="food">Food Supplies (کھانے کا سامان)</option>
                                <option value="transport">Transportation (ٹرانسپورٹ)</option>
                                <option value="maintenance">Maintenance (مرمت)</option>
                                <option value="other">Other (دیگر)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button type="button" onclick="window.cashManager.addManualExpense()" class="btn btn-sm btn-secondary">
                                <i class="fas fa-plus"></i> Add Expense
                            </button>
                        </div>
                    </div>
                    <div id="manualExpensesList" class="manual-expenses-list">
                        <!-- Manual expenses will be added here -->
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update expense summary
     */
    updateExpenseSummary() {
        try {
            let totalExpenses = 0;
            const summary = [];

            // Check operational expenses
            const operationalCheckboxes = document.querySelectorAll('input[data-category="operational"]:checked');
            operationalCheckboxes.forEach(checkbox => {
                const amountInput = document.getElementById('amount_' + checkbox.id);
                const amount = parseFloat(amountInput?.value || 0);
                if (amount > 0) {
                    totalExpenses += amount;
                    summary.push({
                        category: 'Operational',
                        description: checkbox.nextElementSibling.textContent,
                        amount: amount
                    });
                }
            });

            // Check staff salaries
            const staffCheckboxes = document.querySelectorAll('input[data-category="staff"]:checked');
            staffCheckboxes.forEach(checkbox => {
                const amountInput = document.getElementById('amount_' + checkbox.id);
                const amount = parseFloat(amountInput?.value || 0);
                if (amount > 0) {
                    totalExpenses += amount;
                    summary.push({
                        category: 'Staff Salary',
                        description: checkbox.nextElementSibling.textContent,
                        amount: amount
                    });
                }
            });

            // Check manual expenses
            const manualAmount = parseFloat(document.getElementById('manualExpenseAmount')?.value || 0);
            if (manualAmount > 0) {
                totalExpenses += manualAmount;
                const description = document.getElementById('manualExpenseDescription')?.value || 'Manual Expense';
                summary.push({
                    category: 'Manual',
                    description: description,
                    amount: manualAmount
                });
            }

            // Update summary display
            const summaryElement = document.getElementById('expenseSummaryContent');
            if (summaryElement) {
                if (summary.length === 0) {
                    summaryElement.innerHTML = '<p>No expenses selected</p>';
                } else {
                    summaryElement.innerHTML = `
                        <div class="expense-summary-items">
                            ${summary.map(item => `
                                <div class="summary-item">
                                    <span class="category">[${item.category}]</span>
                                    <span class="description">${item.description}</span>
                                    <span class="amount">PKR ${item.amount.toLocaleString()}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="summary-total">
                            <strong>Total Expenses: PKR ${totalExpenses.toLocaleString()}</strong>
                        </div>
                    `;
                }
            }

        } catch (error) {
            console.error('❌ Failed to update expense summary:', error);
        }
    }

    /**
     * Add manual expense
     */
    addManualExpense() {
        try {
            const description = document.getElementById('manualExpenseDescription')?.value;
            const amount = parseFloat(document.getElementById('manualExpenseAmount')?.value || 0);
            const category = document.getElementById('manualExpenseCategory')?.value;

            if (!description || amount <= 0) {
                this.showNotification('Please enter valid description and amount', 'error');
                return;
            }

            // Add to manual expenses list
            const manualExpensesList = document.getElementById('manualExpensesList');
            if (manualExpensesList) {
                const expenseItem = document.createElement('div');
                expenseItem.className = 'manual-expense-item';
                expenseItem.innerHTML = `
                    <span class="expense-desc">${description}</span>
                    <span class="expense-cat">[${category}]</span>
                    <span class="expense-amt">PKR ${amount.toLocaleString()}</span>
                    <button onclick="this.parentElement.remove(); window.cashManager.updateExpenseSummary()" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                manualExpensesList.appendChild(expenseItem);
            }

            // Clear form
            document.getElementById('manualExpenseDescription').value = '';
            document.getElementById('manualExpenseAmount').value = '';

            // Update summary
            this.updateExpenseSummary();

        } catch (error) {
            console.error('❌ Failed to add manual expense:', error);
            this.showNotification('Failed to add manual expense', 'error');
        }
    }

    /**
     * Process day-end expenses
     */
    processDayEndExpenses() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();
            let totalProcessed = 0;

            // Process operational expenses
            const operationalCheckboxes = document.querySelectorAll('input[data-category="operational"]:checked');
            operationalCheckboxes.forEach(checkbox => {
                const amountInput = document.getElementById('amount_' + checkbox.id);
                const amount = parseFloat(amountInput?.value || 0);
                if (amount > 0) {
                    this.addExpenseEntry({
                        description: checkbox.nextElementSibling.textContent.split('(')[0].trim(),
                        amount: amount,
                        category: 'Operational Expenses',
                        date: today,
                        created_at: timestamp,
                        type: 'day_end_operational'
                    });
                    totalProcessed += amount;
                }
            });

            // Process staff salaries
            const staffCheckboxes = document.querySelectorAll('input[data-category="staff"]:checked');
            staffCheckboxes.forEach(checkbox => {
                const amountInput = document.getElementById('amount_' + checkbox.id);
                const amount = parseFloat(amountInput?.value || 0);
                if (amount > 0) {
                    this.addExpenseEntry({
                        description: checkbox.nextElementSibling.textContent.split('(')[0].trim(),
                        amount: amount,
                        category: 'Staff Salaries',
                        date: today,
                        created_at: timestamp,
                        type: 'day_end_salary'
                    });
                    totalProcessed += amount;
                }
            });

            // Process manual expenses
            const manualExpenseItems = document.querySelectorAll('.manual-expense-item');
            manualExpenseItems.forEach(item => {
                const description = item.querySelector('.expense-desc').textContent;
                const category = item.querySelector('.expense-cat').textContent.replace(/[\[\]]/g, '');
                const amountText = item.querySelector('.expense-amt').textContent.replace(/[^\d.]/g, '');
                const amount = parseFloat(amountText);

                if (amount > 0) {
                    this.addExpenseEntry({
                        description: description,
                        amount: amount,
                        category: 'Manual Expenses - ' + category,
                        date: today,
                        created_at: timestamp,
                        type: 'day_end_manual'
                    });
                    totalProcessed += amount;
                }
            });

            // Deduct from cash in hand
            if (totalProcessed > 0) {
                this.updateCashInHand(-totalProcessed);
                this.showNotification(`Day-end expenses processed: PKR ${totalProcessed.toLocaleString()} deducted from cash`, 'success');
            }

            // Close modal and proceed with original day transition
            document.querySelector('.day-end-expenses-modal')?.remove();

            // Call original business day transition
            if (window.businessDayManager && window.businessDayManager.originalExecuteTransition) {
                window.businessDayManager.originalExecuteTransition();
            }

        } catch (error) {
            console.error('❌ Failed to process day-end expenses:', error);
            this.showNotification('Failed to process day-end expenses', 'error');
        }
    }

    /**
     * Add expense entry to database
     */
    addExpenseEntry(expenseData) {
        try {
            const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');

            const expense = {
                id: 'exp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                ...expenseData
            };

            expenses.push(expense);
            localStorage.setItem('expenses', JSON.stringify(expenses));

        } catch (error) {
            console.error('❌ Failed to add expense entry:', error);
        }
    }

    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
}

// Initialize the cash management system
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.cashManager = new ZaiqaCashManagement();
    }, 1500);
});

// Export for global use
window.ZaiqaCashManagement = ZaiqaCashManagement;
