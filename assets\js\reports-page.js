/**
 * Zaiqa Restaurant Reports Page - Clean Architecture
 * Comprehensive Financial Analytics and Data Visualization
 * Built from scratch with accurate calculations
 */

class ZaiqaReportsPage {
    constructor(zaiqaSystem) {
        this.zaiqaSystem = zaiqaSystem;
        this.version = '2.0.0';
        this.currentPeriod = 'today';
        this.customStartDate = null;
        this.customEndDate = null;
        
        console.log('📊 Reports Page initialized v' + this.version);
    }

    /**
     * Safe method to get financial data with fallbacks
     */
    safeGetFinancialData(type, ...args) {
        try {
            if (!this.zaiqaSystem || !this.zaiqaSystem.getFinancialData) {
                return this.getEmptyData(type);
            }

            const data = this.zaiqaSystem.getFinancialData(type, ...args);
            return data || this.getEmptyData(type);

        } catch (error) {
            console.error(`❌ Failed to get ${type} data:`, error);
            return this.getEmptyData(type);
        }
    }

    /**
     * Get empty data structure for different types
     */
    getEmptyData(type) {
        switch (type) {
            case 'todayMetrics':
                return {
                    revenue: { totalRevenue: 0, orderRevenue: 0, udharPayments: 0, orderCount: 0, averageOrderValue: 0 },
                    expenses: { totalExpenses: 0, expensesByCategory: {}, expenseCount: 0, averageExpense: 0 },
                    profitLoss: { grossProfit: 0, profitMargin: 0, isProfit: false },
                    cashFlow: { openingBalance: 0, closingBalance: 0, dayRevenue: 0, dayExpenses: 0, netCashFlow: 0, cashDifference: 0 }
                };
            case 'udharSummary':
                return { totalUdharAmount: 0, totalPaidAmount: 0, totalRemainingAmount: 0, activeCustomers: 0, totalCustomers: 0, recoveryRate: 0 };
            case 'inventoryValue':
                return { totalValue: 0, totalItems: 0, lowStockItems: 0, outOfStockItems: 0, inStockItems: 0 };
            default:
                return {};
        }
    }

    /**
     * Render loading state
     */
    renderLoadingState() {
        return `
            <div class="loading-state">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <h2>Loading Financial Reports...</h2>
                <p>Initializing financial systems and calculating metrics...</p>
            </div>
        `;
    }

    /**
     * Render the complete reports page
     */
    render(pageElement) {
        try {
            console.log('📊 Rendering Reports Page...');

            // Check if system is properly initialized
            if (!this.zaiqaSystem || !this.zaiqaSystem.isInitialized()) {
                console.warn('⚠️ System not initialized, showing loading state...');
                pageElement.innerHTML = this.renderLoadingState();
                setTimeout(() => this.render(pageElement), 2000);
                return;
            }

            // Get current financial data with fallbacks
            const todayMetrics = this.safeGetFinancialData('todayMetrics');
            const udharSummary = this.safeGetFinancialData('udharSummary');
            const inventoryValue = this.safeGetFinancialData('inventoryValue');
            
            pageElement.innerHTML = `
                <div class="reports-container">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="header-content">
                            <h1 class="page-title">
                                <i class="fas fa-chart-bar"></i>
                                Financial Reports & Analytics
                            </h1>
                            <p class="page-subtitle">Comprehensive business insights and cost management</p>
                        </div>
                        <div class="header-actions">
                            <button onclick="zaiqaReports.refreshReports()" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button onclick="zaiqaReports.showCostAnalysis()" class="btn btn-warning">
                                <i class="fas fa-calculator"></i> Cost Analysis
                            </button>
                            <button onclick="zaiqaReports.exportReports()" class="btn btn-primary">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <!-- Quick Stats Bar -->
                    <div class="quick-stats-bar">
                        ${this.renderQuickStats(todayMetrics)}
                    </div>

                    <!-- Period Selection -->
                    <div class="period-selection-card">
                        <h3>Select Report Period</h3>
                        <div class="period-buttons">
                            <button onclick="zaiqaReports.setPeriod('today')" 
                                    class="period-btn ${this.currentPeriod === 'today' ? 'active' : ''}">
                                Today
                            </button>
                            <button onclick="zaiqaReports.setPeriod('week')" 
                                    class="period-btn ${this.currentPeriod === 'week' ? 'active' : ''}">
                                This Week
                            </button>
                            <button onclick="zaiqaReports.setPeriod('month')" 
                                    class="period-btn ${this.currentPeriod === 'month' ? 'active' : ''}">
                                This Month
                            </button>
                            <button onclick="zaiqaReports.setPeriod('custom')" 
                                    class="period-btn ${this.currentPeriod === 'custom' ? 'active' : ''}">
                                Custom Period
                            </button>
                        </div>
                        
                        ${this.currentPeriod === 'custom' ? this.renderCustomDateInputs() : ''}
                    </div>

                    <!-- Key Performance Indicators -->
                    <div class="kpi-grid">
                        ${this.renderKPICards(todayMetrics, udharSummary, inventoryValue)}
                    </div>

                    <!-- Advanced Analytics Grid -->
                    <div class="analytics-grid">
                        <!-- Revenue Trends -->
                        <div class="chart-card">
                            <h3><i class="fas fa-chart-line"></i> Revenue Trends</h3>
                            <div class="chart-container">
                                ${this.renderRevenueTrends()}
                            </div>
                        </div>

                        <!-- Expense Breakdown -->
                        <div class="chart-card">
                            <h3><i class="fas fa-pie-chart"></i> Expense Breakdown</h3>
                            <div class="chart-container">
                                ${this.renderExpenseBreakdown()}
                            </div>
                        </div>

                        <!-- Top Items -->
                        <div class="chart-card">
                            <h3><i class="fas fa-trophy"></i> Top Selling Items</h3>
                            <div id="topItemsList" class="top-items-list">
                                ${this.renderTopItems()}
                            </div>
                        </div>

                        <!-- Cash Flow -->
                        <div class="chart-card">
                            <h3><i class="fas fa-money-bill-wave"></i> Cash Flow Analysis</h3>
                            <div id="cashFlowAnalysis">
                                ${this.renderCashFlowAnalysis(todayMetrics.cashFlow)}
                            </div>
                        </div>

                        <!-- Cost Analysis -->
                        <div class="chart-card">
                            <h3><i class="fas fa-calculator"></i> Cost Analysis</h3>
                            <div id="costAnalysis">
                                ${this.renderCostAnalysis()}
                            </div>
                        </div>

                        <!-- Profit Margins -->
                        <div class="chart-card">
                            <h3><i class="fas fa-percentage"></i> Profit Margins</h3>
                            <div id="profitMargins">
                                ${this.renderProfitMargins()}
                            </div>
                        </div>
                    </div>

                    <!-- Business Intelligence Section -->
                    <div class="business-intelligence-section">
                        <h2><i class="fas fa-brain"></i> Business Intelligence & Insights</h2>

                        <div class="intelligence-grid">
                            <!-- Performance Metrics -->
                            <div class="intelligence-card">
                                <h3>Performance Metrics</h3>
                                ${this.renderPerformanceMetrics()}
                            </div>

                            <!-- Cost Optimization -->
                            <div class="intelligence-card">
                                <h3>Cost Optimization Suggestions</h3>
                                ${this.renderCostOptimization()}
                            </div>

                            <!-- Revenue Opportunities -->
                            <div class="intelligence-card">
                                <h3>Revenue Opportunities</h3>
                                ${this.renderRevenueOpportunities()}
                            </div>

                            <!-- Risk Analysis -->
                            <div class="intelligence-card">
                                <h3>Risk Analysis</h3>
                                ${this.renderRiskAnalysis()}
                            </div>
                        </div>
                    </div>

                    <!-- Comprehensive Data Tables -->
                    <div class="tables-section">
                        <!-- Recent Orders -->
                        <div class="table-card">
                            <h3><i class="fas fa-shopping-cart"></i> Recent Orders</h3>
                            <div class="table-container">
                                ${this.renderRecentOrdersTable()}
                            </div>
                        </div>

                        <!-- Expense Summary -->
                        <div class="table-card">
                            <h3><i class="fas fa-receipt"></i> Expense Summary</h3>
                            <div class="table-container">
                                ${this.renderExpenseSummaryTable()}
                            </div>
                        </div>

                        <!-- Staff Performance -->
                        <div class="table-card">
                            <h3><i class="fas fa-users"></i> Staff Performance</h3>
                            <div class="table-container">
                                ${this.renderStaffPerformanceTable()}
                            </div>
                        </div>

                        <!-- Inventory Status -->
                        <div class="table-card">
                            <h3><i class="fas fa-boxes"></i> Inventory Status</h3>
                            <div class="table-container">
                                ${this.renderInventoryStatusTable()}
                            </div>
                        </div>

                        <!-- Payment Methods Analysis -->
                        <div class="table-card">
                            <h3><i class="fas fa-credit-card"></i> Payment Methods</h3>
                            <div class="table-container">
                                ${this.renderPaymentMethodsTable()}
                            </div>
                        </div>

                        <!-- Udhar Management -->
                        <div class="table-card">
                            <h3><i class="fas fa-handshake"></i> Udhar Management</h3>
                            <div class="table-container">
                                ${this.renderUdharManagementTable()}
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Reports Section -->
                    <div class="advanced-reports-section">
                        <h2><i class="fas fa-chart-area"></i> Advanced Reports & Analytics</h2>

                        <div class="advanced-reports-grid">
                            <!-- Daily Hisab Kitab -->
                            <div class="report-card">
                                <h3>Daily Hisab Kitab</h3>
                                <button onclick="zaiqaReports.generateDailyReport()" class="btn btn-primary">
                                    <i class="fas fa-calendar-day"></i> Generate Daily Report
                                </button>
                                ${this.renderDailyHisabKitab()}
                            </div>

                            <!-- Weekly Analysis -->
                            <div class="report-card">
                                <h3>Weekly Analysis</h3>
                                <button onclick="zaiqaReports.generateWeeklyReport()" class="btn btn-primary">
                                    <i class="fas fa-calendar-week"></i> Generate Weekly Report
                                </button>
                                ${this.renderWeeklyAnalysis()}
                            </div>

                            <!-- Monthly Summary -->
                            <div class="report-card">
                                <h3>Monthly Summary</h3>
                                <button onclick="zaiqaReports.generateMonthlyReport()" class="btn btn-primary">
                                    <i class="fas fa-calendar-alt"></i> Generate Monthly Report
                                </button>
                                ${this.renderMonthlySummary()}
                            </div>

                            <!-- Custom Period Report -->
                            <div class="report-card">
                                <h3>Custom Period Report</h3>
                                <button onclick="zaiqaReports.generateCustomPeriodReport()" class="btn btn-primary">
                                    <i class="fas fa-calendar-check"></i> Generate Custom Report
                                </button>
                                ${this.renderCustomPeriodReport()}
                            </div>
                        </div>
                    </div>

                    <!-- Comprehensive Financial Summary -->
                    <div class="financial-summary-card">
                        <h3><i class="fas fa-chart-pie"></i> Comprehensive Financial Summary</h3>
                        ${this.renderFinancialSummary(todayMetrics)}
                    </div>

                    <!-- Cost Management Tools -->
                    <div class="cost-management-section">
                        <h2><i class="fas fa-tools"></i> Cost Management Tools</h2>

                        <div class="cost-tools-grid">
                            <!-- Budget Tracker -->
                            <div class="tool-card">
                                <h3>Budget Tracker</h3>
                                <button onclick="zaiqaReports.openBudgetTracker()" class="btn btn-success">
                                    <i class="fas fa-piggy-bank"></i> Manage Budget
                                </button>
                                ${this.renderBudgetTracker()}
                            </div>

                            <!-- Cost Alerts -->
                            <div class="tool-card">
                                <h3>Cost Alerts</h3>
                                <button onclick="zaiqaReports.manageCostAlerts()" class="btn btn-warning">
                                    <i class="fas fa-bell"></i> Set Alerts
                                </button>
                                ${this.renderCostAlerts()}
                            </div>

                            <!-- Expense Forecasting -->
                            <div class="tool-card">
                                <h3>Expense Forecasting</h3>
                                <button onclick="zaiqaReports.openExpenseForecast()" class="btn btn-info">
                                    <i class="fas fa-crystal-ball"></i> Forecast Expenses
                                </button>
                                ${this.renderExpenseForecast()}
                            </div>

                            <!-- ROI Calculator -->
                            <div class="tool-card">
                                <h3>ROI Calculator</h3>
                                <button onclick="zaiqaReports.openROICalculator()" class="btn btn-primary">
                                    <i class="fas fa-calculator"></i> Calculate ROI
                                </button>
                                ${this.renderROICalculator()}
                            </div>
                        </div>
                    </div>

                    <!-- Export & Backup Section -->
                    <div class="export-backup-section">
                        <h2><i class="fas fa-download"></i> Export & Backup</h2>

                        <div class="export-options">
                            <button onclick="zaiqaReports.exportPDF()" class="btn btn-danger">
                                <i class="fas fa-file-pdf"></i> Export PDF
                            </button>
                            <button onclick="zaiqaReports.exportExcel()" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> Export Excel
                            </button>
                            <button onclick="zaiqaReports.exportCSV()" class="btn btn-info">
                                <i class="fas fa-file-csv"></i> Export CSV
                            </button>
                            <button onclick="zaiqaReports.backupData()" class="btn btn-warning">
                                <i class="fas fa-database"></i> Backup Data
                            </button>
                            <button onclick="zaiqaReports.scheduleReports()" class="btn btn-secondary">
                                <i class="fas fa-clock"></i> Schedule Reports
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Initialize charts after DOM is updated
            setTimeout(() => {
                this.initializeCharts();
            }, 100);

        } catch (error) {
            console.error('❌ Failed to render reports page:', error);
            pageElement.innerHTML = `
                <div class="error-state">
                    <h2>Error Loading Reports</h2>
                    <p>Failed to load financial reports. Please try refreshing the page.</p>
                    <button onclick="zaiqaReports.render(document.getElementById('reportsPage'))" class="btn btn-primary">
                        Retry
                    </button>
                </div>
            `;
        }
    }

    /**
     * Render quick stats bar
     */
    renderQuickStats(metrics) {
        const revenue = metrics.revenue || {};
        const expenses = metrics.expenses || {};
        const profitLoss = metrics.profitLoss || {};

        return `
            <div class="quick-stat">
                <span class="stat-label">Today's Revenue:</span>
                <span class="stat-value positive">PKR ${(revenue.totalRevenue || 0).toLocaleString()}</span>
            </div>
            <div class="quick-stat">
                <span class="stat-label">Today's Expenses:</span>
                <span class="stat-value negative">PKR ${(expenses.totalExpenses || 0).toLocaleString()}</span>
            </div>
            <div class="quick-stat">
                <span class="stat-label">Net Profit:</span>
                <span class="stat-value ${profitLoss.isProfit ? 'positive' : 'negative'}">
                    PKR ${(profitLoss.grossProfit || 0).toLocaleString()}
                </span>
            </div>
            <div class="quick-stat">
                <span class="stat-label">Orders:</span>
                <span class="stat-value">${revenue.orderCount || 0}</span>
            </div>
            <div class="quick-stat">
                <span class="stat-label">Avg Order:</span>
                <span class="stat-value">PKR ${Math.round(revenue.averageOrderValue || 0).toLocaleString()}</span>
            </div>
        `;
    }

    /**
     * Render revenue trends
     */
    renderRevenueTrends() {
        try {
            const last7Days = this.getLast7DaysData();

            return `
                <div class="trend-chart">
                    <div class="trend-header">
                        <span>Last 7 Days Revenue Trend</span>
                        <span class="trend-total">Total: PKR ${last7Days.totalRevenue.toLocaleString()}</span>
                    </div>
                    <div class="trend-bars">
                        ${last7Days.dailyData.map(day => `
                            <div class="trend-bar-container">
                                <div class="trend-bar" style="height: ${(day.revenue / last7Days.maxRevenue) * 100}%"></div>
                                <div class="trend-label">${day.label}</div>
                                <div class="trend-value">PKR ${day.revenue.toLocaleString()}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load revenue trends</p>';
        }
    }

    /**
     * Render expense breakdown
     */
    renderExpenseBreakdown() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const expenses = this.safeGetFinancialData('expenses', startDate, endDate);

            if (!expenses.expensesByCategory || Object.keys(expenses.expensesByCategory).length === 0) {
                return '<p class="no-data">No expense data available</p>';
            }

            const categories = Object.entries(expenses.expensesByCategory)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 6);

            return `
                <div class="expense-breakdown">
                    <div class="breakdown-header">
                        <span>Expense Categories</span>
                        <span class="breakdown-total">Total: PKR ${expenses.totalExpenses.toLocaleString()}</span>
                    </div>
                    <div class="breakdown-items">
                        ${categories.map(([category, amount]) => {
                            const percentage = ((amount / expenses.totalExpenses) * 100).toFixed(1);
                            return `
                                <div class="breakdown-item">
                                    <div class="breakdown-info">
                                        <span class="category-name">${category}</span>
                                        <span class="category-amount">PKR ${amount.toLocaleString()}</span>
                                    </div>
                                    <div class="breakdown-bar">
                                        <div class="breakdown-fill" style="width: ${percentage}%"></div>
                                    </div>
                                    <span class="breakdown-percentage">${percentage}%</span>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load expense breakdown</p>';
        }
    }

    /**
     * Get last 7 days data
     */
    getLast7DaysData() {
        const dailyData = [];
        let totalRevenue = 0;
        let maxRevenue = 0;

        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            try {
                const dayRevenue = this.safeGetFinancialData('revenue', dateStr, dateStr);
                const revenue = dayRevenue.totalRevenue || 0;

                dailyData.push({
                    date: dateStr,
                    label: date.toLocaleDateString('en-US', { weekday: 'short' }),
                    revenue: revenue
                });

                totalRevenue += revenue;
                maxRevenue = Math.max(maxRevenue, revenue);
            } catch (error) {
                dailyData.push({
                    date: dateStr,
                    label: date.toLocaleDateString('en-US', { weekday: 'short' }),
                    revenue: 0
                });
            }
        }

        return { dailyData, totalRevenue, maxRevenue: maxRevenue || 1 };
    }

    /**
     * Render custom date inputs
     */
    renderCustomDateInputs() {
        const today = new Date().toISOString().split('T')[0];
        const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        return `
            <div class="custom-date-inputs">
                <div class="date-input-group">
                    <label>Start Date:</label>
                    <input type="date" 
                           id="customStartDate" 
                           value="${this.customStartDate || lastWeek}"
                           onchange="zaiqaReports.updateCustomDate('start', this.value)">
                </div>
                <div class="date-input-group">
                    <label>End Date:</label>
                    <input type="date" 
                           id="customEndDate" 
                           value="${this.customEndDate || today}"
                           onchange="zaiqaReports.updateCustomDate('end', this.value)">
                </div>
                <button onclick="zaiqaReports.generateCustomReport()" class="btn btn-primary">
                    Generate Report
                </button>
            </div>
        `;
    }

    /**
     * Render KPI cards
     */
    renderKPICards(todayMetrics, udharSummary, inventoryValue) {
        const revenue = todayMetrics.revenue || {};
        const expenses = todayMetrics.expenses || {};
        const profitLoss = todayMetrics.profitLoss || {};

        return `
            <div class="kpi-card revenue">
                <div class="kpi-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="kpi-content">
                    <h4>Total Revenue</h4>
                    <p class="kpi-value">PKR ${(revenue.totalRevenue || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${revenue.orderCount || 0} orders</span>
                </div>
            </div>

            <div class="kpi-card expenses">
                <div class="kpi-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="kpi-content">
                    <h4>Total Expenses</h4>
                    <p class="kpi-value">PKR ${(expenses.totalExpenses || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${expenses.expenseCount || 0} transactions</span>
                </div>
            </div>

            <div class="kpi-card profit ${profitLoss.isProfit ? 'positive' : 'negative'}">
                <div class="kpi-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="kpi-content">
                    <h4>Net Profit</h4>
                    <p class="kpi-value">PKR ${(profitLoss.grossProfit || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${(profitLoss.profitMargin || 0).toFixed(1)}% margin</span>
                </div>
            </div>

            <div class="kpi-card udhar">
                <div class="kpi-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="kpi-content">
                    <h4>Outstanding Udhar</h4>
                    <p class="kpi-value">PKR ${(udharSummary.totalRemainingAmount || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${udharSummary.activeCustomers || 0} customers</span>
                </div>
            </div>

            <div class="kpi-card inventory">
                <div class="kpi-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="kpi-content">
                    <h4>Inventory Value</h4>
                    <p class="kpi-value">PKR ${(inventoryValue.totalValue || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${inventoryValue.totalItems || 0} items</span>
                </div>
            </div>

            <div class="kpi-card average">
                <div class="kpi-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="kpi-content">
                    <h4>Average Order</h4>
                    <p class="kpi-value">PKR ${(revenue.averageOrderValue || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">per order</span>
                </div>
            </div>
        `;
    }

    /**
     * Render cost analysis
     */
    renderCostAnalysis() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const metrics = this.safeGetFinancialData('periodMetrics', startDate, endDate);

            const costPerOrder = metrics.revenue.orderCount > 0 ?
                (metrics.expenses.totalExpenses / metrics.revenue.orderCount) : 0;

            const costPercentage = metrics.revenue.totalRevenue > 0 ?
                ((metrics.expenses.totalExpenses / metrics.revenue.totalRevenue) * 100) : 0;

            return `
                <div class="cost-analysis">
                    <div class="cost-metric">
                        <label>Cost per Order:</label>
                        <span class="cost-value">PKR ${costPerOrder.toFixed(2)}</span>
                    </div>
                    <div class="cost-metric">
                        <label>Cost Percentage:</label>
                        <span class="cost-value ${costPercentage > 70 ? 'high-cost' : costPercentage > 50 ? 'medium-cost' : 'low-cost'}">
                            ${costPercentage.toFixed(1)}%
                        </span>
                    </div>
                    <div class="cost-metric">
                        <label>Daily Avg Cost:</label>
                        <span class="cost-value">PKR ${(metrics.dailyAverages?.expenses || 0).toLocaleString()}</span>
                    </div>
                    <div class="cost-recommendation">
                        ${this.getCostRecommendation(costPercentage)}
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load cost analysis</p>';
        }
    }

    /**
     * Get cost recommendation
     */
    getCostRecommendation(costPercentage) {
        if (costPercentage > 70) {
            return `
                <div class="recommendation high-priority">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>High Cost Alert:</strong> Your costs are above 70%. Consider reviewing expenses and optimizing operations.
                </div>
            `;
        } else if (costPercentage > 50) {
            return `
                <div class="recommendation medium-priority">
                    <i class="fas fa-info-circle"></i>
                    <strong>Moderate Costs:</strong> Monitor expenses closely and look for optimization opportunities.
                </div>
            `;
        } else {
            return `
                <div class="recommendation low-priority">
                    <i class="fas fa-check-circle"></i>
                    <strong>Good Cost Control:</strong> Your cost management is on track. Keep monitoring.
                </div>
            `;
        }
    }

    /**
     * Render profit margins
     */
    renderProfitMargins() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const metrics = this.safeGetFinancialData('periodMetrics', startDate, endDate);

            const grossMargin = metrics.profitLoss.profitMargin || 0;
            const targetMargin = 25; // Target 25% profit margin

            return `
                <div class="profit-margins">
                    <div class="margin-gauge">
                        <div class="gauge-container">
                            <div class="gauge-fill" style="width: ${Math.min(grossMargin, 100)}%"></div>
                            <div class="gauge-target" style="left: ${targetMargin}%"></div>
                        </div>
                        <div class="gauge-labels">
                            <span>0%</span>
                            <span class="target-label">Target: ${targetMargin}%</span>
                            <span>100%</span>
                        </div>
                    </div>
                    <div class="margin-details">
                        <div class="margin-item">
                            <label>Current Margin:</label>
                            <span class="margin-value ${grossMargin >= targetMargin ? 'good' : 'needs-improvement'}">
                                ${grossMargin.toFixed(1)}%
                            </span>
                        </div>
                        <div class="margin-item">
                            <label>Target Margin:</label>
                            <span class="margin-value">${targetMargin}%</span>
                        </div>
                        <div class="margin-item">
                            <label>Difference:</label>
                            <span class="margin-value ${grossMargin >= targetMargin ? 'positive' : 'negative'}">
                                ${(grossMargin - targetMargin).toFixed(1)}%
                            </span>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load profit margins</p>';
        }
    }

    /**
     * Render performance metrics
     */
    renderPerformanceMetrics() {
        try {
            const todayMetrics = this.safeGetFinancialData('todayMetrics');
            const last7Days = this.getLast7DaysData();

            const avgDailyRevenue = last7Days.totalRevenue / 7;
            const todayVsAvg = todayMetrics.revenue.totalRevenue / avgDailyRevenue;

            return `
                <div class="performance-metrics">
                    <div class="metric-item">
                        <i class="fas fa-chart-line"></i>
                        <div class="metric-content">
                            <label>Today vs 7-Day Avg</label>
                            <span class="metric-value ${todayVsAvg >= 1 ? 'positive' : 'negative'}">
                                ${((todayVsAvg - 1) * 100).toFixed(1)}%
                            </span>
                        </div>
                    </div>
                    <div class="metric-item">
                        <i class="fas fa-clock"></i>
                        <div class="metric-content">
                            <label>Avg Order Processing</label>
                            <span class="metric-value">~15 mins</span>
                        </div>
                    </div>
                    <div class="metric-item">
                        <i class="fas fa-star"></i>
                        <div class="metric-content">
                            <label>Customer Satisfaction</label>
                            <span class="metric-value positive">4.5/5</span>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load performance metrics</p>';
        }
    }

    /**
     * Render top selling items
     */
    renderTopItems() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const topItems = this.zaiqaSystem.getFinancialData('topItems', startDate, endDate, 10);

            if (!topItems || topItems.length === 0) {
                return '<p class="no-data">No sales data available for this period</p>';
            }

            return topItems.map((item, index) => `
                <div class="top-item">
                    <div class="item-rank">${index + 1}</div>
                    <div class="item-details">
                        <h5>${item.name}</h5>
                        <p>Sold: ${item.quantity} | Revenue: PKR ${item.revenue.toLocaleString()}</p>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('❌ Failed to render top items:', error);
            return '<p class="error-message">Failed to load top items data</p>';
        }
    }

    /**
     * Render cost optimization suggestions
     */
    renderCostOptimization() {
        try {
            const suggestions = this.generateCostOptimizationSuggestions();

            return `
                <div class="optimization-suggestions">
                    ${suggestions.map(suggestion => `
                        <div class="suggestion-item ${suggestion.priority}">
                            <div class="suggestion-icon">
                                <i class="${suggestion.icon}"></i>
                            </div>
                            <div class="suggestion-content">
                                <h5>${suggestion.title}</h5>
                                <p>${suggestion.description}</p>
                                <div class="suggestion-impact">
                                    Potential Savings: <strong>PKR ${suggestion.savings.toLocaleString()}/month</strong>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load cost optimization suggestions</p>';
        }
    }

    /**
     * Generate cost optimization suggestions
     */
    generateCostOptimizationSuggestions() {
        const suggestions = [];
        const todayMetrics = this.safeGetFinancialData('todayMetrics');
        const expenses = todayMetrics.expenses;

        // High utility costs
        if (expenses.expensesByCategory['Utilities'] > 15000) {
            suggestions.push({
                priority: 'high',
                icon: 'fas fa-bolt',
                title: 'Reduce Utility Costs',
                description: 'Your utility expenses are high. Consider energy-efficient equipment and LED lighting.',
                savings: 5000
            });
        }

        // Food waste analysis
        if (expenses.expensesByCategory['Food Supplies'] > 50000) {
            suggestions.push({
                priority: 'medium',
                icon: 'fas fa-leaf',
                title: 'Minimize Food Waste',
                description: 'Implement better inventory management to reduce food waste and spoilage.',
                savings: 8000
            });
        }

        // Staff optimization
        suggestions.push({
            priority: 'low',
            icon: 'fas fa-users',
            title: 'Optimize Staff Scheduling',
            description: 'Analyze peak hours and adjust staff schedules to reduce labor costs.',
            savings: 12000
        });

        return suggestions;
    }

    /**
     * Render revenue opportunities
     */
    renderRevenueOpportunities() {
        try {
            const opportunities = this.generateRevenueOpportunities();

            return `
                <div class="revenue-opportunities">
                    ${opportunities.map(opportunity => `
                        <div class="opportunity-item ${opportunity.priority}">
                            <div class="opportunity-icon">
                                <i class="${opportunity.icon}"></i>
                            </div>
                            <div class="opportunity-content">
                                <h5>${opportunity.title}</h5>
                                <p>${opportunity.description}</p>
                                <div class="opportunity-impact">
                                    Potential Revenue: <strong>PKR ${opportunity.revenue.toLocaleString()}/month</strong>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load revenue opportunities</p>';
        }
    }

    /**
     * Generate revenue opportunities
     */
    generateRevenueOpportunities() {
        const opportunities = [];
        const todayMetrics = this.safeGetFinancialData('todayMetrics');

        // Upselling opportunities
        opportunities.push({
            priority: 'high',
            icon: 'fas fa-arrow-up',
            title: 'Upselling & Cross-selling',
            description: 'Train staff to suggest complementary items and premium options to increase average order value.',
            revenue: 25000
        });

        // Peak hour optimization
        opportunities.push({
            priority: 'medium',
            icon: 'fas fa-clock',
            title: 'Peak Hour Promotions',
            description: 'Offer special deals during off-peak hours to increase customer traffic.',
            revenue: 18000
        });

        // Delivery expansion
        opportunities.push({
            priority: 'medium',
            icon: 'fas fa-motorcycle',
            title: 'Expand Delivery Service',
            description: 'Increase delivery radius and partner with food delivery platforms.',
            revenue: 35000
        });

        return opportunities;
    }

    /**
     * Render risk analysis
     */
    renderRiskAnalysis() {
        try {
            const risks = this.generateRiskAnalysis();

            return `
                <div class="risk-analysis">
                    ${risks.map(risk => `
                        <div class="risk-item ${risk.level}">
                            <div class="risk-icon">
                                <i class="${risk.icon}"></i>
                            </div>
                            <div class="risk-content">
                                <h5>${risk.title}</h5>
                                <p>${risk.description}</p>
                                <div class="risk-level">
                                    Risk Level: <strong class="${risk.level}">${risk.level.toUpperCase()}</strong>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load risk analysis</p>';
        }
    }

    /**
     * Generate risk analysis
     */
    generateRiskAnalysis() {
        const risks = [];
        const todayMetrics = this.safeGetFinancialData('todayMetrics');
        const udharSummary = this.safeGetFinancialData('udharSummary');

        // High Udhar risk
        if (udharSummary.totalRemainingAmount > 100000) {
            risks.push({
                level: 'high',
                icon: 'fas fa-exclamation-triangle',
                title: 'High Outstanding Udhar',
                description: 'Outstanding credit amount is high. Implement stricter credit policies and follow up on collections.'
            });
        }

        // Cash flow risk
        if (todayMetrics.cashFlow.cashDifference < -5000) {
            risks.push({
                level: 'medium',
                icon: 'fas fa-money-bill-wave',
                title: 'Cash Flow Discrepancy',
                description: 'There\'s a significant difference between expected and actual cash. Review cash handling procedures.'
            });
        }

        // Low profit margin risk
        if (todayMetrics.profitLoss.profitMargin < 15) {
            risks.push({
                level: 'medium',
                icon: 'fas fa-chart-line',
                title: 'Low Profit Margins',
                description: 'Profit margins are below optimal levels. Review pricing strategy and cost management.'
            });
        }

        return risks;
    }

    /**
     * Render cash flow analysis
     */
    renderCashFlowAnalysis(cashFlow) {
        if (!cashFlow) {
            return '<p class="no-data">No cash flow data available</p>';
        }

        return `
            <div class="cash-flow-summary">
                <div class="cash-flow-item">
                    <label>Opening Balance:</label>
                    <span>PKR ${(cashFlow.openingBalance || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Day Revenue:</label>
                    <span class="positive">+PKR ${(cashFlow.dayRevenue || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Day Expenses:</label>
                    <span class="negative">-PKR ${(cashFlow.dayExpenses || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Expected Closing:</label>
                    <span>PKR ${(cashFlow.expectedClosing || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Actual Closing:</label>
                    <span>PKR ${(cashFlow.closingBalance || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item total">
                    <label>Cash Difference:</label>
                    <span class="${cashFlow.cashDifference >= 0 ? 'positive' : 'negative'}">
                        PKR ${(cashFlow.cashDifference || 0).toLocaleString()}
                    </span>
                </div>
            </div>
        `;
    }

    /**
     * Render recent orders table
     */
    renderRecentOrdersTable() {
        try {
            const orders = this.zaiqaSystem.getData('orders', { status: 'completed' })
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 10);

            if (orders.length === 0) {
                return '<p class="no-data">No recent orders found</p>';
            }

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Items</th>
                            <th>Amount</th>
                            <th>Payment</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map(order => `
                            <tr>
                                <td>${order.orderNumber}</td>
                                <td>${order.customerName || 'Walk-in'}</td>
                                <td>${order.items ? order.items.length : 0} items</td>
                                <td>PKR ${(order.totalAmount || 0).toLocaleString()}</td>
                                <td class="capitalize">${order.paymentMethod}</td>
                                <td>${new Date(order.createdAt).toLocaleDateString()}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render recent orders table:', error);
            return '<p class="error-message">Failed to load recent orders</p>';
        }
    }

    /**
     * Render expense summary table
     */
    renderExpenseSummaryTable() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const expenses = this.zaiqaSystem.getFinancialData('expenses', startDate, endDate);

            if (!expenses.expensesByCategory || Object.keys(expenses.expensesByCategory).length === 0) {
                return '<p class="no-data">No expenses found for this period</p>';
            }

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Amount</th>
                            <th>Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(expenses.expensesByCategory).map(([category, amount]) => {
                            const percentage = expenses.totalExpenses > 0 ? 
                                ((amount / expenses.totalExpenses) * 100).toFixed(1) : 0;
                            return `
                                <tr>
                                    <td class="capitalize">${category}</td>
                                    <td>PKR ${amount.toLocaleString()}</td>
                                    <td>${percentage}%</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render expense summary table:', error);
            return '<p class="error-message">Failed to load expense summary</p>';
        }
    }

    /**
     * Render staff performance table
     */
    renderStaffPerformanceTable() {
        try {
            const staff = this.zaiqaSystem ? this.zaiqaSystem.getData('staff') : [];

            if (staff.length === 0) {
                return '<p class="no-data">No staff data available</p>';
            }

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Monthly Salary</th>
                            <th>Performance</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${staff.map(member => `
                            <tr>
                                <td>${member.name}</td>
                                <td class="capitalize">${member.position}</td>
                                <td>PKR ${(member.monthlySalary || 0).toLocaleString()}</td>
                                <td>
                                    <div class="performance-rating">
                                        <span class="rating-stars">★★★★☆</span>
                                        <span class="rating-text">Good</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge ${member.isActive ? 'active' : 'inactive'}">
                                        ${member.isActive ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render staff performance table:', error);
            return '<p class="error-message">Failed to load staff performance</p>';
        }
    }

    /**
     * Render inventory status table
     */
    renderInventoryStatusTable() {
        try {
            const inventory = this.zaiqaSystem ? this.zaiqaSystem.getData('inventory') : [];

            if (inventory.length === 0) {
                return '<p class="no-data">No inventory data available</p>';
            }

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Quantity</th>
                            <th>Unit</th>
                            <th>Status</th>
                            <th>Action Needed</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${inventory.map(item => {
                            const status = this.getInventoryStatus(item);
                            return `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity || 0}</td>
                                    <td>${item.unit || 'pcs'}</td>
                                    <td>
                                        <span class="status-badge ${status.class}">
                                            ${status.text}
                                        </span>
                                    </td>
                                    <td>${status.action}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render inventory status table:', error);
            return '<p class="error-message">Failed to load inventory status</p>';
        }
    }

    /**
     * Get inventory status
     */
    getInventoryStatus(item) {
        const quantity = item.quantity || 0;
        const threshold = item.lowStockThreshold || 0;

        if (quantity <= 0) {
            return {
                class: 'out-of-stock',
                text: 'Out of Stock',
                action: 'Restock Immediately'
            };
        } else if (quantity <= threshold) {
            return {
                class: 'low-stock',
                text: 'Low Stock',
                action: 'Restock Soon'
            };
        } else {
            return {
                class: 'in-stock',
                text: 'In Stock',
                action: 'Monitor'
            };
        }
    }

    /**
     * Render payment methods table
     */
    renderPaymentMethodsTable() {
        try {
            const orders = this.zaiqaSystem ? this.zaiqaSystem.getData('orders', { status: 'completed' }) : [];

            if (orders.length === 0) {
                return '<p class="no-data">No payment data available</p>';
            }

            const paymentStats = this.calculatePaymentMethodStats(orders);

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Payment Method</th>
                            <th>Orders</th>
                            <th>Total Amount</th>
                            <th>Percentage</th>
                            <th>Avg Order Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(paymentStats).map(([method, stats]) => `
                            <tr>
                                <td class="capitalize">
                                    <i class="${this.getPaymentIcon(method)}"></i>
                                    ${method}
                                </td>
                                <td>${stats.count}</td>
                                <td>PKR ${stats.total.toLocaleString()}</td>
                                <td>${stats.percentage.toFixed(1)}%</td>
                                <td>PKR ${stats.average.toLocaleString()}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render payment methods table:', error);
            return '<p class="error-message">Failed to load payment methods</p>';
        }
    }

    /**
     * Calculate payment method statistics
     */
    calculatePaymentMethodStats(orders) {
        const stats = {};
        let totalAmount = 0;

        orders.forEach(order => {
            const method = order.paymentMethod || 'cash';
            const amount = order.totalAmount || 0;

            if (!stats[method]) {
                stats[method] = { count: 0, total: 0 };
            }

            stats[method].count++;
            stats[method].total += amount;
            totalAmount += amount;
        });

        // Calculate percentages and averages
        Object.keys(stats).forEach(method => {
            stats[method].percentage = totalAmount > 0 ? (stats[method].total / totalAmount) * 100 : 0;
            stats[method].average = stats[method].count > 0 ? stats[method].total / stats[method].count : 0;
        });

        return stats;
    }

    /**
     * Get payment method icon
     */
    getPaymentIcon(method) {
        const icons = {
            cash: 'fas fa-money-bill',
            card: 'fas fa-credit-card',
            online: 'fas fa-globe',
            udhar: 'fas fa-handshake'
        };
        return icons[method] || 'fas fa-money-bill';
    }

    /**
     * Render Udhar management table
     */
    renderUdharManagementTable() {
        try {
            const udhars = this.zaiqaSystem ? this.zaiqaSystem.getData('udhars') : [];

            if (udhars.length === 0) {
                return '<p class="no-data">No Udhar records found</p>';
            }

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Phone</th>
                            <th>Total Amount</th>
                            <th>Paid Amount</th>
                            <th>Remaining</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${udhars.map(udhar => {
                            const remaining = udhar.remainingAmount || 0;
                            const status = remaining <= 0 ? 'paid' : remaining > 50000 ? 'high-risk' : 'active';

                            return `
                                <tr>
                                    <td>${udhar.customerName}</td>
                                    <td>${udhar.phone || 'N/A'}</td>
                                    <td>PKR ${(udhar.totalAmount || 0).toLocaleString()}</td>
                                    <td>PKR ${(udhar.paidAmount || 0).toLocaleString()}</td>
                                    <td>PKR ${remaining.toLocaleString()}</td>
                                    <td>
                                        <span class="status-badge ${status}">
                                            ${status === 'paid' ? 'Paid' : status === 'high-risk' ? 'High Risk' : 'Active'}
                                        </span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render Udhar management table:', error);
            return '<p class="error-message">Failed to load Udhar management</p>';
        }
    }

    /**
     * Render daily Hisab Kitab
     */
    renderDailyHisabKitab() {
        try {
            const todayMetrics = this.safeGetFinancialData('todayMetrics');

            return `
                <div class="daily-hisab-kitab">
                    <div class="hisab-summary">
                        <div class="hisab-item">
                            <label>Opening Balance:</label>
                            <span>PKR ${(todayMetrics.cashFlow.openingBalance || 0).toLocaleString()}</span>
                        </div>
                        <div class="hisab-item">
                            <label>Total Sales:</label>
                            <span class="positive">+PKR ${(todayMetrics.revenue.totalRevenue || 0).toLocaleString()}</span>
                        </div>
                        <div class="hisab-item">
                            <label>Total Expenses:</label>
                            <span class="negative">-PKR ${(todayMetrics.expenses.totalExpenses || 0).toLocaleString()}</span>
                        </div>
                        <div class="hisab-item">
                            <label>Net Profit:</label>
                            <span class="${todayMetrics.profitLoss.isProfit ? 'positive' : 'negative'}">
                                PKR ${(todayMetrics.profitLoss.grossProfit || 0).toLocaleString()}
                            </span>
                        </div>
                        <div class="hisab-item total">
                            <label>Expected Closing:</label>
                            <span>PKR ${(todayMetrics.cashFlow.expectedClosing || 0).toLocaleString()}</span>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load daily Hisab Kitab</p>';
        }
    }

    /**
     * Render weekly analysis
     */
    renderWeeklyAnalysis() {
        try {
            const last7Days = this.getLast7DaysData();
            const avgDaily = last7Days.totalRevenue / 7;

            return `
                <div class="weekly-analysis">
                    <div class="weekly-stats">
                        <div class="weekly-stat">
                            <label>Total Revenue:</label>
                            <span>PKR ${last7Days.totalRevenue.toLocaleString()}</span>
                        </div>
                        <div class="weekly-stat">
                            <label>Daily Average:</label>
                            <span>PKR ${avgDaily.toLocaleString()}</span>
                        </div>
                        <div class="weekly-stat">
                            <label>Best Day:</label>
                            <span>${this.getBestDay(last7Days.dailyData)}</span>
                        </div>
                        <div class="weekly-stat">
                            <label>Growth Trend:</label>
                            <span class="positive">+5.2%</span>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load weekly analysis</p>';
        }
    }

    /**
     * Get best performing day
     */
    getBestDay(dailyData) {
        if (!dailyData || dailyData.length === 0) return 'N/A';

        const bestDay = dailyData.reduce((best, current) =>
            current.revenue > best.revenue ? current : best
        );

        return `${bestDay.label} (PKR ${bestDay.revenue.toLocaleString()})`;
    }

    /**
     * Render monthly summary
     */
    renderMonthlySummary() {
        try {
            const startOfMonth = new Date();
            startOfMonth.setDate(1);
            const endOfMonth = new Date();

            const monthlyMetrics = this.safeGetFinancialData('periodMetrics',
                startOfMonth.toISOString().split('T')[0],
                endOfMonth.toISOString().split('T')[0]
            );

            return `
                <div class="monthly-summary">
                    <div class="monthly-stats">
                        <div class="monthly-stat">
                            <label>Monthly Revenue:</label>
                            <span>PKR ${(monthlyMetrics.revenue?.totalRevenue || 0).toLocaleString()}</span>
                        </div>
                        <div class="monthly-stat">
                            <label>Monthly Expenses:</label>
                            <span>PKR ${(monthlyMetrics.expenses?.totalExpenses || 0).toLocaleString()}</span>
                        </div>
                        <div class="monthly-stat">
                            <label>Monthly Profit:</label>
                            <span class="${monthlyMetrics.profitLoss?.isProfit ? 'positive' : 'negative'}">
                                PKR ${(monthlyMetrics.profitLoss?.grossProfit || 0).toLocaleString()}
                            </span>
                        </div>
                        <div class="monthly-stat">
                            <label>Profit Margin:</label>
                            <span>${(monthlyMetrics.profitLoss?.profitMargin || 0).toFixed(1)}%</span>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load monthly summary</p>';
        }
    }

    /**
     * Render custom period report
     */
    renderCustomPeriodReport() {
        return `
            <div class="custom-period-report">
                <p>Select a custom date range above and click "Generate Custom Report" to view detailed analytics for your chosen period.</p>
                <div class="report-features">
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <span>Revenue & Expense Trends</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-pie-chart"></i>
                        <span>Category Breakdown</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-trophy"></i>
                        <span>Top Performing Items</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-calculator"></i>
                        <span>Profitability Analysis</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render budget tracker
     */
    renderBudgetTracker() {
        try {
            const todayMetrics = this.safeGetFinancialData('todayMetrics');
            const monthlyBudget = 200000; // Example monthly budget
            const currentSpend = todayMetrics.expenses.totalExpenses * 30; // Estimate monthly spend
            const budgetUsed = (currentSpend / monthlyBudget) * 100;

            return `
                <div class="budget-tracker">
                    <div class="budget-progress">
                        <div class="budget-bar">
                            <div class="budget-fill" style="width: ${Math.min(budgetUsed, 100)}%"></div>
                        </div>
                        <div class="budget-labels">
                            <span>PKR 0</span>
                            <span>PKR ${monthlyBudget.toLocaleString()}</span>
                        </div>
                    </div>
                    <div class="budget-stats">
                        <div class="budget-stat">
                            <label>Budget Used:</label>
                            <span class="${budgetUsed > 80 ? 'warning' : 'normal'}">${budgetUsed.toFixed(1)}%</span>
                        </div>
                        <div class="budget-stat">
                            <label>Remaining:</label>
                            <span>PKR ${(monthlyBudget - currentSpend).toLocaleString()}</span>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load budget tracker</p>';
        }
    }

    /**
     * Render cost alerts
     */
    renderCostAlerts() {
        try {
            const alerts = this.generateCostAlerts();

            if (alerts.length === 0) {
                return '<p class="no-alerts">No cost alerts at this time</p>';
            }

            return `
                <div class="cost-alerts">
                    ${alerts.map(alert => `
                        <div class="alert-item ${alert.level}">
                            <div class="alert-icon">
                                <i class="${alert.icon}"></i>
                            </div>
                            <div class="alert-content">
                                <h5>${alert.title}</h5>
                                <p>${alert.message}</p>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load cost alerts</p>';
        }
    }

    /**
     * Generate cost alerts
     */
    generateCostAlerts() {
        const alerts = [];
        const todayMetrics = this.safeGetFinancialData('todayMetrics');

        // High expense alert
        if (todayMetrics.expenses.totalExpenses > 20000) {
            alerts.push({
                level: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: 'High Daily Expenses',
                message: 'Today\'s expenses are above average. Review spending patterns.'
            });
        }

        // Low profit margin alert
        if (todayMetrics.profitLoss.profitMargin < 15) {
            alerts.push({
                level: 'danger',
                icon: 'fas fa-chart-line',
                title: 'Low Profit Margin',
                message: 'Profit margin is below 15%. Consider cost optimization.'
            });
        }

        return alerts;
    }

    /**
     * Render expense forecast
     */
    renderExpenseForecast() {
        try {
            const todayMetrics = this.safeGetFinancialData('todayMetrics');
            const dailyAvg = todayMetrics.expenses.totalExpenses;

            const forecasts = {
                weekly: dailyAvg * 7,
                monthly: dailyAvg * 30,
                quarterly: dailyAvg * 90
            };

            return `
                <div class="expense-forecast">
                    <div class="forecast-item">
                        <label>Weekly Forecast:</label>
                        <span>PKR ${forecasts.weekly.toLocaleString()}</span>
                    </div>
                    <div class="forecast-item">
                        <label>Monthly Forecast:</label>
                        <span>PKR ${forecasts.monthly.toLocaleString()}</span>
                    </div>
                    <div class="forecast-item">
                        <label>Quarterly Forecast:</label>
                        <span>PKR ${forecasts.quarterly.toLocaleString()}</span>
                    </div>
                    <div class="forecast-note">
                        <i class="fas fa-info-circle"></i>
                        Forecasts based on current daily average spending
                    </div>
                </div>
            `;
        } catch (error) {
            return '<p class="error-message">Failed to load expense forecast</p>';
        }
    }

    /**
     * Render ROI calculator
     */
    renderROICalculator() {
        return `
            <div class="roi-calculator">
                <div class="roi-inputs">
                    <div class="input-group">
                        <label>Investment Amount:</label>
                        <input type="number" id="investmentAmount" placeholder="Enter amount">
                    </div>
                    <div class="input-group">
                        <label>Expected Return:</label>
                        <input type="number" id="expectedReturn" placeholder="Enter return">
                    </div>
                    <button onclick="zaiqaReports.calculateROI()" class="btn btn-primary">
                        Calculate ROI
                    </button>
                </div>
                <div id="roiResult" class="roi-result">
                    <p>Enter investment details to calculate ROI</p>
                </div>
            </div>
        `;
    }

    /**
     * Render financial summary
     */
    renderFinancialSummary(metrics) {
        const revenue = metrics.revenue || {};
        const expenses = metrics.expenses || {};
        const profitLoss = metrics.profitLoss || {};

        return `
            <div class="financial-summary-grid">
                <div class="summary-section">
                    <h4>Revenue Analysis</h4>
                    <div class="summary-items">
                        <div class="summary-item">
                            <span>Order Revenue:</span>
                            <span>PKR ${(revenue.orderRevenue || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Udhar Payments:</span>
                            <span>PKR ${(revenue.udharPayments || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Average Order Value:</span>
                            <span>PKR ${(revenue.averageOrderValue || 0).toLocaleString()}</span>
                        </div>
                    </div>
                </div>

                <div class="summary-section">
                    <h4>Expense Analysis</h4>
                    <div class="summary-items">
                        <div class="summary-item">
                            <span>Total Expenses:</span>
                            <span>PKR ${(expenses.totalExpenses || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Average Expense:</span>
                            <span>PKR ${(expenses.averageExpense || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Expense Count:</span>
                            <span>${expenses.expenseCount || 0} transactions</span>
                        </div>
                    </div>
                </div>

                <div class="summary-section">
                    <h4>Profitability</h4>
                    <div class="summary-items">
                        <div class="summary-item">
                            <span>Gross Profit:</span>
                            <span class="${profitLoss.isProfit ? 'positive' : 'negative'}">
                                PKR ${(profitLoss.grossProfit || 0).toLocaleString()}
                            </span>
                        </div>
                        <div class="summary-item">
                            <span>Profit Margin:</span>
                            <span class="${profitLoss.isProfit ? 'positive' : 'negative'}">
                                ${(profitLoss.profitMargin || 0).toFixed(1)}%
                            </span>
                        </div>
                        <div class="summary-item">
                            <span>Status:</span>
                            <span class="${profitLoss.isProfit ? 'positive' : 'negative'}">
                                ${profitLoss.isProfit ? 'Profitable' : 'Loss'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get start date based on current period
     */
    getStartDate() {
        const today = new Date();
        
        switch (this.currentPeriod) {
            case 'today':
                return today.toISOString().split('T')[0];
            case 'week':
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                return weekStart.toISOString().split('T')[0];
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return monthStart.toISOString().split('T')[0];
            case 'custom':
                return this.customStartDate || today.toISOString().split('T')[0];
            default:
                return today.toISOString().split('T')[0];
        }
    }

    /**
     * Get end date based on current period
     */
    getEndDate() {
        const today = new Date();
        
        switch (this.currentPeriod) {
            case 'today':
                return today.toISOString().split('T')[0];
            case 'week':
                const weekEnd = new Date(today);
                weekEnd.setDate(today.getDate() + (6 - today.getDay()));
                return weekEnd.toISOString().split('T')[0];
            case 'month':
                const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                return monthEnd.toISOString().split('T')[0];
            case 'custom':
                return this.customEndDate || today.toISOString().split('T')[0];
            default:
                return today.toISOString().split('T')[0];
        }
    }

    /**
     * Set report period
     */
    setPeriod(period) {
        this.currentPeriod = period;
        this.refreshReports();
    }

    /**
     * Update custom date
     */
    updateCustomDate(type, value) {
        if (type === 'start') {
            this.customStartDate = value;
        } else {
            this.customEndDate = value;
        }
    }

    /**
     * Generate custom report
     */
    generateCustomReport() {
        if (!this.customStartDate || !this.customEndDate) {
            alert('Please select both start and end dates');
            return;
        }
        
        this.refreshReports();
    }

    /**
     * Refresh reports
     */
    refreshReports() {
        const reportsPage = document.getElementById('reportsPage');
        if (reportsPage) {
            this.render(reportsPage);
        }
    }

    /**
     * Export reports
     */
    exportReports() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const metrics = this.zaiqaSystem.getFinancialData('periodMetrics', startDate, endDate);
            
            const exportData = {
                period: this.currentPeriod,
                startDate: startDate,
                endDate: endDate,
                metrics: metrics,
                exportedAt: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-financial-report-${startDate}-to-${endDate}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log('✅ Reports exported successfully');

        } catch (error) {
            console.error('❌ Failed to export reports:', error);
            alert('Failed to export reports. Please try again.');
        }
    }

    /**
     * Action Methods for User Interactions
     */

    /**
     * Show cost analysis modal
     */
    showCostAnalysis() {
        alert('Cost Analysis feature will open detailed cost breakdown and optimization suggestions.');
    }

    /**
     * Generate daily report
     */
    generateDailyReport() {
        try {
            const todayMetrics = this.safeGetFinancialData('todayMetrics');
            const reportData = {
                date: new Date().toISOString().split('T')[0],
                metrics: todayMetrics,
                generatedAt: new Date().toISOString()
            };

            this.downloadReport(reportData, 'daily-report');
        } catch (error) {
            console.error('❌ Failed to generate daily report:', error);
            alert('Failed to generate daily report. Please try again.');
        }
    }

    /**
     * Generate weekly report
     */
    generateWeeklyReport() {
        try {
            const weekStart = new Date();
            weekStart.setDate(weekStart.getDate() - weekStart.getDay());
            const weekEnd = new Date();

            const weeklyMetrics = this.safeGetFinancialData('periodMetrics',
                weekStart.toISOString().split('T')[0],
                weekEnd.toISOString().split('T')[0]
            );

            const reportData = {
                period: 'weekly',
                startDate: weekStart.toISOString().split('T')[0],
                endDate: weekEnd.toISOString().split('T')[0],
                metrics: weeklyMetrics,
                generatedAt: new Date().toISOString()
            };

            this.downloadReport(reportData, 'weekly-report');
        } catch (error) {
            console.error('❌ Failed to generate weekly report:', error);
            alert('Failed to generate weekly report. Please try again.');
        }
    }

    /**
     * Generate monthly report
     */
    generateMonthlyReport() {
        try {
            const monthStart = new Date();
            monthStart.setDate(1);
            const monthEnd = new Date();

            const monthlyMetrics = this.safeGetFinancialData('periodMetrics',
                monthStart.toISOString().split('T')[0],
                monthEnd.toISOString().split('T')[0]
            );

            const reportData = {
                period: 'monthly',
                startDate: monthStart.toISOString().split('T')[0],
                endDate: monthEnd.toISOString().split('T')[0],
                metrics: monthlyMetrics,
                generatedAt: new Date().toISOString()
            };

            this.downloadReport(reportData, 'monthly-report');
        } catch (error) {
            console.error('❌ Failed to generate monthly report:', error);
            alert('Failed to generate monthly report. Please try again.');
        }
    }

    /**
     * Generate custom period report
     */
    generateCustomPeriodReport() {
        if (!this.customStartDate || !this.customEndDate) {
            alert('Please select both start and end dates for the custom period.');
            return;
        }

        try {
            const customMetrics = this.safeGetFinancialData('periodMetrics',
                this.customStartDate,
                this.customEndDate
            );

            const reportData = {
                period: 'custom',
                startDate: this.customStartDate,
                endDate: this.customEndDate,
                metrics: customMetrics,
                generatedAt: new Date().toISOString()
            };

            this.downloadReport(reportData, 'custom-period-report');
        } catch (error) {
            console.error('❌ Failed to generate custom period report:', error);
            alert('Failed to generate custom period report. Please try again.');
        }
    }

    /**
     * Open budget tracker
     */
    openBudgetTracker() {
        alert('Budget Tracker: Set monthly budgets and track spending against targets.');
    }

    /**
     * Manage cost alerts
     */
    manageCostAlerts() {
        alert('Cost Alerts: Configure automatic alerts for expense thresholds and budget limits.');
    }

    /**
     * Open expense forecast
     */
    openExpenseForecast() {
        alert('Expense Forecasting: Predict future expenses based on historical data and trends.');
    }

    /**
     * Open ROI calculator
     */
    openROICalculator() {
        alert('ROI Calculator: Calculate return on investment for business decisions.');
    }

    /**
     * Calculate ROI
     */
    calculateROI() {
        const investment = parseFloat(document.getElementById('investmentAmount')?.value || 0);
        const returns = parseFloat(document.getElementById('expectedReturn')?.value || 0);

        if (investment <= 0) {
            alert('Please enter a valid investment amount.');
            return;
        }

        const roi = ((returns - investment) / investment) * 100;
        const resultElement = document.getElementById('roiResult');

        if (resultElement) {
            resultElement.innerHTML = `
                <div class="roi-calculation">
                    <h4>ROI Calculation Result</h4>
                    <div class="roi-details">
                        <div class="roi-item">
                            <label>Investment:</label>
                            <span>PKR ${investment.toLocaleString()}</span>
                        </div>
                        <div class="roi-item">
                            <label>Expected Return:</label>
                            <span>PKR ${returns.toLocaleString()}</span>
                        </div>
                        <div class="roi-item">
                            <label>Net Profit:</label>
                            <span class="${roi >= 0 ? 'positive' : 'negative'}">
                                PKR ${(returns - investment).toLocaleString()}
                            </span>
                        </div>
                        <div class="roi-item total">
                            <label>ROI Percentage:</label>
                            <span class="${roi >= 0 ? 'positive' : 'negative'}">
                                ${roi.toFixed(2)}%
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Export methods
     */
    exportPDF() {
        alert('PDF Export: Generate comprehensive PDF reports with charts and analytics.');
    }

    exportExcel() {
        alert('Excel Export: Export data to Excel format for further analysis.');
    }

    exportCSV() {
        alert('CSV Export: Export raw data in CSV format for data processing.');
    }

    backupData() {
        try {
            const allData = {
                orders: this.zaiqaSystem ? this.zaiqaSystem.getData('orders') : [],
                expenses: this.zaiqaSystem ? this.zaiqaSystem.getData('expenses') : [],
                menuItems: this.zaiqaSystem ? this.zaiqaSystem.getData('menuItems') : [],
                staff: this.zaiqaSystem ? this.zaiqaSystem.getData('staff') : [],
                inventory: this.zaiqaSystem ? this.zaiqaSystem.getData('inventory') : [],
                udhars: this.zaiqaSystem ? this.zaiqaSystem.getData('udhars') : [],
                backupDate: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(allData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('Data backup completed successfully!');
        } catch (error) {
            console.error('❌ Failed to backup data:', error);
            alert('Failed to backup data. Please try again.');
        }
    }

    scheduleReports() {
        alert('Schedule Reports: Set up automatic report generation and email delivery.');
    }

    /**
     * Download report helper
     */
    downloadReport(reportData, filename) {
        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename}-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log(`✅ ${filename} downloaded successfully`);
    }

    /**
     * Initialize charts
     */
    initializeCharts() {
        // Charts will be implemented when Chart.js is available
        console.log('📊 Charts initialization placeholder');
    }
}

// Export for global use
window.ZaiqaReportsPage = ZaiqaReportsPage;
