/**
 * Zaiqa Restaurant Reports Page - Clean Architecture
 * Comprehensive Financial Analytics and Data Visualization
 * Built from scratch with accurate calculations
 */

class ZaiqaReportsPage {
    constructor(zaiqaSystem) {
        this.zaiqaSystem = zaiqaSystem;
        this.version = '2.0.0';
        this.currentPeriod = 'today';
        this.customStartDate = null;
        this.customEndDate = null;
        
        console.log('📊 Reports Page initialized v' + this.version);
    }

    /**
     * Render the complete reports page
     */
    render(pageElement) {
        try {
            console.log('📊 Rendering Reports Page...');
            
            // Get current financial data
            const todayMetrics = this.zaiqaSystem.getFinancialData('todayMetrics');
            const udharSummary = this.zaiqaSystem.getFinancialData('udharSummary');
            const inventoryValue = this.zaiqaSystem.getFinancialData('inventoryValue');
            
            pageElement.innerHTML = `
                <div class="reports-container">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="header-content">
                            <h1 class="page-title">
                                <i class="fas fa-chart-bar"></i>
                                Financial Reports & Analytics
                            </h1>
                            <p class="page-subtitle">Comprehensive business insights and financial analysis</p>
                        </div>
                        <div class="header-actions">
                            <button onclick="zaiqaReports.refreshReports()" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button onclick="zaiqaReports.exportReports()" class="btn btn-primary">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <!-- Period Selection -->
                    <div class="period-selection-card">
                        <h3>Select Report Period</h3>
                        <div class="period-buttons">
                            <button onclick="zaiqaReports.setPeriod('today')" 
                                    class="period-btn ${this.currentPeriod === 'today' ? 'active' : ''}">
                                Today
                            </button>
                            <button onclick="zaiqaReports.setPeriod('week')" 
                                    class="period-btn ${this.currentPeriod === 'week' ? 'active' : ''}">
                                This Week
                            </button>
                            <button onclick="zaiqaReports.setPeriod('month')" 
                                    class="period-btn ${this.currentPeriod === 'month' ? 'active' : ''}">
                                This Month
                            </button>
                            <button onclick="zaiqaReports.setPeriod('custom')" 
                                    class="period-btn ${this.currentPeriod === 'custom' ? 'active' : ''}">
                                Custom Period
                            </button>
                        </div>
                        
                        ${this.currentPeriod === 'custom' ? this.renderCustomDateInputs() : ''}
                    </div>

                    <!-- Key Performance Indicators -->
                    <div class="kpi-grid">
                        ${this.renderKPICards(todayMetrics, udharSummary, inventoryValue)}
                    </div>

                    <!-- Charts and Analytics -->
                    <div class="analytics-grid">
                        <!-- Revenue Chart -->
                        <div class="chart-card">
                            <h3>Revenue Trends</h3>
                            <div class="chart-container">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>

                        <!-- Expense Breakdown -->
                        <div class="chart-card">
                            <h3>Expense Breakdown</h3>
                            <div class="chart-container">
                                <canvas id="expenseChart"></canvas>
                            </div>
                        </div>

                        <!-- Top Items -->
                        <div class="chart-card">
                            <h3>Top Selling Items</h3>
                            <div id="topItemsList" class="top-items-list">
                                ${this.renderTopItems()}
                            </div>
                        </div>

                        <!-- Cash Flow -->
                        <div class="chart-card">
                            <h3>Cash Flow Analysis</h3>
                            <div id="cashFlowAnalysis">
                                ${this.renderCashFlowAnalysis(todayMetrics.cashFlow)}
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Tables -->
                    <div class="tables-section">
                        <!-- Recent Orders -->
                        <div class="table-card">
                            <h3>Recent Orders</h3>
                            <div class="table-container">
                                ${this.renderRecentOrdersTable()}
                            </div>
                        </div>

                        <!-- Expense Summary -->
                        <div class="table-card">
                            <h3>Expense Summary</h3>
                            <div class="table-container">
                                ${this.renderExpenseSummaryTable()}
                            </div>
                        </div>
                    </div>

                    <!-- Financial Summary -->
                    <div class="financial-summary-card">
                        <h3>Financial Summary</h3>
                        ${this.renderFinancialSummary(todayMetrics)}
                    </div>
                </div>
            `;

            // Initialize charts after DOM is updated
            setTimeout(() => {
                this.initializeCharts();
            }, 100);

        } catch (error) {
            console.error('❌ Failed to render reports page:', error);
            pageElement.innerHTML = `
                <div class="error-state">
                    <h2>Error Loading Reports</h2>
                    <p>Failed to load financial reports. Please try refreshing the page.</p>
                    <button onclick="zaiqaReports.render(document.getElementById('reportsPage'))" class="btn btn-primary">
                        Retry
                    </button>
                </div>
            `;
        }
    }

    /**
     * Render custom date inputs
     */
    renderCustomDateInputs() {
        const today = new Date().toISOString().split('T')[0];
        const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        return `
            <div class="custom-date-inputs">
                <div class="date-input-group">
                    <label>Start Date:</label>
                    <input type="date" 
                           id="customStartDate" 
                           value="${this.customStartDate || lastWeek}"
                           onchange="zaiqaReports.updateCustomDate('start', this.value)">
                </div>
                <div class="date-input-group">
                    <label>End Date:</label>
                    <input type="date" 
                           id="customEndDate" 
                           value="${this.customEndDate || today}"
                           onchange="zaiqaReports.updateCustomDate('end', this.value)">
                </div>
                <button onclick="zaiqaReports.generateCustomReport()" class="btn btn-primary">
                    Generate Report
                </button>
            </div>
        `;
    }

    /**
     * Render KPI cards
     */
    renderKPICards(todayMetrics, udharSummary, inventoryValue) {
        const revenue = todayMetrics.revenue || {};
        const expenses = todayMetrics.expenses || {};
        const profitLoss = todayMetrics.profitLoss || {};

        return `
            <div class="kpi-card revenue">
                <div class="kpi-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="kpi-content">
                    <h4>Total Revenue</h4>
                    <p class="kpi-value">PKR ${(revenue.totalRevenue || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${revenue.orderCount || 0} orders</span>
                </div>
            </div>

            <div class="kpi-card expenses">
                <div class="kpi-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="kpi-content">
                    <h4>Total Expenses</h4>
                    <p class="kpi-value">PKR ${(expenses.totalExpenses || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${expenses.expenseCount || 0} transactions</span>
                </div>
            </div>

            <div class="kpi-card profit ${profitLoss.isProfit ? 'positive' : 'negative'}">
                <div class="kpi-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="kpi-content">
                    <h4>Net Profit</h4>
                    <p class="kpi-value">PKR ${(profitLoss.grossProfit || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${(profitLoss.profitMargin || 0).toFixed(1)}% margin</span>
                </div>
            </div>

            <div class="kpi-card udhar">
                <div class="kpi-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="kpi-content">
                    <h4>Outstanding Udhar</h4>
                    <p class="kpi-value">PKR ${(udharSummary.totalRemainingAmount || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${udharSummary.activeCustomers || 0} customers</span>
                </div>
            </div>

            <div class="kpi-card inventory">
                <div class="kpi-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="kpi-content">
                    <h4>Inventory Value</h4>
                    <p class="kpi-value">PKR ${(inventoryValue.totalValue || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">${inventoryValue.totalItems || 0} items</span>
                </div>
            </div>

            <div class="kpi-card average">
                <div class="kpi-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="kpi-content">
                    <h4>Average Order</h4>
                    <p class="kpi-value">PKR ${(revenue.averageOrderValue || 0).toLocaleString()}</p>
                    <span class="kpi-subtitle">per order</span>
                </div>
            </div>
        `;
    }

    /**
     * Render top selling items
     */
    renderTopItems() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const topItems = this.zaiqaSystem.getFinancialData('topItems', startDate, endDate, 10);

            if (!topItems || topItems.length === 0) {
                return '<p class="no-data">No sales data available for this period</p>';
            }

            return topItems.map((item, index) => `
                <div class="top-item">
                    <div class="item-rank">${index + 1}</div>
                    <div class="item-details">
                        <h5>${item.name}</h5>
                        <p>Sold: ${item.quantity} | Revenue: PKR ${item.revenue.toLocaleString()}</p>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('❌ Failed to render top items:', error);
            return '<p class="error-message">Failed to load top items data</p>';
        }
    }

    /**
     * Render cash flow analysis
     */
    renderCashFlowAnalysis(cashFlow) {
        if (!cashFlow) {
            return '<p class="no-data">No cash flow data available</p>';
        }

        return `
            <div class="cash-flow-summary">
                <div class="cash-flow-item">
                    <label>Opening Balance:</label>
                    <span>PKR ${(cashFlow.openingBalance || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Day Revenue:</label>
                    <span class="positive">+PKR ${(cashFlow.dayRevenue || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Day Expenses:</label>
                    <span class="negative">-PKR ${(cashFlow.dayExpenses || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Expected Closing:</label>
                    <span>PKR ${(cashFlow.expectedClosing || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item">
                    <label>Actual Closing:</label>
                    <span>PKR ${(cashFlow.closingBalance || 0).toLocaleString()}</span>
                </div>
                <div class="cash-flow-item total">
                    <label>Cash Difference:</label>
                    <span class="${cashFlow.cashDifference >= 0 ? 'positive' : 'negative'}">
                        PKR ${(cashFlow.cashDifference || 0).toLocaleString()}
                    </span>
                </div>
            </div>
        `;
    }

    /**
     * Render recent orders table
     */
    renderRecentOrdersTable() {
        try {
            const orders = this.zaiqaSystem.getData('orders', { status: 'completed' })
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 10);

            if (orders.length === 0) {
                return '<p class="no-data">No recent orders found</p>';
            }

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Items</th>
                            <th>Amount</th>
                            <th>Payment</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map(order => `
                            <tr>
                                <td>${order.orderNumber}</td>
                                <td>${order.customerName || 'Walk-in'}</td>
                                <td>${order.items ? order.items.length : 0} items</td>
                                <td>PKR ${(order.totalAmount || 0).toLocaleString()}</td>
                                <td class="capitalize">${order.paymentMethod}</td>
                                <td>${new Date(order.createdAt).toLocaleDateString()}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render recent orders table:', error);
            return '<p class="error-message">Failed to load recent orders</p>';
        }
    }

    /**
     * Render expense summary table
     */
    renderExpenseSummaryTable() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const expenses = this.zaiqaSystem.getFinancialData('expenses', startDate, endDate);

            if (!expenses.expensesByCategory || Object.keys(expenses.expensesByCategory).length === 0) {
                return '<p class="no-data">No expenses found for this period</p>';
            }

            return `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Amount</th>
                            <th>Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(expenses.expensesByCategory).map(([category, amount]) => {
                            const percentage = expenses.totalExpenses > 0 ? 
                                ((amount / expenses.totalExpenses) * 100).toFixed(1) : 0;
                            return `
                                <tr>
                                    <td class="capitalize">${category}</td>
                                    <td>PKR ${amount.toLocaleString()}</td>
                                    <td>${percentage}%</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;

        } catch (error) {
            console.error('❌ Failed to render expense summary table:', error);
            return '<p class="error-message">Failed to load expense summary</p>';
        }
    }

    /**
     * Render financial summary
     */
    renderFinancialSummary(metrics) {
        const revenue = metrics.revenue || {};
        const expenses = metrics.expenses || {};
        const profitLoss = metrics.profitLoss || {};

        return `
            <div class="financial-summary-grid">
                <div class="summary-section">
                    <h4>Revenue Analysis</h4>
                    <div class="summary-items">
                        <div class="summary-item">
                            <span>Order Revenue:</span>
                            <span>PKR ${(revenue.orderRevenue || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Udhar Payments:</span>
                            <span>PKR ${(revenue.udharPayments || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Average Order Value:</span>
                            <span>PKR ${(revenue.averageOrderValue || 0).toLocaleString()}</span>
                        </div>
                    </div>
                </div>

                <div class="summary-section">
                    <h4>Expense Analysis</h4>
                    <div class="summary-items">
                        <div class="summary-item">
                            <span>Total Expenses:</span>
                            <span>PKR ${(expenses.totalExpenses || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Average Expense:</span>
                            <span>PKR ${(expenses.averageExpense || 0).toLocaleString()}</span>
                        </div>
                        <div class="summary-item">
                            <span>Expense Count:</span>
                            <span>${expenses.expenseCount || 0} transactions</span>
                        </div>
                    </div>
                </div>

                <div class="summary-section">
                    <h4>Profitability</h4>
                    <div class="summary-items">
                        <div class="summary-item">
                            <span>Gross Profit:</span>
                            <span class="${profitLoss.isProfit ? 'positive' : 'negative'}">
                                PKR ${(profitLoss.grossProfit || 0).toLocaleString()}
                            </span>
                        </div>
                        <div class="summary-item">
                            <span>Profit Margin:</span>
                            <span class="${profitLoss.isProfit ? 'positive' : 'negative'}">
                                ${(profitLoss.profitMargin || 0).toFixed(1)}%
                            </span>
                        </div>
                        <div class="summary-item">
                            <span>Status:</span>
                            <span class="${profitLoss.isProfit ? 'positive' : 'negative'}">
                                ${profitLoss.isProfit ? 'Profitable' : 'Loss'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get start date based on current period
     */
    getStartDate() {
        const today = new Date();
        
        switch (this.currentPeriod) {
            case 'today':
                return today.toISOString().split('T')[0];
            case 'week':
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                return weekStart.toISOString().split('T')[0];
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return monthStart.toISOString().split('T')[0];
            case 'custom':
                return this.customStartDate || today.toISOString().split('T')[0];
            default:
                return today.toISOString().split('T')[0];
        }
    }

    /**
     * Get end date based on current period
     */
    getEndDate() {
        const today = new Date();
        
        switch (this.currentPeriod) {
            case 'today':
                return today.toISOString().split('T')[0];
            case 'week':
                const weekEnd = new Date(today);
                weekEnd.setDate(today.getDate() + (6 - today.getDay()));
                return weekEnd.toISOString().split('T')[0];
            case 'month':
                const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                return monthEnd.toISOString().split('T')[0];
            case 'custom':
                return this.customEndDate || today.toISOString().split('T')[0];
            default:
                return today.toISOString().split('T')[0];
        }
    }

    /**
     * Set report period
     */
    setPeriod(period) {
        this.currentPeriod = period;
        this.refreshReports();
    }

    /**
     * Update custom date
     */
    updateCustomDate(type, value) {
        if (type === 'start') {
            this.customStartDate = value;
        } else {
            this.customEndDate = value;
        }
    }

    /**
     * Generate custom report
     */
    generateCustomReport() {
        if (!this.customStartDate || !this.customEndDate) {
            alert('Please select both start and end dates');
            return;
        }
        
        this.refreshReports();
    }

    /**
     * Refresh reports
     */
    refreshReports() {
        const reportsPage = document.getElementById('reportsPage');
        if (reportsPage) {
            this.render(reportsPage);
        }
    }

    /**
     * Export reports
     */
    exportReports() {
        try {
            const startDate = this.getStartDate();
            const endDate = this.getEndDate();
            const metrics = this.zaiqaSystem.getFinancialData('periodMetrics', startDate, endDate);
            
            const exportData = {
                period: this.currentPeriod,
                startDate: startDate,
                endDate: endDate,
                metrics: metrics,
                exportedAt: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `zaiqa-financial-report-${startDate}-to-${endDate}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log('✅ Reports exported successfully');

        } catch (error) {
            console.error('❌ Failed to export reports:', error);
            alert('Failed to export reports. Please try again.');
        }
    }

    /**
     * Initialize charts
     */
    initializeCharts() {
        // Charts will be implemented when Chart.js is available
        console.log('📊 Charts initialization placeholder');
    }
}

// Export for global use
window.ZaiqaReportsPage = ZaiqaReportsPage;
